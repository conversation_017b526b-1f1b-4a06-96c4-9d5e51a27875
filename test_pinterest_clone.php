<?php
/**
 * Test Pinterest Clone
 *
 * This script tests the PinterestClone class to ensure it works correctly.
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/PinterestClone.php';

try {
    // Create a new PinterestClone instance
    echo "Creating PinterestClone instance...\n";
    $pinterestClone = new PinterestClone();
    echo "PinterestClone instance created successfully.\n\n";

    // Test getRecentScrapes
    echo "Testing getRecentScrapes method...\n";
    $recentScrapes = $pinterestClone->getRecentScrapes(1);
    echo "Got " . count($recentScrapes) . " recent scrapes.\n";
    foreach ($recentScrapes as $index => $scrape) {
        echo "Scrape #" . ($index + 1) . ": " . $scrape['search_term'] . " (" . $scrape['status'] . ")\n";
    }
    echo "\n";

    // Test processScrape
    echo "Testing processScrape method...\n";
    $scrapeId = $pinterestClone->processScrape(1, ['search_term' => 'test search']);
    echo "Created new scrape with ID: $scrapeId\n\n";

    // Test getScrape
    echo "Testing getScrape method...\n";
    $scrape = $pinterestClone->getScrape($scrapeId, 1);
    echo "Got scrape: " . $scrape['search_term'] . " (" . $scrape['status'] . ")\n\n";

    // Test getPinsFromScrape
    echo "Testing getPinsFromScrape method...\n";
    $pins = $pinterestClone->getPinsFromScrape($scrapeId);
    echo "Got " . count($pins) . " pins from scrape.\n";
    echo "First pin title: " . $pins[0]['title'] . "\n\n";

    // Test in-progress scrape
    echo "Testing in-progress scrape...\n";
    echo "Scrape status: " . $scrape['status'] . "\n";
    echo "Waiting 5 seconds...\n";
    sleep(5);
    $scrape = $pinterestClone->getScrape($scrapeId, 1);
    echo "Scrape status after 5 seconds: " . $scrape['status'] . "\n";
    $pins = $pinterestClone->getPinsFromScrape($scrapeId);
    echo "Pin count after 5 seconds: " . count($pins) . "\n\n";

    // Wait for scrape to complete
    echo "Waiting for scrape to complete...\n";
    $complete = false;
    $attempts = 0;
    while (!$complete && $attempts < 30) {
        $scrape = $pinterestClone->getScrape($scrapeId, 1);
        if ($scrape['status'] === 'completed') {
            $complete = true;
        } else {
            echo "Scrape status: " . $scrape['status'] . ", pins: " . count($pinterestClone->getPinsFromScrape($scrapeId)) . "\n";
            sleep(5);
            $attempts++;
        }
    }

    if ($complete) {
        echo "Scrape completed successfully!\n";
        $pins = $pinterestClone->getPinsFromScrape($scrapeId);
        echo "Final pin count: " . count($pins) . "\n";
    } else {
        echo "Scrape did not complete within the timeout period.\n";
    }

    echo "\nTest completed successfully!\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
