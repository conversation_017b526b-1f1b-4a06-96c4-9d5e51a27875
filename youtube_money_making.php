<?php
/**
 * YouTube Money Making Techniques Browser
 *
 * This script implements a specialized YouTube browsing functionality for finding
 * and analyzing money-making techniques and opportunities.
 */

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';

// Initialize models
$db = Database::getInstance();
$agentModel = new AIAgent();
$taskModel = new AIAgentTask();
$interactionModel = new AIAgentInteraction();

// Check database connection
if (!$db) {
    die("Database connection failed");
}

// Get the YouTube Browser agent
$agents = $agentModel->getUserAgents(1);
$youtubeAgentId = null;

foreach ($agents as $agent) {
    if ($agent['name'] === 'YouTube Browser') {
        $youtubeAgentId = $agent['id'];
        break;
    }
}

if (!$youtubeAgentId) {
    // Try to create the agent
    include_once 'create_youtube_agent.php';

    // Check again
    $agents = $agentModel->getUserAgents(1);
    foreach ($agents as $agent) {
        if ($agent['name'] === 'YouTube Browser') {
            $youtubeAgentId = $agent['id'];
            break;
        }
    }

    if (!$youtubeAgentId) {
        die("YouTube Browser agent not found. Please run create_youtube_agent.php first.");
    }
}

// Get search parameters from POST or use defaults
$searchTopic = isset($_POST['search_topic']) ? $_POST['search_topic'] : 'passive income OR make money online OR side hustle';
$maxResults = isset($_POST['max_results']) ? intval($_POST['max_results']) : 10;
$daysAgo = isset($_POST['days_ago']) ? intval($_POST['days_ago']) : 30;
$minViews = isset($_POST['min_views']) ? intval($_POST['min_views']) : 1000;
$apiKey = isset($_POST['api_key']) ? $_POST['api_key'] : '';

// Create a task for this search
$taskTitle = "Find YouTube videos about {$searchTopic} from the last {$daysAgo} days";
$taskDescription = "Search YouTube for videos about {$searchTopic} that were published in the last {$daysAgo} days. " .
                  "Focus on videos with at least {$minViews} views. Collect the links, titles, channel names, and " .
                  "publication dates. Analyze the content for actionable money-making techniques.";

$taskId = $taskModel->createTask([
    'agent_id' => $youtubeAgentId,
    'user_id' => 1,
    'title' => $taskTitle,
    'description' => $taskDescription,
    'priority' => 'high',
    'status' => 'in_progress',
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
]);

// Log the start of the task
$interactionModel->createInteraction([
    'agent_id' => $youtubeAgentId,
    'user_id' => 1,
    'interaction_type' => 'system',
    'content' => "Starting task: {$taskTitle}",
    'created_at' => date('Y-m-d H:i:s')
]);

/**
 * Function to search YouTube using the YouTube Data API
 */
function searchYouTube($query, $maxResults = 10, $publishedAfter = null, $apiKey = '', $minViews = 1000) {
    // If API key is provided, use the real YouTube API
    if (!empty($apiKey)) {
        // Base URL for YouTube Data API v3 search endpoint
        $baseUrl = 'https://www.googleapis.com/youtube/v3/search';

        // Parameters for the search request
        $params = [
            'part' => 'snippet',
            'q' => $query,
            'maxResults' => $maxResults,
            'type' => 'video',
            'key' => $apiKey,
            'order' => 'relevance',
            'relevanceLanguage' => 'en'
        ];

        // Add publishedAfter parameter if provided
        if ($publishedAfter) {
            $params['publishedAfter'] = $publishedAfter;
        }

        // Build the URL
        $url = $baseUrl . '?' . http_build_query($params);

        // Make the request
        $response = file_get_contents($url);

        if ($response) {
            $data = json_decode($response, true);
            $videos = [];

            if (isset($data['items']) && is_array($data['items'])) {
                foreach ($data['items'] as $item) {
                    // Get video details
                    $videoId = $item['id']['videoId'];
                    $videoDetailsUrl = "https://www.googleapis.com/youtube/v3/videos?part=snippet,statistics&id={$videoId}&key={$apiKey}";
                    $videoDetailsResponse = file_get_contents($videoDetailsUrl);
                    $videoDetails = json_decode($videoDetailsResponse, true);

                    if (isset($videoDetails['items'][0])) {
                        $details = $videoDetails['items'][0];
                        $viewCount = isset($details['statistics']['viewCount']) ? intval($details['statistics']['viewCount']) : 0;

                        // Skip videos with fewer views than the minimum
                        if ($viewCount < $minViews) {
                            continue;
                        }

                        $videos[] = [
                            'id' => $videoId,
                            'title' => $details['snippet']['title'],
                            'channelTitle' => $details['snippet']['channelTitle'],
                            'publishedAt' => $details['snippet']['publishedAt'],
                            'description' => $details['snippet']['description'],
                            'viewCount' => $viewCount,
                            'likeCount' => isset($details['statistics']['likeCount']) ? intval($details['statistics']['likeCount']) : 0,
                            'url' => "https://www.youtube.com/watch?v={$videoId}"
                        ];
                    }
                }
            }

            return $videos;
        }
    }

    // If API key is not provided or API call fails, use sample data
    // This is a fallback for development/testing
    $threeDaysAgo = date('Y-m-d\TH:i:s\Z', strtotime("-{$daysAgo} days"));

    $sampleVideos = [
        [
            'id' => 'video1',
            'title' => '10 Passive Income Ideas for 2024 (Earn $1000+ Per Month)',
            'channelTitle' => 'Financial Freedom',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-2 days')),
            'description' => 'Discover 10 proven passive income ideas that can help you earn over $1000 per month with minimal ongoing effort.',
            'viewCount' => 45000,
            'likeCount' => 3200,
            'url' => 'https://www.youtube.com/watch?v=sample1'
        ],
        [
            'id' => 'video2',
            'title' => 'How I Made $10,000 in 30 Days with AI Tools',
            'channelTitle' => 'AI Entrepreneur',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-5 days')),
            'description' => 'In this video, I break down exactly how I used AI tools to generate $10,000 in just 30 days, with step-by-step instructions.',
            'viewCount' => 78000,
            'likeCount' => 5600,
            'url' => 'https://www.youtube.com/watch?v=sample2'
        ],
        [
            'id' => 'video3',
            'title' => 'The 5 Best Side Hustles for Beginners (No Experience Needed)',
            'channelTitle' => 'Side Hustle Pro',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-7 days')),
            'description' => 'These 5 side hustles require no prior experience and can be started with minimal investment. Perfect for beginners looking to make extra income.',
            'viewCount' => 62000,
            'likeCount' => 4100,
            'url' => 'https://www.youtube.com/watch?v=sample3'
        ],
        [
            'id' => 'video4',
            'title' => 'How to Make $100 Per Day with Affiliate Marketing',
            'channelTitle' => 'Affiliate Success',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-10 days')),
            'description' => 'Learn the exact strategy I use to consistently make $100+ per day with affiliate marketing, even as a complete beginner.',
            'viewCount' => 35000,
            'likeCount' => 2800,
            'url' => 'https://www.youtube.com/watch?v=sample4'
        ],
        [
            'id' => 'video5',
            'title' => 'Print on Demand in 2024: Is It Still Profitable?',
            'channelTitle' => 'E-Commerce Experts',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-12 days')),
            'description' => 'An honest look at the print on demand business model in 2024. I share my actual numbers and whether it\'s still worth starting.',
            'viewCount' => 42000,
            'likeCount' => 3100,
            'url' => 'https://www.youtube.com/watch?v=sample5'
        ],
        [
            'id' => 'video6',
            'title' => 'How to Start a Profitable YouTube Channel in 2024',
            'channelTitle' => 'YouTube Growth Strategies',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-15 days')),
            'description' => 'A complete guide to starting and monetizing a YouTube channel in 2024, including the best niches, equipment, and monetization strategies.',
            'viewCount' => 55000,
            'likeCount' => 4300,
            'url' => 'https://www.youtube.com/watch?v=sample6'
        ],
        [
            'id' => 'video7',
            'title' => 'I Tested 7 AI Side Hustles and Made $3,427',
            'channelTitle' => 'AI Money Maker',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-18 days')),
            'description' => 'I spent one month testing 7 different AI side hustles. Here are my results, including which ones were most profitable and why.',
            'viewCount' => 89000,
            'likeCount' => 6700,
            'url' => 'https://www.youtube.com/watch?v=sample7'
        ],
        [
            'id' => 'video8',
            'title' => 'Digital Product Creation: From Idea to $5K/Month',
            'channelTitle' => 'Digital Product Mastery',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-21 days')),
            'description' => 'How to create and sell digital products that generate $5,000+ per month in passive income. Includes product ideas, creation tools, and marketing strategies.',
            'viewCount' => 37000,
            'likeCount' => 2900,
            'url' => 'https://www.youtube.com/watch?v=sample8'
        ],
        [
            'id' => 'video9',
            'title' => 'Freelancing in 2024: The Highest Paying Skills to Learn',
            'channelTitle' => 'Freelance Freedom',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-25 days')),
            'description' => 'These are the top 10 highest-paying freelance skills in 2024, with details on how to learn them quickly and find high-paying clients.',
            'viewCount' => 51000,
            'likeCount' => 3800,
            'url' => 'https://www.youtube.com/watch?v=sample9'
        ],
        [
            'id' => 'video10',
            'title' => 'How I Built a $10K/Month Automated Business with AI',
            'channelTitle' => 'AI Business Builder',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-28 days')),
            'description' => 'The complete blueprint for building an automated business using AI tools that generates $10,000+ per month with minimal time investment.',
            'viewCount' => 95000,
            'likeCount' => 7200,
            'url' => 'https://www.youtube.com/watch?v=sample10'
        ],
        [
            'id' => 'video11',
            'title' => 'Amazon KDP: Is Low Content Publishing Still Profitable?',
            'channelTitle' => 'KDP Success',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-14 days')),
            'description' => 'An honest review of Amazon KDP low content publishing in 2024. I share my actual earnings and whether beginners can still make money with this method.',
            'viewCount' => 41000,
            'likeCount' => 3000,
            'url' => 'https://www.youtube.com/watch?v=sample11'
        ],
        [
            'id' => 'video12',
            'title' => 'How to Make Money with ChatGPT: 12 Profitable Methods',
            'channelTitle' => 'AI Income Strategies',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-9 days')),
            'description' => 'Discover 12 proven ways to make money using ChatGPT and other AI tools, from content creation to specialized AI services.',
            'viewCount' => 82000,
            'likeCount' => 6100,
            'url' => 'https://www.youtube.com/watch?v=sample12'
        ],
        [
            'id' => 'video13',
            'title' => 'Dropshipping in 2024: Dead or Still Viable?',
            'channelTitle' => 'E-Commerce Mastery',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-16 days')),
            'description' => 'Is dropshipping still a viable business model in 2024? I analyze the current state of dropshipping and share strategies that still work.',
            'viewCount' => 47000,
            'likeCount' => 3400,
            'url' => 'https://www.youtube.com/watch?v=sample13'
        ],
        [
            'id' => 'video14',
            'title' => 'How to Start a Profitable Blog in 2024 (Step by Step)',
            'channelTitle' => 'Blogging Income',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-22 days')),
            'description' => 'A complete step-by-step guide to starting a profitable blog in 2024, including niche selection, content strategy, and monetization methods.',
            'viewCount' => 39000,
            'likeCount' => 2800,
            'url' => 'https://www.youtube.com/watch?v=sample14'
        ],
        [
            'id' => 'video15',
            'title' => 'The Truth About Making Money on Fiverr in 2024',
            'channelTitle' => 'Freelance Success',
            'publishedAt' => date('Y-m-d\TH:i:s\Z', strtotime('-19 days')),
            'description' => 'An honest look at making money on Fiverr in 2024. I share my journey from $0 to $5,000/month and whether it\'s still worth starting.',
            'viewCount' => 58000,
            'likeCount' => 4200,
            'url' => 'https://www.youtube.com/watch?v=sample15'
        ]
    ];

    // Filter videos by publish date if needed
    if ($publishedAfter) {
        $sampleVideos = array_filter($sampleVideos, function($video) use ($publishedAfter) {
            return strtotime($video['publishedAt']) >= strtotime($publishedAfter);
        });
    }

    // Filter by minimum views
    $sampleVideos = array_filter($sampleVideos, function($video) use ($minViews) {
        return $video['viewCount'] >= $minViews;
    });

    // Limit results
    $sampleVideos = array_slice($sampleVideos, 0, $maxResults);

    return $sampleVideos;
}

/**
 * Function to analyze money-making techniques from video data
 */
function analyzeMoneyMakingTechniques($videos) {
    $techniques = [];
    $categories = [
        'passive_income' => [
            'name' => 'Passive Income',
            'keywords' => ['passive income', 'earn while you sleep', 'automated income', 'recurring revenue'],
            'techniques' => []
        ],
        'ai_opportunities' => [
            'name' => 'AI-Based Opportunities',
            'keywords' => ['ai', 'artificial intelligence', 'chatgpt', 'machine learning', 'automation'],
            'techniques' => []
        ],
        'freelancing' => [
            'name' => 'Freelancing & Services',
            'keywords' => ['freelance', 'freelancing', 'service', 'client', 'gig', 'fiverr', 'upwork'],
            'techniques' => []
        ],
        'ecommerce' => [
            'name' => 'E-Commerce',
            'keywords' => ['ecommerce', 'e-commerce', 'dropshipping', 'print on demand', 'pod', 'amazon', 'etsy', 'shopify'],
            'techniques' => []
        ],
        'content_creation' => [
            'name' => 'Content Creation',
            'keywords' => ['youtube', 'blog', 'podcast', 'content creator', 'influencer', 'social media'],
            'techniques' => []
        ],
        'digital_products' => [
            'name' => 'Digital Products',
            'keywords' => ['digital product', 'ebook', 'course', 'template', 'kdp', 'kindle'],
            'techniques' => []
        ],
        'other' => [
            'name' => 'Other Opportunities',
            'keywords' => [],
            'techniques' => []
        ]
    ];

    foreach ($videos as $video) {
        $title = strtolower($video['title']);
        $description = strtolower($video['description']);
        $content = $title . ' ' . $description;

        // Extract potential money-making technique from the title
        $technique = [
            'title' => $video['title'],
            'source' => $video['channelTitle'],
            'url' => $video['url'],
            'views' => $video['viewCount'],
            'likes' => $video['likeCount'],
            'publishedAt' => $video['publishedAt']
        ];

        // Categorize the technique
        $categorized = false;
        foreach ($categories as $key => &$category) {
            if ($key === 'other') continue; // Skip 'other' for now

            foreach ($category['keywords'] as $keyword) {
                if (strpos($content, $keyword) !== false) {
                    $category['techniques'][] = $technique;
                    $categorized = true;
                    break 2; // Break out of both loops once categorized
                }
            }
        }

        // If not categorized, put in 'other'
        if (!$categorized) {
            $categories['other']['techniques'][] = $technique;
        }
    }

    // Remove empty categories
    foreach ($categories as $key => $category) {
        if (empty($category['techniques'])) {
            unset($categories[$key]);
        }
    }

    return $categories;
}

// Search for videos
$daysAgoDate = date('Y-m-d\TH:i:s\Z', strtotime("-{$daysAgo} days"));
$searchResults = searchYouTube($searchTopic, $maxResults, $daysAgoDate, $apiKey, $minViews);

// Analyze the results for money-making techniques
$analyzedTechniques = analyzeMoneyMakingTechniques($searchResults);

// Format the results
$formattedResults = "# Money-Making Techniques from YouTube\n\n";
$formattedResults .= "Search completed on: " . date('Y-m-d H:i:s') . "\n";
$formattedResults .= "Search topic: {$searchTopic}\n";
$formattedResults .= "Time period: Last {$daysAgo} days\n";
$formattedResults .= "Minimum views: {$minViews}\n\n";

if (empty($searchResults)) {
    $formattedResults .= "No videos found matching the criteria.\n";
} else {
    $formattedResults .= "Found " . count($searchResults) . " videos with potential money-making techniques.\n\n";

    $formattedResults .= "## Money-Making Opportunities by Category\n\n";

    foreach ($analyzedTechniques as $category) {
        $formattedResults .= "### " . $category['name'] . " (" . count($category['techniques']) . " techniques)\n\n";

        foreach ($category['techniques'] as $index => $technique) {
            $formattedResults .= ($index + 1) . ". **" . $technique['title'] . "**\n";
            $formattedResults .= "   - **Channel:** " . $technique['source'] . "\n";
            $formattedResults .= "   - **Published:** " . date('Y-m-d', strtotime($technique['publishedAt'])) . "\n";
            $formattedResults .= "   - **Views:** " . number_format($technique['views']) . "\n";
            $formattedResults .= "   - **Likes:** " . number_format($technique['likes']) . "\n";
            $formattedResults .= "   - **URL:** [Watch Video](" . $technique['url'] . ")\n\n";
        }
    }

    $formattedResults .= "## Top 5 Most Viewed Videos\n\n";

    // Sort videos by view count
    usort($searchResults, function($a, $b) {
        return $b['viewCount'] - $a['viewCount'];
    });

    // Get top 5 videos
    $topVideos = array_slice($searchResults, 0, 5);

    foreach ($topVideos as $index => $video) {
        $formattedResults .= ($index + 1) . ". **" . $video['title'] . "**\n";
        $formattedResults .= "   - **Channel:** " . $video['channelTitle'] . "\n";
        $formattedResults .= "   - **Views:** " . number_format($video['viewCount']) . "\n";
        $formattedResults .= "   - **Likes:** " . number_format($video['likeCount']) . "\n";
        $formattedResults .= "   - **URL:** [Watch Video](" . $video['url'] . ")\n\n";
    }

    $formattedResults .= "## Recommended Action Plan\n\n";
    $formattedResults .= "Based on the analyzed videos, here are the top money-making opportunities to consider:\n\n";

    // Get the top category by number of videos
    usort($analyzedTechniques, function($a, $b) {
        return count($b['techniques']) - count($a['techniques']);
    });

    $topCategory = reset($analyzedTechniques);

    $formattedResults .= "1. **Focus Area:** " . $topCategory['name'] . "\n";
    $formattedResults .= "   - This category has the most trending content, suggesting high current interest and potential opportunity.\n\n";

    // Get the most viewed video overall
    $mostViewedVideo = $topVideos[0];

    $formattedResults .= "2. **Research Priority:** " . $mostViewedVideo['title'] . "\n";
    $formattedResults .= "   - With " . number_format($mostViewedVideo['viewCount']) . " views, this represents a highly popular topic worth investigating further.\n\n";

    $formattedResults .= "3. **Next Steps:**\n";
    $formattedResults .= "   - Watch the top 5 videos and take detailed notes on the specific techniques mentioned\n";
    $formattedResults .= "   - Identify common themes and strategies across multiple videos\n";
    $formattedResults .= "   - Research the viability and startup costs for the most promising opportunities\n";
    $formattedResults .= "   - Select one technique to implement as a test case\n";
    $formattedResults .= "   - Create a detailed implementation plan with timeline and success metrics\n";
}

// Save the results as an interaction
$interactionId = $interactionModel->createInteraction([
    'agent_id' => $youtubeAgentId,
    'user_id' => 1,
    'interaction_type' => 'system',
    'content' => "Completed task: {$taskTitle}",
    'response' => $formattedResults,
    'success' => true,
    'created_at' => date('Y-m-d H:i:s')
]);

// Update task status
$taskModel->updateTask($taskId, [
    'status' => 'completed',
    'updated_at' => date('Y-m-d H:i:s')
]);

// Update agent last active time
$agentModel->updateLastActive($youtubeAgentId);

// Output the results
echo "<h1>YouTube Money Making Techniques Analysis</h1>";
echo "<pre>" . htmlspecialchars($formattedResults) . "</pre>";
echo "<p>Results have been saved to the agent's interactions.</p>";
echo "<p><a href='/momentum/ai-agents/view/{$youtubeAgentId}'>View Agent</a> | <a href='/momentum/ai-agents'>AI Agents Dashboard</a></p>";
