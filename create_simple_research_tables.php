<?php
/**
 * Create Simple Research Tables
 */

error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'src/utils/Database.php';

try {
    $db = Database::getInstance();
    echo "Connected to database successfully.\n\n";

    // Create research_sessions table
    echo "Creating research_sessions table...\n";
    $sql1 = "CREATE TABLE IF NOT EXISTS research_sessions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        research_type VARCHAR(50) DEFAULT 'other',
        status VARCHAR(20) DEFAULT 'active',
        priority VARCHAR(20) DEFAULT 'medium',
        tags TEXT,
        metadata TEXT,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        completed_at DATETIME NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if ($db->query($sql1)) {
        echo "✓ research_sessions table created\n";
    } else {
        echo "✗ Failed to create research_sessions table\n";
    }

    // Create research_links table
    echo "Creating research_links table...\n";
    $sql2 = "CREATE TABLE IF NOT EXISTS research_links (
        id INT AUTO_INCREMENT PRIMARY KEY,
        research_session_id INT NOT NULL,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        url TEXT NOT NULL,
        description TEXT,
        link_type VARCHAR(50) DEFAULT 'other',
        importance VARCHAR(20) DEFAULT 'medium',
        status VARCHAR(20) DEFAULT 'to_review',
        notes TEXT,
        tags TEXT,
        metadata TEXT,
        access_count INT DEFAULT 0,
        last_accessed DATETIME NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if ($db->query($sql2)) {
        echo "✓ research_links table created\n";
    } else {
        echo "✗ Failed to create research_links table\n";
    }

    // Create research_notes table
    echo "Creating research_notes table...\n";
    $sql3 = "CREATE TABLE IF NOT EXISTS research_notes (
        id INT AUTO_INCREMENT PRIMARY KEY,
        research_session_id INT NOT NULL,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        content TEXT NOT NULL,
        note_type VARCHAR(50) DEFAULT 'finding',
        importance VARCHAR(20) DEFAULT 'medium',
        tags TEXT,
        linked_links TEXT,
        metadata TEXT,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if ($db->query($sql3)) {
        echo "✓ research_notes table created\n";
    } else {
        echo "✗ Failed to create research_notes table\n";
    }

    // Create research_plans table
    echo "Creating research_plans table...\n";
    $sql4 = "CREATE TABLE IF NOT EXISTS research_plans (
        id INT AUTO_INCREMENT PRIMARY KEY,
        research_session_id INT NOT NULL,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        plan_type VARCHAR(50) DEFAULT 'implementation',
        priority VARCHAR(20) DEFAULT 'medium',
        estimated_effort VARCHAR(20) DEFAULT 'medium',
        estimated_duration_days INT DEFAULT NULL,
        success_criteria TEXT,
        risks_and_mitigation TEXT,
        resources_needed TEXT,
        status VARCHAR(20) DEFAULT 'draft',
        progress INT DEFAULT 0,
        tags TEXT,
        metadata TEXT,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if ($db->query($sql4)) {
        echo "✓ research_plans table created\n";
    } else {
        echo "✗ Failed to create research_plans table\n";
    }

    // Create plan_action_items table
    echo "Creating plan_action_items table...\n";
    $sql5 = "CREATE TABLE IF NOT EXISTS plan_action_items (
        id INT AUTO_INCREMENT PRIMARY KEY,
        research_plan_id INT NOT NULL,
        user_id INT NOT NULL,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        action_type VARCHAR(50) DEFAULT 'other',
        priority VARCHAR(20) DEFAULT 'medium',
        estimated_time_minutes INT DEFAULT NULL,
        dependencies TEXT,
        status VARCHAR(20) DEFAULT 'todo',
        assigned_to INT NULL,
        due_date DATE NULL,
        notes TEXT,
        completion_notes TEXT,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        completed_at DATETIME NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if ($db->query($sql5)) {
        echo "✓ plan_action_items table created\n";
    } else {
        echo "✗ Failed to create plan_action_items table\n";
    }

    // Create research_project_conversions table
    echo "Creating research_project_conversions table...\n";
    $sql6 = "CREATE TABLE IF NOT EXISTS research_project_conversions (
        id INT AUTO_INCREMENT PRIMARY KEY,
        research_session_id INT NOT NULL,
        research_plan_id INT NULL,
        project_id INT NOT NULL,
        user_id INT NOT NULL,
        conversion_type VARCHAR(50) DEFAULT 'full_project',
        conversion_notes TEXT,
        success_metrics TEXT,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4";

    if ($db->query($sql6)) {
        echo "✓ research_project_conversions table created\n";
    } else {
        echo "✗ Failed to create research_project_conversions table\n";
    }

    // Add some sample data
    echo "\nAdding sample data...\n";
    
    // Sample research session
    $sessionSql = "INSERT INTO research_sessions (user_id, title, description, research_type, status, priority, tags, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
    
    $sessionData = [
        1, // user_id
        'AI Agent Army Market Research',
        'Research into AI agent platforms, competitors, and market opportunities for building our own platform',
        'market_analysis',
        'active',
        'high',
        '["ai", "agents", "market-research", "competition"]',
        '{"created_by": "system", "sample_data": true}',
        date('Y-m-d H:i:s'),
        date('Y-m-d H:i:s')
    ];
    
    $sessionId = $db->insert($sessionSql, $sessionData);
    
    if ($sessionId) {
        echo "✓ Sample research session created (ID: $sessionId)\n";
        
        // Sample links
        $linkSql = "INSERT INTO research_links (research_session_id, user_id, title, url, description, link_type, importance, status, notes, tags, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $sampleLinks = [
            [
                $sessionId, 1,
                'CrewAI - Multi-Agent Framework',
                'https://www.crewai.com/',
                'Leading multi-agent platform for business automation',
                'competitor', 'high', 'to_review', '',
                '["competitor", "multi-agent"]',
                '{"sample_data": true}',
                date('Y-m-d H:i:s'), date('Y-m-d H:i:s')
            ],
            [
                $sessionId, 1,
                'Microsoft AutoGen Documentation',
                'https://microsoft.github.io/autogen/',
                'Microsoft\'s conversational AI agent framework',
                'documentation', 'high', 'to_review', '',
                '["microsoft", "documentation"]',
                '{"sample_data": true}',
                date('Y-m-d H:i:s'), date('Y-m-d H:i:s')
            ]
        ];
        
        foreach ($sampleLinks as $linkData) {
            $linkId = $db->insert($linkSql, $linkData);
            if ($linkId) {
                echo "✓ Sample link created: " . $linkData[2] . "\n";
            }
        }
        
        // Sample plan
        $planSql = "INSERT INTO research_plans (research_session_id, user_id, title, description, plan_type, priority, estimated_effort, estimated_duration_days, success_criteria, status, progress, tags, metadata, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $planData = [
            $sessionId, 1,
            'AI Agent Army Implementation Plan',
            'Comprehensive plan to implement competitive AI agent platform with ADHD-friendly features',
            'implementation',
            'high',
            'large',
            90,
            'Launch MVP with core features, acquire first 100 users, generate $10k MRR',
            'draft',
            0,
            '["implementation", "mvp", "ai-agents"]',
            '{"sample_data": true}',
            date('Y-m-d H:i:s'),
            date('Y-m-d H:i:s')
        ];
        
        $planId = $db->insert($planSql, $planData);
        
        if ($planId) {
            echo "✓ Sample research plan created (ID: $planId)\n";
        }
    }

    echo "\n" . str_repeat("=", 50) . "\n";
    echo "Research & Planning System Setup Complete!\n";
    echo str_repeat("=", 50) . "\n\n";

    echo "✅ Database tables created successfully!\n";
    echo "✅ Sample data added for testing\n";
    echo "✅ System ready for integration\n\n";

} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
