# ADHD-Friendly UI Guidelines for Momentum

This document outlines the guidelines and best practices for implementing ADHD-friendly UI features in the Momentum application. These guidelines are designed to reduce cognitive load, improve focus, and enhance the overall user experience for individuals with ADHD.

## Table of Contents

1. [Color Coding](#color-coding)
2. [Visual Cues](#visual-cues)
3. [Focus States](#focus-states)
4. [Progress Indicators](#progress-indicators)
5. [Keyboard Shortcuts](#keyboard-shortcuts)
6. [Focus Mode](#focus-mode)
7. [Reduced Visual Noise](#reduced-visual-noise)
8. [Accessibility Improvements](#accessibility-improvements)
9. [Implementation Guide](#implementation-guide)

## Color Coding

Color coding helps users quickly identify and categorize information without having to read and process text.

### Priority Levels

- **High Priority**: Red border-left (`priority-high` class)
- **Normal Priority**: Blue border-left (`priority-normal` class)
- **Low Priority**: Gray border-left (`priority-low` class)

### Status Indicators

- **Overdue**: Red background (`status-overdue` class)
- **Due Today**: Yellow background (`status-due-today` class)
- **Upcoming**: Blue background (`status-upcoming` class)
- **Completed**: Green background (`status-completed` class)

### Category Color Coding

Each category has its own color for quick visual identification:

- **Medication**: Pink (`category-medication` class)
- **Vaccination**: Purple (`category-vaccination` class)
- **Vet Visit**: Cyan (`category-vet-visit` class)
- **Grooming**: Teal (`category-grooming` class)
- **Training**: Amber (`category-training` class)
- **Feeding**: Lime (`category-feeding` class)
- **Exercise**: Red (`category-exercise` class)
- **Other**: Gray (`category-other` class)

## Visual Cues

Visual cues help draw attention to important elements and provide feedback on interactions.

### Icon Color Coding

Icons match their category colors for consistent visual identification:

- **Medication**: `<i class="fas fa-pills icon-medication"></i>`
- **Vaccination**: `<i class="fas fa-syringe icon-vaccination"></i>`
- **Vet Visit**: `<i class="fas fa-stethoscope icon-vet-visit"></i>`
- **Grooming**: `<i class="fas fa-cut icon-grooming"></i>`
- **Training**: `<i class="fas fa-graduation-cap icon-training"></i>`
- **Feeding**: `<i class="fas fa-utensils icon-feeding"></i>`
- **Exercise**: `<i class="fas fa-running icon-exercise"></i>`
- **Other**: `<i class="fas fa-bell icon-other"></i>`

### Pulse Animation

Use pulse animation to draw attention to important elements:

```html
<button class="pulse-animation">Important Action</button>
```

### Card Styles

Use card styles to create visual separation between content sections:

```html
<div class="adhd-card">Content goes here</div>
```

## Focus States

Enhanced focus states help users track their current position in the interface.

### Focus Border

```html
<input class="adhd-focus-border" type="text">
```

### Focus Glow

```html
<button class="adhd-focus-glow">Click Me</button>
```

## Progress Indicators

Progress indicators provide visual feedback on task completion and time remaining.

### Basic Progress Bar

```html
<div class="progress-container" aria-label="Progress">
    <div class="progress-bar progress-bar-info" style="width: 50%" data-percentage="50" data-type="info"></div>
</div>
```

### Progress Bar Types

- **Success**: Green (`progress-bar-success` class)
- **Warning**: Yellow (`progress-bar-warning` class)
- **Danger**: Red (`progress-bar-danger` class)
- **Info**: Blue (`progress-bar-info` class)

## Keyboard Shortcuts

Keyboard shortcuts reduce the need for mouse movements and provide alternative ways to interact with the interface.

### Shortcut Indicators

```html
<button data-keyboard-shortcut="S">Save</button>
```

### Keyboard Shortcut Help

```html
<div class="keyboard-shortcut">S</div> Save
```

## Focus Mode

Focus mode reduces distractions by dimming non-essential elements.

### Implementing Focus Mode

```html
<body class="focus-mode-active">
    <div class="focus-mode-hide">This will be dimmed in focus mode</div>
    <div class="focus-mode-show">This will be highlighted in focus mode</div>
</body>
```

### Focus Mode Toggle

```html
<button id="focus-mode-toggle">Enter Focus Mode</button>
```

## Reduced Visual Noise

Reducing visual noise helps minimize distractions and cognitive load.

### Implementing Reduced Noise

```html
<div class="reduced-noise">Content with reduced visual noise</div>
```

## Accessibility Improvements

Accessibility improvements help ensure the interface is usable by everyone.

### High Contrast Text

```html
<h1 class="high-contrast-text">Heading with high contrast</h1>
```

### Increased Spacing

```html
<p class="increased-spacing">Text with increased spacing for better readability</p>
```

## Implementation Guide

### 1. Include the CSS and JavaScript Files

```html
<link rel="stylesheet" href="/momentum/assets/css/adhd-friendly.css">
<script src="/momentum/assets/js/adhd-friendly.js"></script>
```

### 2. Add Data Attributes to Elements

```html
<div data-priority="high" data-category="medication" data-status="overdue">
    Content goes here
</div>
```

### 3. Initialize ADHD-Friendly Features

The JavaScript will automatically initialize all ADHD-friendly features when the page loads.

### 4. Add Focus Mode Toggle

```html
<div class="fixed bottom-4 right-4 z-50">
    <button id="focus-mode-toggle" class="bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-full shadow-lg transition-colors duration-200 flex items-center">
        <i class="fas fa-eye mr-2"></i> Enter Focus Mode
    </button>
</div>
```

### 5. Use Progress Indicators for Time-Sensitive Information

```html
<div class="progress-container" id="progress-1" aria-label="Time remaining">
    <div class="progress-bar progress-bar-warning" style="width: 75%" data-percentage="75" data-type="warning"></div>
</div>
```

### 6. Update Progress Dynamically

```javascript
updateProgress('progress-1', 80, 'success');
```

### 7. Toggle Focus on Specific Elements

```javascript
toggleElementFocus('element-id');
```

### 8. Apply Color Coding Dynamically

```javascript
applyColorCoding('element-id', 'priority', 'high');
```

## Best Practices

1. **Be Consistent**: Use color coding and visual cues consistently throughout the application.
2. **Provide Multiple Cues**: Combine color, icons, and text to convey information.
3. **Respect User Preferences**: Honor the user's preference for reduced motion.
4. **Maintain Accessibility**: Ensure all ADHD-friendly enhancements also meet accessibility standards.
5. **Avoid Overload**: Don't use too many visual cues at once, which can create new distractions.
6. **Test with Users**: Regularly test with users who have ADHD to ensure the enhancements are helpful.
7. **Document Shortcuts**: Always provide a visible reference for keyboard shortcuts.
8. **Provide Clear Feedback**: Use progress indicators and status updates to provide clear feedback on actions.

## Resources

- [ADHD-Friendly Design Guidelines](https://www.w3.org/WAI/cognitive/)
- [Color Contrast Checker](https://webaim.org/resources/contrastchecker/)
- [Reduced Motion Media Query](https://developer.mozilla.org/en-US/docs/Web/CSS/@media/prefers-reduced-motion)
