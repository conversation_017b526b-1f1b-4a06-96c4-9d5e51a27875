# ADHD Medication Reference Feature Documentation

## Overview

The ADHD Medication Reference feature provides a comprehensive database of medications used for ADHD treatment and cognitive enhancement. This feature allows users to search for medications, view detailed information about them, and understand their effects, dosages, and potential side effects.

## Table of Contents

1. [Feature Components](#feature-components)
2. [Database Structure](#database-structure)
3. [User Interface](#user-interface)
4. [Functionality](#functionality)
5. [Usage Guide](#usage-guide)
6. [Technical Implementation](#technical-implementation)
7. [Future Enhancements](#future-enhancements)

## Feature Components

The ADHD Medication Reference feature consists of the following components:

1. **Medication Database** - Comprehensive database of ADHD medications and cognitive enhancers
2. **Search Functionality** - Search for medications by name (generic or brand)
3. **Medication Details** - Detailed information about each medication
4. **Enhancement Areas** - Categorization of medications by enhancement areas
5. **Medication Comparison** - Compare different medications

## Database Structure

The feature uses the `adhd_medications_reference` table:

| Column | Type | Description |
|--------|------|-------------|
| id | INT | Primary key |
| generic_name | VARCHAR | Generic name of the medication |
| brand_names | TEXT | Comma-separated list of brand names |
| medication_class | VARCHAR | Class of medication |
| mechanism_of_action | TEXT | How the medication works |
| typical_dosages | TEXT | Typical dosage information |
| half_life | VARCHAR | Half-life of the medication |
| onset_of_action | VARCHAR | How quickly the medication takes effect |
| duration_of_effect | VARCHAR | How long the effects last |
| common_side_effects | TEXT | Common side effects |
| serious_side_effects | TEXT | Serious side effects to watch for |
| contraindications | TEXT | When the medication should not be used |
| interactions | TEXT | Drug interactions |
| enhancement_areas | TEXT | Areas of cognitive enhancement |
| notes | TEXT | Additional notes |
| created_at | DATETIME | Creation timestamp |
| updated_at | DATETIME | Last update timestamp |

## User Interface

The ADHD Medication Reference feature includes the following UI components:

### 1. Medication Reference Home

- Overview of the medication database
- Search bar for finding medications
- Categories of medications
- Enhancement area filters

### 2. Search Results

- List of medications matching search criteria
- Quick information about each medication
- Sorting and filtering options

### 3. Medication Detail View

- Comprehensive information about a specific medication
- Dosage information
- Side effects
- Interactions
- Enhancement areas
- References and resources

### 4. Enhancement Areas View

- Medications grouped by enhancement areas
- Comparison of medications within each area
- Effectiveness ratings

## Functionality

### 1. Medication Search

- **Search by Name**: Search for medications by generic or brand name
- **Autocomplete**: Suggestions as you type
- **Filter by Class**: Filter medications by medication class
- **Filter by Enhancement Area**: Find medications that enhance specific cognitive functions

### 2. Medication Information

- **Detailed Profiles**: Comprehensive information about each medication
- **Dosage Guidelines**: Typical dosages for different conditions
- **Side Effect Information**: Common and serious side effects
- **Interaction Warnings**: Potential interactions with other medications

### 3. Enhancement Areas

- **Cognitive Enhancement**: Medications that improve focus, attention, and cognitive function
- **Mood Enhancement**: Medications that improve mood and emotional regulation
- **Energy Enhancement**: Medications that increase energy and reduce fatigue
- **Memory Enhancement**: Medications that improve memory function

### 4. Medication Comparison

- **Side-by-Side Comparison**: Compare multiple medications
- **Effectiveness Comparison**: Compare effectiveness for different symptoms
- **Side Effect Comparison**: Compare side effect profiles

## Usage Guide

### Searching for Medications

1. Navigate to the ADHD Medication Reference section
2. Use the search bar to enter a medication name (generic or brand)
3. View the search results
4. Click on a medication to view detailed information

### Browsing by Enhancement Area

1. Navigate to the ADHD Medication Reference section
2. Select an enhancement area from the categories
3. View medications that enhance that specific area
4. Compare medications within the category

### Viewing Medication Details

1. Search for a medication or browse by category
2. Click on a medication to view its detailed profile
3. Review information about dosages, side effects, etc.
4. Use the references for further reading

### Comparing Medications

1. Select multiple medications using the checkboxes
2. Click "Compare Selected"
3. View the side-by-side comparison
4. Compare effectiveness, side effects, and other factors

## Technical Implementation

### Controllers

- **ADHDController.php**: Handles medication reference functionality

### Models

- **ADHDMedicationReference.php**: Manages medication data and operations

### Views

- **adhd/medication/reference.php**: Medication reference home
- **adhd/medication/reference_search.php**: Search results
- **adhd/medication/reference_detail.php**: Medication detail view
- **adhd/medication/reference_compare.php**: Medication comparison view

### JavaScript

- **medication-reference.js**: Handles search autocomplete and dynamic content

## Key Methods

### ADHDMedicationReference Model

```php
/**
 * Search medications by name (generic or brand)
 */
public function searchMedications($searchTerm) {
    // Make sure searchTerm is a string
    $searchTerm = is_array($searchTerm) ? $searchTerm[0] : $searchTerm;
    $searchTerm = "%{$searchTerm}%";
    
    $sql = "SELECT * FROM {$this->table}
            WHERE generic_name LIKE ?
            OR brand_names LIKE ?
            ORDER BY generic_name ASC";
    return $this->db->fetchAll($sql, [$searchTerm, $searchTerm]);
}

/**
 * Get medication suggestions for autocomplete
 */
public function getMedicationSuggestions($searchTerm) {
    // Make sure searchTerm is a string
    $searchTerm = is_array($searchTerm) ? $searchTerm[0] : $searchTerm;
    $searchTerm = "%{$searchTerm}%";
    
    $sql = "SELECT id, generic_name, brand_names, medication_class, typical_dosages
            FROM {$this->table}
            WHERE generic_name LIKE ?
            OR brand_names LIKE ?
            ORDER BY generic_name ASC
            LIMIT 10";
    return $this->db->fetchAll($sql, [$searchTerm, $searchTerm]);
}

/**
 * Get medications by enhancement area
 */
public function getMedicationsByEnhancementArea($enhancementArea) {
    // Make sure enhancementArea is a string
    $enhancementArea = is_array($enhancementArea) ? $enhancementArea[0] : $enhancementArea;
    $searchTerm = "%{$enhancementArea}%";
    
    $sql = "SELECT * FROM {$this->table}
            WHERE enhancement_areas LIKE ?
            ORDER BY generic_name ASC";
    return $this->db->fetchAll($sql, [$searchTerm]);
}
```

### ADHDController

```php
public function searchMedicationReference() {
    $this->requireLogin();

    $searchTerm = $this->getQueryData('search');

    if (empty($searchTerm)) {
        $this->redirect('/adhd/medication/reference');
    }

    // Ensure searchTerm is a string
    if (is_array($searchTerm)) {
        $searchTerm = $searchTerm[0];
    }

    // Search medications
    $searchResults = $this->medicationReferenceModel->searchMedications($searchTerm);

    $this->view('adhd/medication/reference_search', [
        'searchTerm' => $searchTerm,
        'searchResults' => $searchResults
    ]);
}
```

## Future Enhancements

1. **User Reviews**: Allow users to add their experiences with medications
2. **Effectiveness Ratings**: Add user-generated effectiveness ratings
3. **Side Effect Tracking**: Integration with the medication tracker to track side effects
4. **Personalized Recommendations**: AI-powered recommendations based on user profile
5. **Interaction Checker**: Check for interactions with other medications the user is taking
6. **Expanded Database**: Add more medications and supplements
7. **Research Updates**: Regular updates with the latest research findings
8. **Doctor Discussion Guide**: Generate discussion guides for doctor appointments
9. **Medication Schedule Planner**: Help users plan their medication schedule
