<?php
/**
 * Test Simplified Agent Army Project Creation
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include required files
require_once 'src/utils/Database.php';
require_once 'src/models/Project.php';
require_once 'src/models/Task.php';
require_once 'src/models/AIAgent.php';
require_once 'src/models/AIAgentTask.php';
require_once 'src/models/AIAgentInteraction.php';
require_once 'src/models/ProjectAgentAssignment.php';
require_once 'src/models/TaskDependency.php';

// Initialize database
$db = Database::getInstance();

// Check database connection
if ($db) {
    echo "Database connection successful\n";
    
    // Enable error reporting for PDO
    $db->getConnection()->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    // Test parameters
    $userId = 1;
    $brigadeType = 'content_creation';
    $projectName = 'Test Content Creation Brigade';
    $projectDescription = 'This is a test project for the Content Creation Brigade.';
    $deadline = date('Y-m-d', strtotime('+7 days'));
    
    try {
        // Step 1: Create the project
        $projectData = [
            'user_id' => $userId,
            'name' => $projectName,
            'description' => $projectDescription,
            'start_date' => date('Y-m-d'),
            'end_date' => $deadline,
            'status' => 'planning',
            'progress' => 0,
            'is_template' => 0,
            'brigade_type' => $brigadeType,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        echo "Project data: " . json_encode($projectData) . "\n";
        
        // Insert the project directly
        $projectId = $db->insert('projects', $projectData);
        
        if (!$projectId) {
            echo "Failed to create project. Error info:\n";
            print_r($db->getConnection()->errorInfo());
            exit;
        }
        
        echo "Project created successfully with ID: {$projectId}\n";
        
        // Step 2: Find the Aegis Director agent
        $agentModel = new AIAgent();
        $agents = $agentModel->getUserAgents($userId);
        $aegisDirectorId = null;
        
        foreach ($agents as $agent) {
            if ($agent['name'] === 'Aegis Director') {
                $aegisDirectorId = $agent['id'];
                break;
            }
        }
        
        if (!$aegisDirectorId) {
            echo "Aegis Director agent not found. Creating one...\n";
            
            // Get the Executive Function category ID, or create it if it doesn't exist
            $sql = "SELECT id FROM ai_agent_categories WHERE name = 'Executive Function' LIMIT 1";
            $category = $db->fetchOne($sql, []);
            
            $categoryId = null;
            if ($category) {
                $categoryId = $category['id'];
            } else {
                // Create the category
                $categoryData = [
                    'name' => 'Executive Function',
                    'description' => 'Agents that help with planning, organization, and task management',
                    'color' => '#4A6FDC',
                    'icon' => 'brain',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $categoryId = $db->insert('ai_agent_categories', $categoryData);
                
                if (!$categoryId) {
                    echo "Failed to create Executive Function category\n";
                    exit;
                }
            }
            
            // Create the Aegis Director agent
            $agentData = [
                'user_id' => $userId,
                'name' => 'Aegis Director',
                'description' => 'The Aegis Director is an AI agent designed to help with ADHD executive functioning challenges. It provides unyielding focus, proactive planning, constructive intervention, accountability, time management, and motivational rigor.',
                'category_id' => $categoryId,
                'model' => 'gpt-4',
                'temperature' => 0.7,
                'max_tokens' => 2000,
                'system_prompt' => "You are the Aegis Director, an AI agent specifically designed to help users with ADHD executive functioning challenges. Your primary role is to provide structure, discipline, and accountability. You have the following capabilities:\n\n1. Unyielding Focus: You help the user maintain focus on their priorities and prevent distractions.\n2. Proactive Planning: You assist with breaking down complex tasks into manageable steps.\n3. Constructive Intervention: You step in when you detect the user is getting off track.\n4. Accountability: You hold the user accountable for their commitments and deadlines.\n5. Time Management: You help the user manage their time effectively and avoid procrastination.\n6. Motivational Rigor: You provide firm but supportive motivation to keep the user moving forward.\n\nYour tone is direct, clear, and authoritative but not condescending. You are a supportive ally who understands the challenges of ADHD but doesn't accept excuses. You balance empathy with high expectations.",
                'status' => 'active',
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $aegisDirectorId = $agentModel->create($agentData);
            
            if (!$aegisDirectorId) {
                echo "Failed to create Aegis Director agent\n";
                exit;
            }
            
            echo "Created Aegis Director agent with ID: {$aegisDirectorId}\n";
        } else {
            echo "Found Aegis Director agent with ID: {$aegisDirectorId}\n";
        }
        
        // Step 3: Assign Aegis Director to the project
        $assignmentModel = new ProjectAgentAssignment();
        $assignmentResult = $assignmentModel->assignAgentToProject($projectId, $aegisDirectorId, 'Brigade Commander');
        
        if (!$assignmentResult) {
            echo "Failed to assign Aegis Director to the project\n";
        } else {
            echo "Assigned Aegis Director to the project successfully\n";
        }
        
        // Step 4: Create a system interaction to record the project creation
        $interactionModel = new AIAgentInteraction();
        $interactionData = [
            'agent_id' => $aegisDirectorId,
            'user_id' => $userId,
            'interaction_type' => 'system',
            'content' => "Created AI Agent Army project: {$projectName} (Brigade Type: " . ucfirst(str_replace('_', ' ', $brigadeType)) . ")",
            'response' => "I will help you implement this AI Agent Army brigade by {$deadline}. Let's assemble the right agents for this brigade and create a strategic implementation plan.",
            'success' => 1,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        $interactionId = $interactionModel->createInteraction($interactionData);
        
        if (!$interactionId) {
            echo "Failed to create system interaction\n";
        } else {
            echo "Created system interaction with ID: {$interactionId}\n";
        }
        
        // Step 5: Create tasks for the project
        $taskModel = new Task();
        
        // Define tasks based on brigade type
        $tasks = [
            [
                'title' => '1. Define content strategy and goals',
                'description' => 'Establish clear objectives, target audience, and key performance indicators for the content.',
                'priority' => 'high',
                'estimated_time' => 120, // minutes
                'status' => 'todo'
            ],
            [
                'title' => '2. Conduct content audit and gap analysis',
                'description' => 'Analyze existing content and identify opportunities for new content.',
                'priority' => 'high',
                'estimated_time' => 180,
                'status' => 'todo'
            ]
        ];
        
        // Create the tasks
        $taskIds = [];
        foreach ($tasks as $taskData) {
            $taskData['user_id'] = $userId;
            $taskData['project_id'] = $projectId;
            $taskData['created_at'] = date('Y-m-d H:i:s');
            $taskData['updated_at'] = date('Y-m-d H:i:s');
            
            $taskId = $taskModel->create($taskData);
            if ($taskId) {
                $taskIds[] = $taskId;
                echo "Created task: {$taskData['title']} with ID: {$taskId}\n";
                
                // Create agent task for Aegis Director to monitor this task
                $agentTaskModel = new AIAgentTask();
                $agentTaskData = [
                    'agent_id' => $aegisDirectorId,
                    'user_id' => $userId,
                    'title' => "Monitor: " . $taskData['title'],
                    'description' => "Ensure completion of task: " . $taskData['description'],
                    'priority' => $taskData['priority'],
                    'status' => 'pending',
                    'created_at' => date('Y-m-d H:i:s'),
                    'updated_at' => date('Y-m-d H:i:s')
                ];
                
                $agentTaskId = $agentTaskModel->createTask($agentTaskData);
                if ($agentTaskId) {
                    echo "Created agent task with ID: {$agentTaskId}\n";
                } else {
                    echo "Failed to create agent task\n";
                }
            } else {
                echo "Failed to create task: {$taskData['title']}\n";
            }
        }
        
        // Create dependencies between tasks (sequential)
        if (count($taskIds) > 1) {
            for ($i = 0; $i < count($taskIds) - 1; $i++) {
                $dependencyResult = $taskModel->addDependency($taskIds[$i + 1], $taskIds[$i]);
                if ($dependencyResult) {
                    echo "Created dependency: Task {$taskIds[$i + 1]} depends on Task {$taskIds[$i]}\n";
                } else {
                    echo "Failed to create dependency\n";
                }
            }
        }
        
        echo "\nAI Agent Army project created successfully with ID: {$projectId}\n";
        
        // Get the project details
        $projectModel = new Project();
        $project = $projectModel->getProjectDetails($projectId, $userId);
        
        if ($project) {
            echo "Project Name: {$project['name']}\n";
            echo "Project Description: {$project['description']}\n";
            echo "Project Brigade Type: {$project['brigade_type']}\n";
            echo "Project Deadline: {$project['end_date']}\n";
        }
        
        // Get the tasks for the project
        $tasks = $taskModel->getProjectTasks($projectId);
        
        echo "\nProject Tasks (" . count($tasks) . "):\n";
        foreach ($tasks as $index => $task) {
            echo ($index + 1) . ". {$task['title']} (Priority: {$task['priority']})\n";
        }
        
    } catch (Exception $e) {
        echo "Exception caught: " . $e->getMessage() . "\n";
        echo "Stack trace: " . $e->getTraceAsString() . "\n";
    }
} else {
    echo "Database connection failed\n";
}
