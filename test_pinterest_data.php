<?php
/**
 * Test Pinterest Data Generator
 *
 * This script tests the Pinterest data generator to ensure it's working correctly.
 */

// Include the updated Pinterest data generator
require_once 'src/data/pinterest_data_updated.php';

// Test search terms
$searchTerms = ['home decor', 'digital marketing', 'fitness', 'custom search term'];

// Test the generator with each search term
foreach ($searchTerms as $searchTerm) {
    echo "Testing search term: $searchTerm\n";

    // Generate pins
    $pins = getPinterestData($searchTerm, 5);

    // Display the generated pins
    echo "Generated " . count($pins) . " pins:\n";
    foreach ($pins as $index => $pin) {
        echo "\nPin #" . ($index + 1) . ":\n";
        echo "Title: " . $pin['title'] . "\n";
        echo "Description: " . $pin['description'] . "\n";
        echo "Board: " . $pin['board_name'] . "\n";
        echo "Image URL: " . $pin['image_url'] . "\n";
        echo "Save Count: " . $pin['save_count'] . "\n";
        echo "Comment Count: " . $pin['comment_count'] . "\n";
        echo "Created At: " . $pin['created_at'] . "\n";
    }

    echo "\n" . str_repeat('-', 80) . "\n\n";
}

echo "Test completed successfully!\n";
