<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Real Data - PlumberPro</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9fafb;
        }
        .test-section {
            background: white;
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .plumber-card {
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f9fafb;
        }
        .rating {
            color: #f59e0b;
            font-weight: bold;
        }
        .price {
            color: #2563eb;
            font-weight: bold;
        }
        .services {
            color: #6b7280;
            font-style: italic;
        }
        .contact {
            color: #059669;
        }
        .loading {
            text-align: center;
            padding: 40px;
            color: #6b7280;
        }
        .error {
            color: #dc2626;
            background: #fef2f2;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #fecaca;
        }
        .success {
            color: #059669;
            background: #f0fdf4;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #bbf7d0;
        }
    </style>
</head>
<body>
    <h1>🔧 PlumberPro Real Data Test</h1>
    <p>This page tests if the real scraped data is loading correctly.</p>

    <div class="test-section">
        <h2>📊 Data Loading Status</h2>
        <div id="status" class="loading">Loading data...</div>
    </div>

    <div class="test-section">
        <h2>🏢 Real Plumber Companies (10 total)</h2>
        <div id="plumbers-list" class="loading">Loading plumbers...</div>
    </div>

    <div class="test-section">
        <h2>💬 Real Customer Reviews Sample</h2>
        <div id="reviews-list" class="loading">Loading reviews...</div>
    </div>

    <div class="test-section">
        <h2>💰 Real Pricing Data</h2>
        <div id="pricing-data" class="loading">Loading pricing...</div>
    </div>

    <script>
        // Test the PlumberSearch class
        document.addEventListener('DOMContentLoaded', function() {
            testDataLoading();
        });

        function testDataLoading() {
            try {
                // Test if PlumberSearch class exists and loads data
                const testSearch = new PlumberSearch();
                const plumbers = testSearch.searchData;
                
                if (plumbers && plumbers.length > 0) {
                    document.getElementById('status').innerHTML = `
                        <div class="success">
                            ✅ SUCCESS: ${plumbers.length} real plumber companies loaded successfully!
                        </div>
                    `;
                    
                    displayPlumbers(plumbers);
                    loadReviews();
                    loadPricing();
                } else {
                    throw new Error('No plumber data found');
                }
            } catch (error) {
                document.getElementById('status').innerHTML = `
                    <div class="error">
                        ❌ ERROR: ${error.message}
                    </div>
                `;
            }
        }

        function displayPlumbers(plumbers) {
            const container = document.getElementById('plumbers-list');
            
            container.innerHTML = plumbers.map(plumber => `
                <div class="plumber-card">
                    <h3>${plumber.name}</h3>
                    <div class="rating">⭐ ${plumber.rating}/5 (${plumber.reviews.toLocaleString()} reviews)</div>
                    <div class="price">💰 $${plumber.hourlyRate}/hour</div>
                    <div class="services">🔧 ${plumber.services.join(', ')}</div>
                    <div class="contact">📞 ${plumber.phone} | 🌐 ${plumber.website}</div>
                    <div>📍 ${plumber.location} | 🕒 ${plumber.experience}+ years</div>
                    <div>✅ ${plumber.certifications.join(', ')}</div>
                </div>
            `).join('');
        }

        function loadReviews() {
            // Simulate loading reviews from the JSON file
            const sampleReviews = [
                {
                    customer: "Susan",
                    plumber: "Metro-Flow Plumbing",
                    rating: 5,
                    service: "Drain Cleaning",
                    review: "He was professional and really nice. He was honest and on time."
                },
                {
                    customer: "Nancy S.",
                    plumber: "Metro-Flow Plumbing", 
                    rating: 5,
                    service: "Pipe Repair",
                    review: "Very reasonable pricing considering the entire state of Texas is currently in need of plumbers.",
                    cost: 428
                },
                {
                    customer: "Billa W.",
                    plumber: "Benjamin Franklin Plumbing",
                    rating: 5,
                    service: "Drain Cleaning",
                    review: "We received great service from Corey. He was diligent in locating the issue.",
                    cost: 300
                }
            ];

            const container = document.getElementById('reviews-list');
            container.innerHTML = sampleReviews.map(review => `
                <div class="plumber-card">
                    <strong>${review.customer}</strong> - ${review.plumber}
                    <div class="rating">⭐ ${review.rating}/5 stars</div>
                    <div>Service: ${review.service} ${review.cost ? `($${review.cost})` : ''}</div>
                    <div style="margin-top: 8px;">"${review.review}"</div>
                </div>
            `).join('');
        }

        function loadPricing() {
            const pricingData = {
                "Emergency Plumbing": "$150-$500 (avg $275)",
                "Drain Cleaning": "$120-$400 (avg $240)",
                "Water Heater Services": "$120-$3,000 (avg $800)",
                "Toilet Services": "$100-$1,250 (avg $400)",
                "Pipe Repair": "$200-$2,000 (avg $650)",
                "Faucet Repair": "$80-$800 (avg $250)"
            };

            const container = document.getElementById('pricing-data');
            container.innerHTML = Object.entries(pricingData).map(([service, price]) => `
                <div class="plumber-card">
                    <strong>${service}</strong>
                    <div class="price">${price}</div>
                </div>
            `).join('');
        }
    </script>

    <!-- Load the search.js file to test -->
    <script src="assets/js/search.js"></script>
</body>
</html>
