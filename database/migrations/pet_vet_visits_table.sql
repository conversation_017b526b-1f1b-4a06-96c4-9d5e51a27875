-- Pet Vet Visits table
CREATE TABLE IF NOT EXISTS `pet_vet_visits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pet_id` int(11) NOT NULL,
  `visit_date` date NOT NULL,
  `visit_time` time DEFAULT NULL,
  `vet_name` varchar(100) DEFAULT NULL,
  `clinic_name` varchar(100) DEFAULT NULL,
  `visit_type` enum('routine_checkup','vaccination','illness','injury','surgery','dental','emergency','follow_up','other') NOT NULL,
  `reason` text DEFAULT NULL,
  `diagnosis` text DEFAULT NULL,
  `treatment` text DEFAULT NULL,
  `medications` text DEFAULT NULL,
  `follow_up_date` date DEFAULT NULL,
  `follow_up_notes` text DEFAULT NULL,
  `cost` decimal(10,2) DEFAULT NULL,
  `payment_method` enum('cash','credit_card','debit_card','insurance','other') DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `pet_id` (`pet_id`),
  KEY `visit_date` (`visit_date`),
  KEY `follow_up_date` (`follow_up_date`),
  CONSTRAINT `pet_vet_visits_ibfk_1` FOREIGN KEY (`pet_id`) REFERENCES `pets` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
