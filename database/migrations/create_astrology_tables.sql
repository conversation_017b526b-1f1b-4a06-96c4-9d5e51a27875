-- Create Astrology Tables Migration
-- This file creates the necessary tables for the astrology functionality

-- User Astrology Preferences Table
CREATE TABLE IF NOT EXISTS user_astrology_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notifications TINYINT(1) DEFAULT 0 COMMENT 'Enable/disable <PERSON>hu Ka<PERSON>a notifications',
    location VARCHAR(100) DEFAULT 'Sri Lanka' COMMENT 'User location for timing calculations',
    timezone VARCHAR(50) DEFAULT 'Asia/Colombo' COMMENT 'User timezone',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY unique_user_preferences (user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- <PERSON><PERSON> Schedule Table (for future customization)
CREATE TABLE IF NOT EXISTS rahu_kalaya_schedule (
    id INT AUTO_INCREMENT PRIMARY KEY,
    day_of_week ENUM('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday') NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    location VARCHAR(100) DEFAULT 'Sri Lanka',
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    UNIQUE KEY unique_day_location (day_of_week, location)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Insert default Rahu Kalaya timings for Sri Lanka
INSERT INTO rahu_kalaya_schedule (day_of_week, start_time, end_time, location) VALUES
('Monday', '07:30:00', '09:00:00', 'Sri Lanka'),
('Tuesday', '15:00:00', '16:30:00', 'Sri Lanka'),
('Wednesday', '12:00:00', '13:30:00', 'Sri Lanka'),
('Thursday', '13:30:00', '15:00:00', 'Sri Lanka'),
('Friday', '10:30:00', '12:00:00', 'Sri Lanka'),
('Saturday', '09:00:00', '10:30:00', 'Sri Lanka'),
('Sunday', '16:30:00', '18:00:00', 'Sri Lanka')
ON DUPLICATE KEY UPDATE 
    start_time = VALUES(start_time),
    end_time = VALUES(end_time),
    updated_at = CURRENT_TIMESTAMP;

-- Astrology Activity Log Table (for tracking user interactions)
CREATE TABLE IF NOT EXISTS astrology_activity_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    activity_type ENUM('view_dashboard', 'view_schedule', 'view_info', 'check_status', 'save_preferences') NOT NULL,
    activity_data JSON NULL COMMENT 'Additional activity data in JSON format',
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_activity (user_id, activity_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Astrology Notifications Table (for future notification system)
CREATE TABLE IF NOT EXISTS astrology_notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    notification_type ENUM('rahu_kalaya_start', 'rahu_kalaya_end', 'daily_reminder') NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    scheduled_at TIMESTAMP NOT NULL,
    sent_at TIMESTAMP NULL,
    status ENUM('pending', 'sent', 'failed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_notifications (user_id, status),
    INDEX idx_scheduled_at (scheduled_at),
    INDEX idx_status (status)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
