-- Task Batches table
CREATE TABLE IF NOT EXISTS `task_batches` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text,
  `energy_level` enum('high', 'medium', 'low') NOT NULL DEFAULT 'medium',
  `estimated_time` int(11) DEFAULT NULL COMMENT 'Estimated time in minutes',
  `status` enum('active', 'completed', 'archived') NOT NULL DEFAULT 'active',
  `created_at` datetime NOT NULL,
  `updated_at` datetime NOT NULL,
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `energy_level` (`energy_level`),
  KEY `status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Task Batch Items table (for tasks in a batch)
CREATE TABLE IF NOT EXISTS `task_batch_items` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `batch_id` int(11) NOT NULL,
  `task_id` int(11) NOT NULL,
  `position` int(11) NOT NULL DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `batch_task` (`batch_id`, `task_id`),
  KEY `batch_id` (`batch_id`),
  KEY `task_id` (`task_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Add foreign key constraints
ALTER TABLE `task_batches`
  ADD CONSTRAINT `task_batches_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;

ALTER TABLE `task_batch_items`
  ADD CONSTRAINT `task_batch_items_ibfk_1` FOREIGN KEY (`batch_id`) REFERENCES `task_batches` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `task_batch_items_ibfk_2` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;

-- Add batch_id column to time_blocks table if it doesn't exist
ALTER TABLE `time_blocks` 
  ADD COLUMN IF NOT EXISTS `batch_id` int(11) DEFAULT NULL AFTER `task_id`,
  ADD KEY IF NOT EXISTS `batch_id` (`batch_id`),
  ADD CONSTRAINT IF NOT EXISTS `time_blocks_ibfk_2` FOREIGN KEY (`batch_id`) REFERENCES `task_batches` (`id`) ON DELETE SET NULL;
