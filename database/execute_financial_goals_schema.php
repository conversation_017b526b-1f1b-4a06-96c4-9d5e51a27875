<?php
/**
 * Execute Financial Goals Schema
 * 
 * This script executes the SQL in the financial_goals_schema.sql file
 */

// Load database configuration
$config = require_once __DIR__ . '/../src/config/database.php';

try {
    // Connect to the database
    $dsn = "mysql:host={$config['host']};dbname={$config['database']};charset={$config['charset']}";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
    ];
    $pdo = new PDO($dsn, $config['username'], $config['password'], $options);
    
    // Read the SQL file
    $sql = file_get_contents(__DIR__ . '/financial_goals_schema.sql');
    
    // Execute the SQL
    $pdo->exec($sql);
    
    echo "Financial goals schema executed successfully!\n";
} catch (PDOException $e) {
    echo "Error: " . $e->getMessage() . "\n";
    exit(1);
}
