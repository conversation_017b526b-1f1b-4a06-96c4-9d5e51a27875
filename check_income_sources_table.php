<?php
require_once 'src/utils/Database.php';

$db = Database::getInstance();

// Check if income_sources table exists
$result = $db->query("SHOW TABLES LIKE 'income_sources'");
echo "Income sources table exists: " . ($result->rowCount() > 0 ? 'Yes' : 'No') . "\n";

if ($result->rowCount() > 0) {
    // Check table structure
    $result = $db->query("DESCRIBE income_sources");
    echo "Income sources table columns:\n";
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
    }
    
    // Check if there are any records
    $result = $db->query("SELECT COUNT(*) as count FROM income_sources");
    $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Number of income sources: " . $count . "\n";
    
    if ($count > 0) {
        // Show some sample records
        $result = $db->query("SELECT * FROM income_sources LIMIT 3");
        echo "Sample income sources:\n";
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "- ID: " . $row['id'] . ", Name: " . $row['name'] . ", Active: " . $row['is_active'] . "\n";
        }
    }
}

// Check if income_source_transactions table exists
$result = $db->query("SHOW TABLES LIKE 'income_source_transactions'");
echo "\nIncome source transactions table exists: " . ($result->rowCount() > 0 ? 'Yes' : 'No') . "\n";

if ($result->rowCount() > 0) {
    // Check table structure
    $result = $db->query("DESCRIBE income_source_transactions");
    echo "Income source transactions table columns:\n";
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
    }
    
    // Check if there are any records
    $result = $db->query("SELECT COUNT(*) as count FROM income_source_transactions");
    $count = $result->fetch(PDO::FETCH_ASSOC)['count'];
    echo "Number of income source transactions: " . $count . "\n";
}
