<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Include database utility
require_once 'src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Test data
$testData = [
    'user_id' => 1,
    'name' => 'Test Project',
    'description' => 'This is a test project.',
    'start_date' => date('Y-m-d'),
    'end_date' => date('Y-m-d', strtotime('+7 days')),
    'status' => 'planning',
    'progress' => 0,
    'brigade_type' => 'content_creation',
    'created_at' => date('Y-m-d H:i:s'),
    'updated_at' => date('Y-m-d H:i:s')
];

echo "Inserting test project...\n";
$newId = $db->insert('projects', $testData);

if ($newId) {
    echo "Project inserted successfully with ID: {$newId}\n";
} else {
    echo "Failed to insert project\n";
    $error = $db->getConnection()->errorInfo();
    echo "Database error: " . print_r($error, true) . "\n";
}
