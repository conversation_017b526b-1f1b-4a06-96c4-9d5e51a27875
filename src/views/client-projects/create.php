<div class="tools-container">
    <!-- Header -->
    <div class="gallery-header">
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <a href="/momentum/client-projects" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4 text-xl">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h1 class="gallery-title">Create New Client Project</h1>
                    <p class="gallery-subtitle">Set up organized project management for client work</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Project Form -->
    <div class="max-w-4xl mx-auto">
        <form action="/momentum/client-projects/store" method="POST" class="space-y-8">
            <!-- Basic Project Information -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        🧾 Project Information
                    </h3>
                </div>
                <div class="p-6 space-y-6">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Client Selection -->
                        <div>
                            <label for="client_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Client <span class="text-red-500">*</span>
                            </label>
                            <select name="client_id" id="client_id" required class="form-select">
                                <option value="">Select a client</option>
                                <?php foreach ($clients as $client): ?>
                                    <option value="<?= $client['id'] ?>">
                                        <?= View::escape($client['name']) ?>
                                        <?php if ($client['company']): ?>
                                            (<?= View::escape($client['company']) ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                                Don't see your client? <a href="/momentum/freelance/clients/create" class="text-blue-600 hover:text-blue-800">Create a new client</a>
                            </p>
                        </div>

                        <!-- Project Category -->
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Project Category
                            </label>
                            <select name="category" id="category" class="form-select">
                                <option value="Website">🌐 Website</option>
                                <option value="Mobile App">📱 Mobile App</option>
                                <option value="Branding">🎨 Branding</option>
                                <option value="Marketing">📢 Marketing</option>
                                <option value="E-commerce">🛒 E-commerce</option>
                                <option value="Other">📋 Other</option>
                            </select>
                        </div>
                    </div>

                    <!-- Project Name -->
                    <div>
                        <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Project Name <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="name" id="name" required 
                               class="form-input" 
                               placeholder="e.g., ABC Corp Website Redesign">
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            This will appear as: 🌐 [Project Name] – [Client Name]
                        </p>
                    </div>

                    <!-- Project Description -->
                    <div>
                        <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Project Description
                        </label>
                        <textarea name="description" id="description" rows="4" 
                                  class="form-textarea" 
                                  placeholder="Describe the project scope, objectives, and key deliverables..."></textarea>
                    </div>

                    <!-- Timeline -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Start Date
                            </label>
                            <input type="date" name="start_date" id="start_date" 
                                   class="form-input" 
                                   value="<?= date('Y-m-d') ?>">
                        </div>

                        <div>
                            <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                Target Completion Date
                            </label>
                            <input type="date" name="end_date" id="end_date" 
                                   class="form-input">
                        </div>
                    </div>
                </div>
            </div>

            <!-- Project Setup Options -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
                <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                        ⚙️ Project Setup
                    </h3>
                </div>
                <div class="p-6 space-y-6">
                    <!-- Checklist Template -->
                    <div>
                        <label for="checklist_template_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Pre-Launch Checklist Template
                        </label>
                        <select name="checklist_template_id" id="checklist_template_id" class="form-select">
                            <option value="">Create empty checklist</option>
                            <?php foreach ($checklistTemplates as $template): ?>
                                <option value="<?= $template['id'] ?>">
                                    <?= View::escape($template['template_name']) ?>
                                    (<?= View::escape($template['project_category']) ?>)
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            Choose a template to pre-populate your project checklist with ADHD-friendly tasks
                        </p>
                    </div>

                    <!-- Initial Milestones -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Initial Milestones
                        </label>
                        <div class="space-y-3" id="milestones-container">
                            <div class="milestone-item grid grid-cols-1 md:grid-cols-3 gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded">
                                <input type="text" name="milestones[0][title]" placeholder="Milestone title" class="form-input">
                                <input type="date" name="milestones[0][due_date]" class="form-input">
                                <select name="milestones[0][type]" class="form-select">
                                    <option value="kickoff">🚀 Kickoff</option>
                                    <option value="design">🎨 Design</option>
                                    <option value="development">💻 Development</option>
                                    <option value="content">📝 Content</option>
                                    <option value="testing">🧪 Testing</option>
                                    <option value="launch">🎉 Launch</option>
                                    <option value="delivery">📦 Delivery</option>
                                    <option value="other">📋 Other</option>
                                </select>
                            </div>
                        </div>
                        <button type="button" id="add-milestone" class="mt-3 btn-secondary text-sm">
                            <i class="fas fa-plus mr-2"></i> Add Another Milestone
                        </button>
                    </div>

                    <!-- Notion Template Option -->
                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <div class="flex items-start">
                            <div class="flex items-center h-5">
                                <input type="checkbox" name="use_notion_template" id="use_notion_template" value="1"
                                       class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                            </div>
                            <div class="ml-3">
                                <label for="use_notion_template" class="text-sm font-medium text-blue-900 dark:text-blue-100">
                                    🚀 Use Professional Notion-Style Template
                                </label>
                                <p class="text-sm text-blue-700 dark:text-blue-300 mt-1">
                                    Create a comprehensive project with pre-built milestones, content plan, meeting notes, and an
                                    extensive 56-item ADHD-friendly checklist organized across 9 categories. Perfect for professional
                                    website projects.
                                </p>
                                <div class="mt-2 text-xs text-blue-600 dark:text-blue-400">
                                    ✅ Includes: Project Setup, Design & Branding, Content Strategy, Development, SEO & Analytics,
                                    Testing & QA, Security & Performance, Launch Preparation, and Post-Launch Activities
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Project Template -->
                    <div id="regular-template-section">
                        <label for="template_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Project Template (Optional)
                        </label>
                        <select name="template_id" id="template_id" class="form-select">
                            <option value="">Start from scratch</option>
                            <?php foreach ($templates as $template): ?>
                                <option value="<?= $template['id'] ?>">
                                    <?= View::escape($template['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
                            Use an existing project as a template to copy tasks and structure
                        </p>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-4">
                <a href="/momentum/client-projects" class="btn-secondary">
                    Cancel
                </a>
                <button type="submit" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i> Create Client Project
                </button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let milestoneCount = 1;
    
    document.getElementById('add-milestone').addEventListener('click', function() {
        const container = document.getElementById('milestones-container');
        const newMilestone = document.createElement('div');
        newMilestone.className = 'milestone-item grid grid-cols-1 md:grid-cols-3 gap-3 p-3 border border-gray-200 dark:border-gray-700 rounded';
        newMilestone.innerHTML = `
            <input type="text" name="milestones[${milestoneCount}][title]" placeholder="Milestone title" class="form-input">
            <input type="date" name="milestones[${milestoneCount}][due_date]" class="form-input">
            <div class="flex space-x-2">
                <select name="milestones[${milestoneCount}][type]" class="form-select flex-1">
                    <option value="kickoff">🚀 Kickoff</option>
                    <option value="design">🎨 Design</option>
                    <option value="development">💻 Development</option>
                    <option value="content">📝 Content</option>
                    <option value="testing">🧪 Testing</option>
                    <option value="launch">🎉 Launch</option>
                    <option value="delivery">📦 Delivery</option>
                    <option value="other">📋 Other</option>
                </select>
                <button type="button" class="btn-danger text-sm px-3" onclick="this.closest('.milestone-item').remove()">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        `;
        container.appendChild(newMilestone);
        milestoneCount++;
    });

    // Auto-generate project name based on client and category
    const clientSelect = document.getElementById('client_id');
    const categorySelect = document.getElementById('category');
    const nameInput = document.getElementById('name');

    function updateProjectName() {
        const clientText = clientSelect.options[clientSelect.selectedIndex]?.text || '';
        const category = categorySelect.value;
        
        if (clientText && category && !nameInput.value) {
            const clientName = clientText.split('(')[0].trim();
            nameInput.value = `${clientName} ${category}`;
        }
    }

    clientSelect.addEventListener('change', updateProjectName);
    categorySelect.addEventListener('change', updateProjectName);

    // Handle Notion template checkbox
    const notionTemplateCheckbox = document.getElementById('use_notion_template');
    const regularTemplateSection = document.getElementById('regular-template-section');
    const milestonesContainer = document.getElementById('milestones-container');
    const addMilestoneButton = document.getElementById('add-milestone');

    notionTemplateCheckbox.addEventListener('change', function() {
        if (this.checked) {
            // Hide regular template and milestone sections when Notion template is selected
            regularTemplateSection.style.opacity = '0.5';
            regularTemplateSection.style.pointerEvents = 'none';
            milestonesContainer.style.opacity = '0.5';
            milestonesContainer.style.pointerEvents = 'none';
            addMilestoneButton.style.opacity = '0.5';
            addMilestoneButton.style.pointerEvents = 'none';

            // Clear regular template selection
            document.getElementById('template_id').value = '';

            // Show info message
            if (!document.getElementById('notion-info')) {
                const infoDiv = document.createElement('div');
                infoDiv.id = 'notion-info';
                infoDiv.className = 'mt-2 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded text-sm text-green-700 dark:text-green-300';
                infoDiv.innerHTML = '✅ Notion template will automatically create comprehensive milestones, content plan, and checklist for you.';
                notionTemplateCheckbox.closest('.bg-blue-50').appendChild(infoDiv);
            }
        } else {
            // Re-enable regular template and milestone sections
            regularTemplateSection.style.opacity = '1';
            regularTemplateSection.style.pointerEvents = 'auto';
            milestonesContainer.style.opacity = '1';
            milestonesContainer.style.pointerEvents = 'auto';
            addMilestoneButton.style.opacity = '1';
            addMilestoneButton.style.pointerEvents = 'auto';

            // Remove info message
            const infoDiv = document.getElementById('notion-info');
            if (infoDiv) {
                infoDiv.remove();
            }
        }
    });
});
</script>
