<div class="py-6 h-screen flex flex-col">
    <div class="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 flex-grow flex flex-col">
        <!-- Header with back button -->
        <div class="flex items-center mb-6">
            <a href="/momentum/dashboard" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">Focus Mode</h1>
            
            <div class="ml-auto flex space-x-2">
                <a href="/momentum/productivity/focus-timer?task_id=<?= $task['id'] ?? '' ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">
                    <i class="fas fa-clock mr-1.5"></i> Focus Timer
                </a>
            </div>
        </div>

        <?php if (isset($task) && $task): ?>
            <!-- Focus task container -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden flex-grow flex flex-col">
                <div class="p-6 flex-grow flex flex-col">
                    <!-- Task header -->
                    <div class="mb-6">
                        <h2 class="text-xl font-medium text-gray-900 dark:text-white mb-2">
                            <?= htmlspecialchars($task['title']) ?>
                        </h2>
                        
                        <?php if (!empty($task['due_date'])): ?>
                            <div class="text-sm text-gray-500 dark:text-gray-400 mb-2">
                                <i class="far fa-calendar-alt mr-1"></i> Due: <?= date('M j, Y', strtotime($task['due_date'])) ?>
                            </div>
                        <?php endif; ?>
                        
                        <?php if (!empty($task['priority'])): ?>
                            <div class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                <?php 
                                    switch($task['priority']) {
                                        case 'high':
                                            echo 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
                                            break;
                                        case 'medium':
                                            echo 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
                                            break;
                                        default:
                                            echo 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
                                    }
                                ?>">
                                <?= ucfirst(htmlspecialchars($task['priority'])) ?> Priority
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Task description -->
                    <div class="prose dark:prose-invert max-w-none mb-8 flex-grow">
                        <?php if (!empty($task['description'])): ?>
                            <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                <?= nl2br(htmlspecialchars($task['description'])) ?>
                            </div>
                        <?php else: ?>
                            <div class="text-gray-500 dark:text-gray-400 italic">
                                No description provided.
                            </div>
                        <?php endif; ?>
                    </div>
                    
                    <!-- Focus tips -->
                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg mb-6">
                        <h3 class="text-lg font-medium text-blue-800 dark:text-blue-200 mb-2">
                            <i class="fas fa-lightbulb mr-1"></i> Focus Tips
                        </h3>
                        <ul class="list-disc pl-5 text-blue-700 dark:text-blue-300 space-y-1">
                            <li>Break this task into smaller steps if it feels overwhelming</li>
                            <li>Remove distractions from your environment</li>
                            <li>Set a timer for 25 minutes of focused work</li>
                            <li>Take a 5-minute break after each focused session</li>
                            <li>Celebrate your progress, no matter how small</li>
                        </ul>
                    </div>
                    
                    <!-- Action buttons -->
                    <div class="flex flex-wrap gap-3 mt-auto">
                        <a href="/momentum/tasks/complete/<?= $task['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                            <i class="fas fa-check mr-1.5"></i> Mark Complete
                        </a>
                        <a href="/momentum/tasks/edit/<?= $task['id'] ?>" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-edit mr-1.5"></i> Edit Task
                        </a>
                        <a href="/momentum/dashboard" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-home mr-1.5"></i> Return to Dashboard
                        </a>
                    </div>
                </div>
            </div>
        <?php else: ?>
            <!-- No task selected message -->
            <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6 text-center">
                <div class="text-gray-500 dark:text-gray-400 mb-4">
                    <i class="fas fa-exclamation-circle text-4xl"></i>
                </div>
                <h2 class="text-xl font-medium text-gray-900 dark:text-white mb-2">No Task Selected</h2>
                <p class="text-gray-500 dark:text-gray-400 mb-4">
                    Please select a task to enter focus mode.
                </p>
                <a href="/momentum/tasks" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-tasks mr-1.5"></i> Go to Tasks
                </a>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add keyboard shortcut for going back (Backspace)
    document.addEventListener('keydown', function(e) {
        // Only respond to Backspace when not in an input field
        if (e.key === 'Backspace' && 
            !['INPUT', 'TEXTAREA', 'SELECT'].includes(document.activeElement.tagName)) {
            e.preventDefault();
            window.location.href = '/momentum/dashboard';
        }
    });
    
    // Enter full screen mode automatically
    const enterFullScreen = () => {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('Error attempting to enable full-screen mode:', err);
            });
        }
    };
    
    // Add a button to toggle full screen
    const header = document.querySelector('h1').parentNode;
    const fullScreenBtn = document.createElement('button');
    fullScreenBtn.className = 'ml-2 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300';
    fullScreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
    fullScreenBtn.title = 'Toggle Full Screen';
    fullScreenBtn.addEventListener('click', () => {
        if (!document.fullscreenElement) {
            enterFullScreen();
        } else {
            document.exitFullscreen();
        }
    });
    header.insertBefore(fullScreenBtn, header.querySelector('.ml-auto'));
});
</script>
