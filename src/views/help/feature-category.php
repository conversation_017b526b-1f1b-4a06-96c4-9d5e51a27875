<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="flex items-center mb-8">
        <a href="/momentum/help" class="mr-4 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
            <i class="fas fa-arrow-left"></i>
            <span class="ml-1">Back to Help Center</span>
        </a>
        <h1 class="text-3xl font-bold"><?= htmlspecialchars($categoryTitle) ?></h1>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">About <?= htmlspecialchars($categoryTitle) ?></h2>
        <p class="mb-4"><?= htmlspecialchars($categoryDescription) ?></p>

        <div class="flex flex-wrap gap-4 mt-6">
            <div class="flex items-center">
                <span class="w-3 h-3 rounded-full bg-success-500 mr-2"></span>
                <span>Implemented</span>
            </div>
            <div class="flex items-center">
                <span class="w-3 h-3 rounded-full bg-warning-500 mr-2"></span>
                <span>In Progress</span>
            </div>
            <div class="flex items-center">
                <span class="w-3 h-3 rounded-full bg-secondary-500 mr-2"></span>
                <span>Planned</span>
            </div>
            <div class="flex items-center">
                <span class="w-3 h-3 rounded-full bg-gray-500 mr-2"></span>
                <span>Concept</span>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden mb-8">
        <div class="p-6">
            <h2 class="text-xl font-semibold mb-4"><?= htmlspecialchars($categoryTitle) ?> Features</h2>
            <div class="space-y-4">
                <?php foreach ($features as $feature): ?>
                    <div class="feature-item">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="font-medium"><?= htmlspecialchars($feature['name']) ?></h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400"><?= htmlspecialchars($feature['description']) ?></p>
                            </div>
                            <span class="px-2 py-1 text-xs rounded-md shadow-sm status-<?= $feature['status'] ?>"><?= ucfirst(str_replace('-', ' ', $feature['status'])) ?></span>
                        </div>
                        <?php if (isset($feature['details']) && !empty($feature['details'])): ?>
                            <div class="mt-3 pl-4 border-l-2 border-gray-200 dark:border-gray-700">
                                <div class="text-sm text-gray-600 dark:text-gray-400">
                                    <?= $feature['details'] ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-8">
        <h2 class="text-xl font-semibold mb-4">Related Features</h2>
        <p class="mb-4">Explore other feature categories that complement <?= htmlspecialchars($categoryTitle) ?>:</p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
            <?php foreach ($relatedCategories as $category): ?>
                <a href="<?= $category['url'] ?>" class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600 hover:bg-gray-100 dark:hover:bg-gray-600 transition">
                    <h3 class="font-semibold text-gray-700 dark:text-gray-300 mb-2"><?= htmlspecialchars($category['title']) ?></h3>
                    <p class="text-sm text-gray-600 dark:text-gray-400"><?= htmlspecialchars($category['description']) ?></p>
                </a>
            <?php endforeach; ?>
        </div>
    </div>

    <div class="mt-8 text-center">
        <a href="/momentum/help/feature-overview" class="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            View All Features
        </a>
    </div>
</div>

<style>
    .status-implemented {
        background-color: #059669; /* Green - Success */
        color: white;
        border: 1px solid #047857;
        font-weight: 600;
    }

    .status-in-progress {
        background-color: #d97706; /* Amber - Warning */
        color: white;
        border: 1px solid #b45309;
        font-weight: 600;
    }

    .status-planned {
        background-color: #4f46e5; /* Indigo - Info */
        color: white;
        border: 1px solid #4338ca;
        font-weight: 600;
    }

    .status-concept {
        background-color: #6b7280; /* Gray - Neutral */
        color: white;
        border: 1px solid #4b5563;
        font-weight: 600;
    }

    .feature-item {
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        background-color: rgba(0, 0, 0, 0.02);
    }

    .dark .feature-item {
        background-color: rgba(255, 255, 255, 0.02);
    }
</style>
