<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/goals" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <?= View::escape($goal['name']) ?>
            </h1>
            <span class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                <?php if ($goal['status'] === 'active'): ?>
                    bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200
                <?php elseif ($goal['status'] === 'completed'): ?>
                    bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200
                <?php else: ?>
                    bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200
                <?php endif; ?>">
                <?= ucfirst($goal['status']) ?>
            </span>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Main Goal Information -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Goal Details</h3>
                        <div class="flex space-x-2">
                            <a href="/momentum/finances/goals/edit/<?= $goal['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 shadow-sm text-xs font-medium rounded-md text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-edit mr-1"></i> Edit
                            </a>
                            <a href="/momentum/finances/goals/contribution/<?= $goal['id'] ?>" class="inline-flex items-center px-3 py-1.5 border border-transparent shadow-sm text-xs font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                                <i class="fas fa-plus mr-1"></i> Add Contribution
                            </a>
                        </div>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <!-- Progress Bar -->
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-2">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Progress</div>
                                <div class="text-sm font-medium text-gray-900 dark:text-white"><?= $goal['progress_percentage'] ?>%</div>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4">
                                <div class="bg-primary-600 h-4 rounded-full" style="width: <?= $goal['progress_percentage'] ?>%"></div>
                            </div>
                            <div class="flex justify-between items-center mt-2">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    <?= View::formatCurrency($goal['current_amount']) ?>
                                </div>
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    <?= View::formatCurrency($goal['target_amount']) ?>
                                </div>
                            </div>
                        </div>

                        <!-- Time Progress -->
                        <div class="mb-6">
                            <div class="flex justify-between items-center mb-2">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">Time Progress</div>
                                <div class="text-sm font-medium text-gray-900 dark:text-white"><?= $goal['time_progress'] ?>%</div>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-yellow-500 h-2 rounded-full" style="width: <?= $goal['time_progress'] ?>%"></div>
                            </div>
                            <div class="flex justify-between items-center mt-2">
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    <?= date('M j, Y', strtotime($goal['start_date'])) ?>
                                </div>
                                <div class="text-sm font-medium text-gray-500 dark:text-gray-400">
                                    <?= date('M j, Y', strtotime($goal['target_date'])) ?>
                                </div>
                            </div>
                        </div>

                        <!-- Status Indicator -->
                        <div class="mb-6">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    <?php if ($goal['on_track']): ?>
                                        <span class="h-8 w-8 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center">
                                            <i class="fas fa-check text-green-600 dark:text-green-400"></i>
                                        </span>
                                    <?php else: ?>
                                        <span class="h-8 w-8 rounded-full bg-yellow-100 dark:bg-yellow-900 flex items-center justify-center">
                                            <i class="fas fa-exclamation text-yellow-600 dark:text-yellow-400"></i>
                                        </span>
                                    <?php endif; ?>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-gray-900 dark:text-white">
                                        <?php if ($goal['on_track']): ?>
                                            On Track
                                        <?php else: ?>
                                            Falling Behind
                                        <?php endif; ?>
                                    </h3>
                                    <div class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                        <?php if ($goal['on_track']): ?>
                                            You're making good progress toward your goal!
                                        <?php else: ?>
                                            Your savings rate is behind schedule. Consider increasing your contributions.
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Goal Details -->
                        <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                            <dl class="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-6">
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Category</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?= $goal['category'] ? View::escape($goal['category']) : 'Not specified' ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Priority</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?= ucfirst($goal['priority']) ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Created</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?= date('M j, Y', strtotime($goal['created_at'])) ?></dd>
                                </div>
                                <div>
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Last Updated</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?= date('M j, Y', strtotime($goal['updated_at'])) ?></dd>
                                </div>
                                <div class="md:col-span-2">
                                    <dt class="text-sm font-medium text-gray-500 dark:text-gray-400">Description</dt>
                                    <dd class="mt-1 text-sm text-gray-900 dark:text-white"><?= $goal['description'] ? nl2br(View::escape($goal['description'])) : 'No description provided' ?></dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>

                <!-- Contributions History -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Contribution History</h3>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (empty($goal['contributions'])): ?>
                            <div class="text-center py-4">
                                <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                                    <i class="fas fa-coins text-gray-400 dark:text-gray-500"></i>
                                </div>
                                <p class="text-gray-500 dark:text-gray-400 mb-4">No contributions yet</p>
                                <a href="/momentum/finances/goals/contribution/<?= $goal['id'] ?>" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                    <i class="fas fa-plus mr-2"></i> Add First Contribution
                                </a>
                            </div>
                        <?php else: ?>
                            <div class="overflow-x-auto">
                                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                    <thead class="bg-gray-50 dark:bg-gray-800">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Date</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Amount</th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Notes</th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                        <?php foreach ($goal['contributions'] as $contribution): ?>
                                            <tr>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                                    <?= date('M j, Y', strtotime($contribution['contribution_date'])) ?>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-green-600 dark:text-green-400 font-medium">
                                                    <?= View::formatCurrency($contribution['amount']) ?>
                                                </td>
                                                <td class="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                                                    <?= $contribution['notes'] ? View::escape($contribution['notes']) : '-' ?>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Milestones -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Milestones</h3>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php if (empty($goal['milestones'])): ?>
                            <div class="text-center py-4">
                                <div class="inline-flex items-center justify-center w-12 h-12 rounded-full bg-gray-100 dark:bg-gray-700 mb-4">
                                    <i class="fas fa-flag-checkered text-gray-400 dark:text-gray-500"></i>
                                </div>
                                <p class="text-gray-500 dark:text-gray-400">No milestones set</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                                    You can add milestones when editing this goal
                                </p>
                            </div>
                        <?php else: ?>
                            <div class="space-y-4">
                                <?php foreach ($goal['milestones'] as $milestone): ?>
                                    <div class="border-l-4 <?= $milestone['is_reached'] ? 'border-green-500 dark:border-green-400' : 'border-gray-300 dark:border-gray-600' ?> pl-4 py-2">
                                        <div class="flex items-center">
                                            <?php if ($milestone['is_reached']): ?>
                                                <span class="flex-shrink-0 h-5 w-5 rounded-full bg-green-100 dark:bg-green-900 flex items-center justify-center mr-2">
                                                    <i class="fas fa-check text-xs text-green-600 dark:text-green-400"></i>
                                                </span>
                                            <?php else: ?>
                                                <span class="flex-shrink-0 h-5 w-5 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center mr-2">
                                                    <i class="fas fa-flag text-xs text-gray-500 dark:text-gray-400"></i>
                                                </span>
                                            <?php endif; ?>
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white">
                                                <?= View::escape($milestone['name']) ?>
                                            </h4>
                                        </div>
                                        <div class="mt-1 ml-7">
                                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                                <?= View::formatCurrency($milestone['target_amount']) ?>
                                                <?php if ($milestone['target_date']): ?>
                                                    by <?= date('M j, Y', strtotime($milestone['target_date'])) ?>
                                                <?php endif; ?>
                                            </p>
                                            <?php if ($milestone['is_reached']): ?>
                                                <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                                                    Reached on <?= date('M j, Y', strtotime($milestone['reached_date'])) ?>
                                                </p>
                                            <?php endif; ?>
                                            <?php if ($milestone['notes']): ?>
                                                <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                                    <?= View::escape($milestone['notes']) ?>
                                                </p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Savings Projection -->
                <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
                    <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Savings Projection</h3>
                    </div>
                    <div class="px-4 py-5 sm:p-6">
                        <?php
                            // Calculate average monthly contribution
                            $totalContributions = 0;
                            $contributionDates = [];
                            foreach ($goal['contributions'] as $contribution) {
                                $totalContributions += $contribution['amount'];
                                $contributionDates[] = strtotime($contribution['contribution_date']);
                            }
                            
                            $monthlyRate = 0;
                            if (count($contributionDates) > 1) {
                                $firstDate = min($contributionDates);
                                $lastDate = max($contributionDates);
                                $monthsDiff = max(1, round((($lastDate - $firstDate) / (30 * 24 * 60 * 60)), 1));
                                $monthlyRate = $totalContributions / $monthsDiff;
                            } elseif (count($contributionDates) === 1) {
                                // If only one contribution, use time since that contribution
                                $firstDate = min($contributionDates);
                                $now = time();
                                $monthsDiff = max(1, round((($now - $firstDate) / (30 * 24 * 60 * 60)), 1));
                                $monthlyRate = $totalContributions / $monthsDiff;
                            }
                            
                            // Calculate remaining amount and months
                            $remainingAmount = max(0, $goal['target_amount'] - $goal['current_amount']);
                            $monthsToGoal = $monthlyRate > 0 ? ceil($remainingAmount / $monthlyRate) : 0;
                            
                            // Calculate projected completion date
                            $projectedDate = $monthlyRate > 0 ? date('M j, Y', strtotime("+{$monthsToGoal} months")) : 'Unknown';
                            
                            // Calculate if on target
                            $targetDate = strtotime($goal['target_date']);
                            $projectedTimestamp = $monthlyRate > 0 ? strtotime("+{$monthsToGoal} months") : PHP_INT_MAX;
                            $onTarget = $projectedTimestamp <= $targetDate;
                        ?>
                        
                        <div class="space-y-4">
                            <div>
                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Average Monthly Contribution</p>
                                <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= View::formatCurrency($monthlyRate) ?></p>
                            </div>
                            
                            <div>
                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Remaining Amount</p>
                                <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= View::formatCurrency($remainingAmount) ?></p>
                            </div>
                            
                            <div>
                                <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Projected Completion</p>
                                <p class="text-lg font-semibold text-gray-900 dark:text-white"><?= $projectedDate ?></p>
                                
                                <?php if ($monthlyRate > 0): ?>
                                    <?php if ($onTarget): ?>
                                        <p class="text-xs text-green-600 dark:text-green-400 mt-1">
                                            <i class="fas fa-check-circle mr-1"></i> On target to meet goal date
                                        </p>
                                    <?php else: ?>
                                        <p class="text-xs text-yellow-600 dark:text-yellow-400 mt-1">
                                            <i class="fas fa-exclamation-circle mr-1"></i> Projected to complete after target date
                                        </p>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <p class="text-xs text-red-600 dark:text-red-400 mt-1">
                                        <i class="fas fa-exclamation-triangle mr-1"></i> Need more contribution data
                                    </p>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($monthlyRate > 0 && !$onTarget): ?>
                                <div class="border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">Suggested Monthly Contribution</p>
                                    <?php
                                        $monthsToTarget = max(1, round((($targetDate - time()) / (30 * 24 * 60 * 60)), 0));
                                        $suggestedMonthly = $remainingAmount / $monthsToTarget;
                                    ?>
                                    <p class="text-lg font-semibold text-primary-600 dark:text-primary-400"><?= View::formatCurrency($suggestedMonthly) ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                        To reach your goal by <?= date('M j, Y', $targetDate) ?>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
