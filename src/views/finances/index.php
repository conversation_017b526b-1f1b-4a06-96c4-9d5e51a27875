<?php
require_once __DIR__ . '/../../utils/Session.php';
require_once __DIR__ . '/../../utils/View.php';
require_once __DIR__ . '/../../utils/Database.php';
require_once __DIR__ . '/../../models/Finance.php';
require_once __DIR__ . '/../../models/Subscription.php';
require_once __DIR__ . '/../../models/Debt.php';
require_once __DIR__ . '/../../models/Budget.php';
require_once __DIR__ . '/../../models/IncomeSource.php';
require_once __DIR__ . '/../../models/FinancialGoal.php';
require_once __DIR__ . '/../../models/Receipt.php';
?>
<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Financial Overview</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/finances/create-transaction" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Transaction
                </a>
                <a href="/momentum/finances/subscriptions" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-calendar-alt mr-2"></i> Subscriptions
                </a>
                <a href="/momentum/finances/debts" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-hand-holding-usd mr-2"></i> Debts
                </a>
                <a href="/momentum/finances/budgets" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-chart-pie mr-2"></i> Budgets
                </a>
                <a href="/momentum/finances/income-sources" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-money-bill-wave mr-2"></i> Income Sources
                </a>
                <a href="/momentum/finances/goals" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-piggy-bank mr-2"></i> Financial Goals
                </a>
                <a href="/momentum/finances/reports" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-chart-bar mr-2"></i> Reports
                </a>
                <a href="#receipt-management" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-receipt mr-2"></i> Receipts
                </a>
            </div>
        </div>

        <!-- Financial Summary -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Income</h3>
                    <div class="text-3xl font-bold text-green-600 dark:text-green-400">
                        <?= View::formatCurrency($summary['total_income'] ?? 0) ?>
                    </div>
                    <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        <div class="flex justify-between">
                            <span>Monetary:</span>
                            <span class="font-medium text-gray-700 dark:text-gray-300"><?= View::formatCurrency($comprehensiveSummary['monetary_income'] ?? 0) ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span>Non-monetary:</span>
                            <span class="font-medium text-gray-700 dark:text-gray-300"><?= View::formatCurrency($comprehensiveSummary['non_monetary_income'] ?? 0) ?></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Expenses</h3>
                    <div class="text-3xl font-bold text-red-600 dark:text-red-400">
                        <?= View::formatCurrency($summary['total_expense'] ?? 0) ?>
                    </div>
                    <div class="mt-2 text-sm text-gray-500 dark:text-gray-400">
                        <div class="flex justify-between">
                            <span>Monetary:</span>
                            <span class="font-medium text-gray-700 dark:text-gray-300"><?= View::formatCurrency($comprehensiveSummary['monetary_expense'] ?? 0) ?></span>
                        </div>
                        <div class="flex justify-between">
                            <span>Non-monetary:</span>
                            <span class="font-medium text-gray-700 dark:text-gray-300"><?= View::formatCurrency($comprehensiveSummary['non_monetary_expense'] ?? 0) ?></span>
                        </div>
                        <div class="flex justify-between mt-1 pt-1 border-t border-gray-200 dark:border-gray-700">
                            <span>Credit Card:</span>
                            <span class="font-medium text-gray-700 dark:text-gray-300"><?= View::formatCurrency($comprehensiveSummary['credit_card_expense'] ?? 0) ?></span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">Balance</h3>
                    <div class="text-3xl font-bold <?= ($summary['total_income'] ?? 0) - ($summary['total_expense'] ?? 0) >= 0 ? 'text-blue-600 dark:text-blue-400' : 'text-red-600 dark:text-red-400' ?>">
                        <?= View::formatCurrency(($summary['total_income'] ?? 0) - ($summary['total_expense'] ?? 0)) ?>
                    </div>
                    <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        Net balance for the selected period
                    </p>
                </div>
            </div>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Filters</h3>
                <form action="/momentum/finances" method="GET" class="grid grid-cols-1 md:grid-cols-5 gap-4">
                    <div>
                        <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Start Date</label>
                        <input type="date" id="start_date" name="start_date"
                            value="<?= htmlspecialchars($filters['start_date'] ?? date('Y-m-01')) ?>"
                            class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                    </div>
                    <div>
                        <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">End Date</label>
                        <input type="date" id="end_date" name="end_date"
                            value="<?= htmlspecialchars($filters['end_date'] ?? date('Y-m-t')) ?>"
                            class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                    </div>
                    <div>
                        <label for="type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Type</label>
                        <select id="type" name="type" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">All Types</option>
                            <option value="income" <?= (isset($filters['type']) && $filters['type'] === 'income') ? 'selected' : '' ?>>Income</option>
                            <option value="expense" <?= (isset($filters['type']) && $filters['type'] === 'expense') ? 'selected' : '' ?>>Expense</option>
                        </select>
                    </div>
                    <div>
                        <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
                        <select id="category" name="category" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">All Categories</option>
                            <?php
                            if (!empty($categories)) {
                                foreach ($categories as $category) {
                                    $selected = (isset($filters['category']) && $filters['category'] === $category) ? 'selected' : '';
                                    echo '<option value="' . htmlspecialchars($category) . '" ' . $selected . '>' . htmlspecialchars($category) . '</option>';
                                }
                            }
                            ?>
                        </select>
                    </div>
                    <div>
                        <label for="transaction_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Form</label>
                        <select id="transaction_type" name="transaction_type" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">All Forms</option>
                            <option value="monetary" <?= (isset($filters['transaction_type']) && $filters['transaction_type'] === 'monetary') ? 'selected' : '' ?>>Money</option>
                            <option value="non_monetary" <?= (isset($filters['transaction_type']) && $filters['transaction_type'] === 'non_monetary') ? 'selected' : '' ?>>Goods/Services</option>
                        </select>
                    </div>
                    <div class="md:col-span-5 flex justify-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-filter mr-2"></i> Apply Filters
                        </button>
                        <a href="/momentum/finances" class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-times mr-2"></i> Clear Filters
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Transactions List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Transactions</h3>
            </div>
            <?php if (empty($transactions)): ?>
                <div class="px-4 py-5 sm:p-6 text-center">
                    <div class="text-gray-500 dark:text-gray-400 mb-4">
                        <i class="fas fa-money-bill-wave text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No transactions found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">
                        <?php if (!empty($filters)): ?>
                            Try adjusting your filters or
                        <?php endif; ?>
                        add a new transaction to get started.
                    </p>

                    <!-- Debug information -->
                    <div class="mt-4 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg text-left">
                        <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">Debug Information</h4>
                        <p class="text-sm text-gray-700 dark:text-gray-300">Applied Filters:</p>
                        <pre class="text-xs text-gray-600 dark:text-gray-400 mt-1 overflow-auto max-h-40"><?php print_r($filters); ?></pre>
                    </div>
                    <a href="/momentum/finances/create-transaction" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i> Add Transaction
                    </a>
                </div>
            <?php else: ?>
                <!-- Debug information when transactions exist -->
                <div class="px-4 py-2 bg-gray-100 dark:bg-gray-700 text-left">
                    <details>
                        <summary class="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer">Show Debug Information</summary>
                        <div class="mt-2">
                            <p class="text-sm text-gray-700 dark:text-gray-300">Applied Filters:</p>
                            <pre class="text-xs text-gray-600 dark:text-gray-400 mt-1 overflow-auto max-h-40"><?php print_r($filters); ?></pre>

                            <p class="text-sm text-gray-700 dark:text-gray-300 mt-3">First Transaction Data:</p>
                            <pre class="text-xs text-gray-600 dark:text-gray-400 mt-1 overflow-auto max-h-40"><?php if (!empty($transactions)) print_r($transactions[0]); ?></pre>

                            <p class="text-sm text-gray-700 dark:text-gray-300 mt-3">Transaction Count: <?= count($transactions) ?></p>

                            <p class="text-sm text-gray-700 dark:text-gray-300 mt-3">Non-Monetary Transactions:</p>
                            <pre class="text-xs text-gray-600 dark:text-gray-400 mt-1 overflow-auto max-h-40"><?php
                                $nonMonetaryTransactions = array_filter($transactions, function($t) {
                                    return isset($t['transaction_type']) && $t['transaction_type'] === 'non_monetary';
                                });
                                print_r($nonMonetaryTransactions);
                            ?></pre>
                        </div>
                    </details>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Category
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Description
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Amount
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($transactions as $transaction): ?>
                                <tr class="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                        <?= View::formatDate($transaction['date']) ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium <?= $transaction['type'] === 'income' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' ?>">
                                            <?= View::escape($transaction['category']) ?>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-900 dark:text-white">
                                        <?php if ($transaction['transaction_type'] === 'non_monetary' && !empty($transaction['goods_services_description'])): ?>
                                            <?= View::escape($transaction['goods_services_description']) ?>
                                            <?php if (!empty($transaction['description'])): ?>
                                                <br><span class="text-xs text-gray-500 dark:text-gray-400"><?= View::escape($transaction['description']) ?></span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <?= View::escape($transaction['description'] ?? '') ?>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-right font-medium <?= $transaction['type'] === 'income' ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                                        <?php
                                        // Display fair_market_value for non-monetary transactions, otherwise display amount
                                        $displayAmount = 0;
                                        if ($transaction['transaction_type'] === 'non_monetary' && isset($transaction['fair_market_value'])) {
                                            $displayAmount = $transaction['fair_market_value'];
                                        } else {
                                            $displayAmount = $transaction['amount'];
                                        }
                                        ?>
                                        <?= $transaction['type'] === 'income' ? '+' : '-' ?><?= View::formatCurrency($displayAmount) ?>
                                        <?php if ($transaction['transaction_type'] === 'non_monetary'): ?>
                                            <span class="text-xs text-gray-500 dark:text-gray-400 ml-1">(Goods)</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end space-x-2">
                                            <?php
                                            // Check if transaction has receipts
                                            $receiptModel = new Receipt();
                                            $receipts = $receiptModel->getTransactionReceipts($transaction['id']);
                                            if (!empty($receipts)):
                                            ?>
                                            <a href="/momentum/finances/transactions/<?= $transaction['id'] ?>/receipts" class="text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300" title="View Receipts (<?= count($receipts) ?>)">
                                                <i class="fas fa-receipt"></i>
                                            </a>
                                            <?php endif; ?>
                                            <a href="/momentum/finances/edit-transaction/<?= $transaction['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button type="button" onclick="deleteTransaction(<?= $transaction['id'] ?>)" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300 bg-transparent border-0 p-0 cursor-pointer" title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>

        <!-- Spending by Category -->
        <?php if (!empty($spendingByCategory)): ?>
            <div class="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Spending by Category</h3>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="space-y-4">
                        <?php foreach ($spendingByCategory as $category): ?>
                            <div>
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= View::escape($category['category']) ?></span>
                                    <span class="text-sm font-medium text-gray-700 dark:text-gray-300"><?= View::formatCurrency($category['total_amount']) ?></span>
                                </div>
                                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                    <?php
                                    $percentage = ($category['total_amount'] / ($summary['total_expense'] ?? 1)) * 100;
                                    ?>
                                    <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= min(100, $percentage) ?>%"></div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <!-- Debt Summary -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Debt Summary</h3>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Debt Balance</p>
                        <p class="text-xl font-semibold <?= ($debtSummary['total_receivable'] - $debtSummary['total_payable']) >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400' ?>">
                            <?= View::formatCurrency($debtSummary['total_receivable'] - $debtSummary['total_payable']) ?>
                        </p>
                    </div>
                    <a href="/momentum/finances/debts" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-hand-holding-usd mr-2"></i> Manage Debts
                    </a>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Money Owed to You -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border-l-4 border-green-500">
                        <div class="p-4">
                            <div class="flex items-center">
                                <div class="rounded-full bg-green-100 dark:bg-green-900 p-2 mr-3">
                                    <i class="fas fa-hand-holding-usd text-green-600 dark:text-green-400"></i>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Money Owed to You</h3>
                                    <p class="text-lg font-bold text-green-600 dark:text-green-400"><?= View::formatCurrency($debtSummary['total_receivable']) ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        <span class="font-medium"><?= $debtSummary['receivable_count'] ?></span> active receivables
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Money You Owe -->
                    <div class="bg-white dark:bg-gray-800 overflow-hidden shadow-sm rounded-lg border-l-4 border-red-500">
                        <div class="p-4">
                            <div class="flex items-center">
                                <div class="rounded-full bg-red-100 dark:bg-red-900 p-2 mr-3">
                                    <i class="fas fa-hand-holding-usd text-red-600 dark:text-red-400 fa-flip-horizontal"></i>
                                </div>
                                <div>
                                    <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400">Money You Owe</h3>
                                    <p class="text-lg font-bold text-red-600 dark:text-red-400"><?= View::formatCurrency($debtSummary['total_payable']) ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        <span class="font-medium"><?= $debtSummary['payable_count'] ?></span> active payables
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Budget Summary -->
        <?php if ($activeBudget && $budgetProgress): ?>
            <div class="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">Budget Summary</h3>
                        <div class="mt-2 md:mt-0 text-sm text-gray-500 dark:text-gray-400">
                            <?= View::formatDate($activeBudget['start_date']) ?> to <?= View::formatDate($activeBudget['end_date']) ?>
                        </div>
                    </div>
                </div>
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <p class="text-sm text-gray-500 dark:text-gray-400">Budget Progress</p>
                            <p class="text-xl font-semibold text-gray-900 dark:text-white">
                                <?= View::formatCurrency($budgetProgress['total_spent']) ?> / <?= View::formatCurrency($budgetProgress['total_budget']) ?>
                            </p>
                        </div>
                        <a href="/momentum/finances/budgets" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-chart-pie mr-2"></i> Manage Budgets
                        </a>
                    </div>

                    <!-- Overall Budget Progress -->
                    <div class="mb-4">
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                            <div class="h-2.5 rounded-full <?= $budgetProgress['percentage'] > 100 ? 'bg-red-600' : ($budgetProgress['percentage'] > 90 ? 'bg-yellow-500' : 'bg-green-500') ?>" style="width: <?= min(100, $budgetProgress['percentage']) ?>%"></div>
                        </div>
                        <div class="flex justify-between mt-1 text-xs text-gray-500 dark:text-gray-400">
                            <span><?= number_format($budgetProgress['percentage'], 1) ?>% spent</span>
                            <?php
                            // Calculate days elapsed percentage
                            $today = date('Y-m-d');
                            $startDate = $activeBudget['start_date'];
                            $endDate = $activeBudget['end_date'];
                            $totalDays = (strtotime($endDate) - strtotime($startDate)) / (60 * 60 * 24);
                            $daysElapsed = min($totalDays, max(0, (strtotime($today) - strtotime($startDate)) / (60 * 60 * 24)));
                            $percentElapsed = ($totalDays > 0) ? ($daysElapsed / $totalDays) * 100 : 0;
                            ?>
                            <span><?= number_format($percentElapsed, 1) ?>% of period elapsed</span>
                        </div>
                    </div>

                    <!-- Top Categories -->
                    <?php if (!empty($budgetProgress['categories'])): ?>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Top Categories</h4>
                        <div class="space-y-2">
                            <?php
                            // Sort categories by percentage spent
                            usort($budgetProgress['categories'], function($a, $b) {
                                return $b['percentage'] - $a['percentage'];
                            });

                            // Show top 3 categories
                            $topCategories = array_slice($budgetProgress['categories'], 0, 3);
                            foreach ($topCategories as $category):
                            ?>
                                <div>
                                    <div class="flex items-center justify-between mb-1">
                                        <span class="text-xs font-medium text-gray-700 dark:text-gray-300"><?= View::escape($category['category']['category']) ?></span>
                                        <div class="text-xs">
                                            <span class="font-medium text-gray-700 dark:text-gray-300">
                                                <?= View::formatCurrency($category['spent']) ?> / <?= View::formatCurrency($category['category']['amount']) ?>
                                            </span>
                                            <span class="ml-1 <?= $category['percentage'] > 90 ? 'text-red-600 dark:text-red-400' : 'text-gray-500 dark:text-gray-400' ?>">
                                                (<?= number_format($category['percentage'], 1) ?>%)
                                            </span>
                                        </div>
                                    </div>
                                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5">
                                        <div class="h-1.5 rounded-full <?= $category['percentage'] > 100 ? 'bg-red-600' : ($category['percentage'] > 90 ? 'bg-yellow-500' : 'bg-green-500') ?>" style="width: <?= min(100, $category['percentage']) ?>%"></div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>

                        <?php if (count($budgetProgress['categories']) > 3): ?>
                            <div class="mt-3 text-center">
                                <a href="/momentum/finances/budgets/view/<?= $activeBudget['id'] ?>" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    View all <?= count($budgetProgress['categories']) ?> categories
                                </a>
                            </div>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>
            </div>
        <?php else: ?>
            <div class="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
                <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Budget Planning</h3>
                </div>
                <div class="px-4 py-5 sm:p-6 text-center">
                    <div class="text-gray-500 dark:text-gray-400 mb-4">
                        <i class="fas fa-chart-pie text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No Active Budget</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">
                        Create a budget to track your spending and stay on top of your finances.
                    </p>
                    <a href="/momentum/finances/budgets/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i> Create Budget
                    </a>
                </div>
            </div>
        <?php endif; ?>

        <!-- Financial Goals Summary -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Financial Goals</h3>
                    <a href="/momentum/finances/goals" class="mt-2 md:mt-0 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-piggy-bank mr-2"></i> Manage Goals
                    </a>
                </div>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Total Saved</p>
                        <p class="text-xl font-semibold text-green-600 dark:text-green-400"><?= View::formatCurrency($goalsSummary['total_saved_amount'] ?? 0) ?></p>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            of <?= View::formatCurrency($goalsSummary['total_target_amount'] ?? 0) ?>
                            (<?= $goalsSummary['total_target_amount'] > 0 ? round(($goalsSummary['total_saved_amount'] / $goalsSummary['total_target_amount']) * 100, 1) : 0 ?>%)
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Active Goals</p>
                        <p class="text-xl font-semibold text-primary-600 dark:text-primary-400"><?= $goalsSummary['active_goals'] ?? 0 ?></p>
                    </div>
                </div>
                <?php if (empty($activeGoals)): ?>
                    <p class="text-gray-500 dark:text-gray-400 text-center py-4">No active financial goals</p>
                    <div class="text-center">
                        <a href="/momentum/finances/goals/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i> Create Goal
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php
                        // Sort goals by priority (high to low) and then by progress (lowest to highest)
                        usort($activeGoals, function($a, $b) {
                            // First sort by priority
                            $priorityOrder = ['high' => 1, 'medium' => 2, 'low' => 3];
                            $priorityA = $priorityOrder[$a['priority']] ?? 4;
                            $priorityB = $priorityOrder[$b['priority']] ?? 4;

                            if ($priorityA !== $priorityB) {
                                return $priorityA - $priorityB;
                            }

                            // Then sort by progress (lowest to highest)
                            $progressA = $a['target_amount'] > 0 ? ($a['current_amount'] / $a['target_amount']) : 0;
                            $progressB = $b['target_amount'] > 0 ? ($b['current_amount'] / $b['target_amount']) : 0;

                            return $progressA - $progressB;
                        });

                        // Show only first 3 goals
                        $displayGoals = array_slice($activeGoals, 0, 3);
                        foreach ($displayGoals as $goal):
                            $progressPercentage = $goal['target_amount'] > 0 ? min(100, round(($goal['current_amount'] / $goal['target_amount']) * 100, 1)) : 0;
                        ?>
                            <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                                <div class="flex items-start">
                                    <div class="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-primary-100 dark:bg-primary-900 mr-4">
                                        <i class="fas <?= $goal['icon'] ?? 'fa-piggy-bank' ?> text-primary-600 dark:text-primary-400"></i>
                                    </div>
                                    <div class="flex-1">
                                        <div class="flex justify-between items-start">
                                            <h4 class="text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($goal['name']) ?></h4>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200">
                                                <?= ucfirst($goal['priority']) ?>
                                            </span>
                                        </div>
                                        <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                            Target: <?= View::formatCurrency($goal['target_amount']) ?> by <?= date('M j, Y', strtotime($goal['target_date'])) ?>
                                        </p>
                                        <div class="mt-2">
                                            <div class="flex justify-between text-xs text-gray-500 dark:text-gray-400 mb-1">
                                                <span><?= View::formatCurrency($goal['current_amount']) ?></span>
                                                <span><?= $progressPercentage ?>%</span>
                                            </div>
                                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                                <div class="bg-primary-600 h-2 rounded-full" style="width: <?= $progressPercentage ?>%"></div>
                                            </div>
                                        </div>
                                        <div class="mt-3 flex justify-end">
                                            <a href="/momentum/finances/goals/view/<?= $goal['id'] ?>" class="text-xs text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                                View Details
                                            </a>
                                            <span class="mx-2 text-gray-300 dark:text-gray-600">|</span>
                                            <a href="/momentum/finances/goals/contribution/<?= $goal['id'] ?>" class="text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">
                                                Add Contribution
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php if (count($activeGoals) > 3): ?>
                            <div class="text-center pt-2">
                                <a href="/momentum/finances/goals" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    View all <?= count($activeGoals) ?> goals
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Income Sources Summary -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Income Sources</h3>
                    <a href="/momentum/finances/income-sources" class="mt-2 md:mt-0 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-money-bill-wave mr-2"></i> Manage Income Sources
                    </a>
                </div>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Total Income from Sources</p>
                        <p class="text-xl font-semibold text-green-600 dark:text-green-400"><?= View::formatCurrency($totalSourceIncome) ?></p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Active Sources</p>
                        <p class="text-xl font-semibold text-primary-600 dark:text-primary-400"><?= count(array_filter($incomeSources, function($source) { return $source['is_active'] == 1; })) ?></p>
                    </div>
                </div>
                <?php if (empty($incomeSources)): ?>
                    <p class="text-gray-500 dark:text-gray-400 text-center py-4">No income sources defined</p>
                    <div class="text-center">
                        <a href="/momentum/finances/income-sources/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i> Add Income Source
                        </a>
                    </div>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php
                        // Sort sources by total income
                        usort($incomeSources, function($a, $b) {
                            return ($b['total_income'] ?? 0) - ($a['total_income'] ?? 0);
                        });

                        // Show only first 3 sources
                        $displaySources = array_slice($incomeSources, 0, 3);
                        foreach ($displaySources as $source):
                        ?>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($source['name']) ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        <?= View::escape($source['category'] ?? 'Uncategorized') ?>
                                        <?php if ($source['is_recurring']): ?>
                                            <span class="ml-2 px-1.5 py-0.5 bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100 rounded-full text-xs">
                                                <?= View::escape($source['recurrence_pattern'] ?? 'Recurring') ?>
                                            </span>
                                        <?php endif; ?>
                                    </p>
                                </div>
                                <div class="text-sm font-medium text-green-600 dark:text-green-400">
                                    <?= View::formatCurrency($source['total_income'] ?? 0) ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php if (count($incomeSources) > 3): ?>
                            <div class="text-center pt-2">
                                <a href="/momentum/finances/income-sources" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    View all <?= count($incomeSources) ?> income sources
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Receipt Management Section -->
        <div id="receipt-management" class="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Receipt Management</h3>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Organize and track your receipts</p>
                        <p class="text-md font-medium text-gray-900 dark:text-white">Keep digital copies of all your transaction receipts</p>
                    </div>
                </div>

                <?php
                // Get transactions with receipts
                $receiptModel = new Receipt();
                $db = Database::getInstance();
                $user = Session::getUser();
                $userId = $user['id'];

                // Get count of transactions with receipts
                $query = "SELECT COUNT(DISTINCT transaction_id) as count FROM transaction_receipts WHERE user_id = ?";
                $result = $db->fetchOne($query, [$userId]);
                $transactionsWithReceipts = $result ? $result['count'] : 0;

                // Get total receipt count
                $query = "SELECT COUNT(*) as count FROM transaction_receipts WHERE user_id = ?";
                $result = $db->fetchOne($query, [$userId]);
                $totalReceipts = $result ? $result['count'] : 0;

                // Get recent transactions with receipts
                $query = "SELECT tr.*, f.type, f.category, f.date, f.amount
                          FROM transaction_receipts tr
                          JOIN finances f ON tr.transaction_id = f.id
                          WHERE tr.user_id = ?
                          ORDER BY tr.created_at DESC
                          LIMIT 3";
                $recentReceipts = $db->fetchAll($query, [$userId]);
                ?>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div class="bg-blue-50 dark:bg-blue-900 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="rounded-full bg-blue-100 dark:bg-blue-800 p-2 mr-3">
                                <i class="fas fa-receipt text-blue-600 dark:text-blue-400"></i>
                            </div>
                            <div>
                                <p class="text-sm text-blue-700 dark:text-blue-300">Total Receipts</p>
                                <p class="text-xl font-bold text-blue-800 dark:text-blue-200"><?= $totalReceipts ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-green-50 dark:bg-green-900 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="rounded-full bg-green-100 dark:bg-green-800 p-2 mr-3">
                                <i class="fas fa-file-invoice-dollar text-green-600 dark:text-green-400"></i>
                            </div>
                            <div>
                                <p class="text-sm text-green-700 dark:text-green-300">Transactions with Receipts</p>
                                <p class="text-xl font-bold text-green-800 dark:text-green-200"><?= $transactionsWithReceipts ?></p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-purple-50 dark:bg-purple-900 p-4 rounded-lg">
                        <div class="flex items-center">
                            <div class="rounded-full bg-purple-100 dark:bg-purple-800 p-2 mr-3">
                                <i class="fas fa-cloud-upload-alt text-purple-600 dark:text-purple-400"></i>
                            </div>
                            <div>
                                <p class="text-sm text-purple-700 dark:text-purple-300">Upload New Receipt</p>
                                <a href="/momentum/finances/create-transaction" class="text-purple-800 dark:text-purple-200 font-medium hover:underline">Add Transaction with Receipt</a>
                            </div>
                        </div>
                    </div>
                </div>

                <?php if (empty($recentReceipts)): ?>
                    <div class="text-center py-8">
                        <i class="fas fa-receipt text-gray-400 dark:text-gray-500 text-4xl mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No receipts found</p>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                            Start uploading receipts when creating or editing transactions
                        </p>
                        <a href="/momentum/finances/create-transaction" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            <i class="fas fa-plus mr-2"></i> Add Transaction with Receipt
                        </a>
                    </div>
                <?php else: ?>
                    <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Recent Receipts</h4>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Date</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Transaction</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Receipt</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($recentReceipts as $receipt): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            <?= date('M j, Y', strtotime($receipt['date'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm text-gray-900 dark:text-white"><?= View::escape($receipt['category']) ?></div>
                                            <div class="text-sm text-gray-500 dark:text-gray-400">
                                                <?= $receipt['type'] === 'income' ? 'Income' : 'Expense' ?> -
                                                <?= View::formatCurrency($receipt['amount']) ?>
                                            </div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                            <span class="truncate max-w-[150px] inline-block" title="<?= View::escape($receipt['file_name']) ?>">
                                                <?= View::escape($receipt['file_name']) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="/momentum/finances/transactions/<?= $receipt['transaction_id'] ?>/receipts" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>

                    <?php if ($totalReceipts > 3): ?>
                        <div class="mt-4 text-center">
                            <a href="/momentum/finances/receipts" class="inline-flex items-center text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                <i class="fas fa-receipt mr-1"></i> View All Receipts
                            </a>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>

        <!-- Subscription Summary -->
        <div class="mt-6 bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <div class="px-4 py-5 sm:px-6 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Subscription Summary</h3>
            </div>
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <p class="text-sm text-gray-500 dark:text-gray-400">Monthly Cost</p>
                        <p class="text-xl font-semibold text-gray-900 dark:text-white"><?= View::formatCurrency($monthlyCost) ?></p>
                    </div>
                    <a href="/momentum/finances/subscriptions" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-calendar-alt mr-2"></i> Manage Subscriptions
                    </a>
                </div>
                <?php if (empty($subscriptions)): ?>
                    <p class="text-gray-500 dark:text-gray-400 text-center py-4">No active subscriptions</p>
                <?php else: ?>
                    <div class="space-y-4">
                        <?php
                        // Show only first 3 subscriptions
                        $displaySubscriptions = array_slice($subscriptions, 0, 3);
                        foreach ($displaySubscriptions as $subscription):
                        ?>
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white"><?= View::escape($subscription['name']) ?></p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">
                                        Next billing: <?= View::formatDate($subscription['next_billing_date']) ?>
                                    </p>
                                </div>
                                <div class="text-sm font-medium text-gray-900 dark:text-white">
                                    <?= View::formatCurrency($subscription['amount']) ?> / <?= $subscription['billing_cycle'] ?>
                                </div>
                            </div>
                        <?php endforeach; ?>

                        <?php if (count($subscriptions) > 3): ?>
                            <div class="text-center pt-2">
                                <a href="/momentum/finances/subscriptions" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                                    View all <?= count($subscriptions) ?> subscriptions
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Hidden form for transaction deletion -->
<form id="delete-transaction-form" method="POST" style="display: none;">
</form>

<script>
    function deleteTransaction(id) {
        if (confirm('Are you sure you want to delete this transaction?')) {
            const form = document.getElementById('delete-transaction-form');
            form.action = `/momentum/finances/delete-transaction/${id}`;
            form.submit();
        }
    }
</script>
