<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center mb-6">
            <a href="/momentum/finances/budgets" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Create New Budget
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/finances/budgets/create" method="POST" id="budget-form" class="p-6">
                <!-- Display validation errors if any -->
                <?php if (isset($errors) && !empty($errors)): ?>
                    <div class="mb-4 bg-red-50 dark:bg-red-900 p-4 rounded-md">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <i class="fas fa-exclamation-circle text-red-400 dark:text-red-300"></i>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                    Please fix the following errors:
                                </h3>
                                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                    <ul class="list-disc pl-5 space-y-1">
                                        <?php foreach ($errors as $error): ?>
                                            <li><?= $error ?></li>
                                        <?php endforeach; ?>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Budget Details -->
                <div class="space-y-6">
                    <div>
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Budget Details</h2>
                        <div class="grid grid-cols-1 gap-6">
                            <!-- Budget Name -->
                            <div>
                                <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Budget Name <span class="text-red-500">*</span>
                                </label>
                                <input type="text" name="name" id="name" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="e.g., Monthly Budget - June 2023" value="<?= isset($data['name']) ? View::escape($data['name']) : '' ?>" required>
                            </div>

                            <!-- Date Range -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label for="start_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Start Date <span class="text-red-500">*</span>
                                    </label>
                                    <input type="date" name="start_date" id="start_date" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($data['start_date']) ? $data['start_date'] : date('Y-m-01') ?>" required>
                                </div>
                                <div>
                                    <label for="end_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        End Date <span class="text-red-500">*</span>
                                    </label>
                                    <input type="date" name="end_date" id="end_date" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($data['end_date']) ? $data['end_date'] : date('Y-m-t') ?>" required>
                                </div>
                            </div>

                            <!-- Description -->
                            <div>
                                <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Description
                                </label>
                                <textarea name="description" id="description" rows="3" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="Optional description for this budget"><?= isset($data['description']) ? View::escape($data['description']) : '' ?></textarea>
                            </div>

                            <!-- Is Active -->
                            <div class="flex items-center">
                                <input type="checkbox" name="is_active" id="is_active" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-700 dark:bg-gray-700" <?= isset($data['is_active']) && $data['is_active'] ? 'checked' : 'checked' ?>>
                                <label for="is_active" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                    Set as active budget
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Budget Categories -->
                    <div>
                        <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Budget Categories</h2>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">
                            Set spending limits for each category. You can add more categories later.
                        </p>

                        <div id="categories-container" class="space-y-4">
                            <!-- Category template - will be duplicated by JavaScript -->
                            <div class="category-row grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Category
                                    </label>
                                    <select name="categories[]" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                        <option value="">Select a category</option>
                                        <?php foreach ($categories as $category): ?>
                                            <option value="<?= View::escape($category) ?>"><?= View::escape($category) ?></option>
                                        <?php endforeach; ?>
                                        <option value="other">Other (Custom)</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                        Amount (Rs)
                                    </label>
                                    <div class="flex">
                                        <div class="flex-none w-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                                            <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                                        </div>
                                        <input type="number" name="amounts[]" step="0.01" min="0" class="flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="0.00">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4">
                            <button type="button" id="add-category" class="inline-flex items-center px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-plus mr-1"></i> Add Category
                            </button>
                        </div>
                    </div>

                    <!-- Budget Alerts -->
                    <div>
                        <div class="flex items-center">
                            <input type="checkbox" name="enable_alerts" id="enable_alerts" class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded dark:border-gray-700 dark:bg-gray-700" value="1" checked>
                            <label for="enable_alerts" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                                Enable budget alerts (at 80% of budget)
                            </label>
                        </div>
                    </div>

                    <!-- Submit Button -->
                    <div class="flex justify-end">
                        <a href="/momentum/finances/budgets" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200 mr-3">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                            Create Budget
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const categoriesContainer = document.getElementById('categories-container');
        const addCategoryButton = document.getElementById('add-category');
        
        // Add a new category row
        addCategoryButton.addEventListener('click', function() {
            const categoryRow = categoriesContainer.querySelector('.category-row').cloneNode(true);
            
            // Clear input values
            categoryRow.querySelectorAll('input').forEach(input => {
                input.value = '';
            });
            
            categoryRow.querySelectorAll('select').forEach(select => {
                select.selectedIndex = 0;
            });
            
            // Add remove button if it's not the first row
            if (categoriesContainer.querySelectorAll('.category-row').length > 0) {
                const removeButton = document.createElement('button');
                removeButton.type = 'button';
                removeButton.className = 'remove-category text-red-500 hover:text-red-700 ml-2';
                removeButton.innerHTML = '<i class="fas fa-times"></i>';
                removeButton.addEventListener('click', function() {
                    categoryRow.remove();
                });
                
                const amountField = categoryRow.querySelector('input[name="amounts[]"]').parentNode;
                amountField.appendChild(removeButton);
            }
            
            categoriesContainer.appendChild(categoryRow);
        });
        
        // Handle custom category selection
        categoriesContainer.addEventListener('change', function(e) {
            if (e.target.tagName === 'SELECT') {
                const selectedValue = e.target.value;
                if (selectedValue === 'other') {
                    const customCategory = prompt('Enter custom category name:');
                    if (customCategory) {
                        // Create a new option
                        const newOption = document.createElement('option');
                        newOption.value = customCategory;
                        newOption.textContent = customCategory;
                        
                        // Add it to the select and select it
                        e.target.insertBefore(newOption, e.target.options[e.target.options.length - 1]);
                        e.target.value = customCategory;
                    } else {
                        e.target.selectedIndex = 0;
                    }
                }
            }
        });
    });
</script>
