<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8"> <!-- Narrower container for focus -->
        <div class="flex items-center mb-6">
            <a href="/momentum/finances" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-2">
                <i class="fas fa-arrow-left"></i>
            </a>
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                Add New Transaction
            </h1>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg">
            <!-- Progress steps for multi-step form -->
            <div class="border-b border-gray-200 dark:border-gray-700">
                <div class="flex">
                    <button type="button" class="step-btn active" data-step="1">
                        <span class="step-number">1</span>
                        <span class="step-text">Type</span>
                    </button>
                    <button type="button" class="step-btn" data-step="2">
                        <span class="step-number">2</span>
                        <span class="step-text">Details</span>
                    </button>
                    <button type="button" class="step-btn" data-step="3">
                        <span class="step-number">3</span>
                        <span class="step-text">Review</span>
                    </button>
                </div>
            </div>

            <!-- Display validation errors if any -->
            <?php if (isset($errors) && !empty($errors)): ?>
                <div class="p-4 bg-red-50 dark:bg-red-900 border-b border-red-200 dark:border-red-800">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <i class="fas fa-exclamation-circle text-red-400 dark:text-red-300"></i>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                                Please fix the following errors:
                            </h3>
                            <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                                <ul class="list-disc pl-5 space-y-1">
                                    <?php foreach ($errors as $error): ?>
                                        <li><?= $error ?></li>
                                    <?php endforeach; ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <form action="/momentum/finances/create-transaction" method="POST" id="transaction-form" class="p-6" enctype="multipart/form-data">
                <!-- Step 1: Transaction Type -->
                <div class="step-content" id="step-1">
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">What type of transaction is this?</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                            <label class="option-card-container">
                                <input type="radio" name="type_radio" value="expense" class="sr-only" <?= (isset($data['type']) && $data['type'] === 'expense') || !isset($data['type']) ? 'checked' : '' ?>>
                                <div class="option-card <?= (isset($data['type']) && $data['type'] === 'expense') || !isset($data['type']) ? 'selected' : '' ?>" data-value="expense">
                                    <div class="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-red-100 dark:bg-red-900 mr-4">
                                        <i class="fas fa-arrow-up text-red-600 dark:text-red-400"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Expense</h3>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Money going out</p>
                                    </div>
                                </div>
                            </label>
                            <label class="option-card-container">
                                <input type="radio" name="type_radio" value="income" class="sr-only" <?= isset($data['type']) && $data['type'] === 'income' ? 'checked' : '' ?>>
                                <div class="option-card <?= isset($data['type']) && $data['type'] === 'income' ? 'selected' : '' ?>" data-value="income">
                                    <div class="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-green-100 dark:bg-green-900 mr-4">
                                        <i class="fas fa-arrow-down text-green-600 dark:text-green-400"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Income</h3>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Money coming in</p>
                                    </div>
                                </div>
                            </label>
                        </div>
                        <input type="hidden" name="type" id="type-input" value="<?= isset($data['type']) ? $data['type'] : 'expense' ?>" required>
                    </div>

                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">What form does this transaction take?</label>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
                            <label class="option-card-container">
                                <input type="radio" name="transaction_type_radio" value="monetary" class="sr-only" <?= !isset($data['transaction_type']) || $data['transaction_type'] === 'monetary' ? 'checked' : '' ?>>
                                <div class="option-card <?= !isset($data['transaction_type']) || $data['transaction_type'] === 'monetary' ? 'selected' : '' ?>" data-value="monetary">
                                    <div class="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900 mr-4">
                                        <i class="fas fa-money-bill-wave text-blue-600 dark:text-blue-400"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Money</h3>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Cash, card, or bank transfer</p>
                                    </div>
                                </div>
                            </label>
                            <label class="option-card-container">
                                <input type="radio" name="transaction_type_radio" value="non_monetary" class="sr-only" <?= isset($data['transaction_type']) && $data['transaction_type'] === 'non_monetary' ? 'checked' : '' ?>>
                                <div class="option-card <?= isset($data['transaction_type']) && $data['transaction_type'] === 'non_monetary' ? 'selected' : '' ?>" data-value="non_monetary">
                                    <div class="flex-shrink-0 flex items-center justify-center w-10 h-10 rounded-full bg-purple-100 dark:bg-purple-900 mr-4">
                                        <i class="fas fa-exchange-alt text-purple-600 dark:text-purple-400"></i>
                                    </div>
                                    <div>
                                        <h3 class="text-sm font-medium text-gray-900 dark:text-white">Goods/Services</h3>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">Non-monetary exchange</p>
                                    </div>
                                </div>
                            </label>
                        </div>
                        <input type="hidden" name="transaction_type" id="transaction_type-input" value="<?= isset($data['transaction_type']) ? $data['transaction_type'] : 'monetary' ?>" required>
                    </div>

                    <div class="flex justify-end">
                        <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200" onclick="nextStep(2)">Continue</button>
                    </div>
                </div>

                <!-- Step 2: Transaction Details -->
                <div class="step-content hidden" id="step-2">
                    <!-- Monetary Transaction Fields -->
                    <div id="monetary-fields" class="space-y-4 <?= isset($data['transaction_type']) && $data['transaction_type'] === 'non_monetary' ? 'hidden' : '' ?>">
                        <div>
                            <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Amount <span class="text-red-500">*</span>
                            </label>
                            <div class="flex">
                                <div class="flex-none w-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                                    <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                                </div>
                                <input type="number" name="amount" id="amount" step="0.01" min="0.01" class="flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="0.00" value="<?= isset($data['amount']) ? $data['amount'] : '' ?>">
                            </div>
                        </div>

                        <div>
                            <label for="payment_method" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Payment Method <span class="text-red-500">*</span>
                            </label>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
                                <div class="payment-method-option <?= !isset($data['payment_method']) || $data['payment_method'] === 'cash' ? 'selected' : '' ?>" data-value="cash" onclick="selectPaymentMethod('cash')">
                                    <i class="fas fa-money-bill-alt"></i>
                                    <span>Cash</span>
                                </div>
                                <div class="payment-method-option <?= isset($data['payment_method']) && $data['payment_method'] === 'credit_card' ? 'selected' : '' ?>" data-value="credit_card" onclick="selectPaymentMethod('credit_card')">
                                    <i class="fas fa-credit-card"></i>
                                    <span>Credit Card</span>
                                </div>
                                <div class="payment-method-option <?= isset($data['payment_method']) && $data['payment_method'] === 'debit_card' ? 'selected' : '' ?>" data-value="debit_card" onclick="selectPaymentMethod('debit_card')">
                                    <i class="fas fa-credit-card"></i>
                                    <span>Debit Card</span>
                                </div>
                                <div class="payment-method-option <?= isset($data['payment_method']) && $data['payment_method'] === 'bank_transfer' ? 'selected' : '' ?>" data-value="bank_transfer" onclick="selectPaymentMethod('bank_transfer')">
                                    <i class="fas fa-university"></i>
                                    <span>Bank Transfer</span>
                                </div>
                            </div>
                            <input type="hidden" name="payment_method" id="payment_method-input" value="<?= isset($data['payment_method']) ? $data['payment_method'] : 'cash' ?>">
                        </div>
                    </div>

                    <!-- Non-Monetary Transaction Fields -->
                    <div id="non-monetary-fields" class="space-y-4 <?= isset($data['transaction_type']) && $data['transaction_type'] === 'non_monetary' ? '' : 'hidden' ?>">
                        <div>
                            <label for="fair_market_value" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Estimated Value <span class="text-red-500">*</span>
                            </label>
                            <div class="flex">
                                <div class="flex-none w-10 flex items-center justify-center bg-gray-100 dark:bg-gray-600 border border-r-0 border-gray-300 dark:border-gray-700 rounded-l-md">
                                    <span class="text-gray-500 dark:text-gray-400 font-medium">Rs</span>
                                </div>
                                <input type="number" name="fair_market_value" id="fair_market_value" step="0.01" min="0.01" class="flex-grow rounded-none rounded-r-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="0.00" value="<?= isset($data['fair_market_value']) ? $data['fair_market_value'] : '' ?>">
                            </div>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Approximate market value of goods or services</p>
                        </div>

                        <div>
                            <label for="goods_services_description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Description <span class="text-red-500">*</span>
                            </label>
                            <textarea name="goods_services_description" id="goods_services_description" rows="3" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="Describe the goods or services exchanged"><?= isset($data['goods_services_description']) ? View::escape($data['goods_services_description']) : '' ?></textarea>
                        </div>
                    </div>

                    <!-- Common Fields -->
                    <div class="space-y-4 mt-4">
                        <div>
                            <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Category <span class="text-red-500">*</span>
                            </label>
                            <input type="text" name="category" id="category" list="category-list" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($data['category']) ? View::escape($data['category']) : '' ?>" required>
                            <datalist id="category-list">
                                <!-- Basic Categories -->
                                <option value="Food & Dining">
                                <option value="Shopping">
                                <option value="Housing">
                                <option value="Transportation">
                                <option value="Utilities">
                                <option value="Healthcare">
                                <option value="Entertainment">
                                <option value="Personal Care">
                                <option value="Education">
                                <option value="Gifts & Donations">
                                <option value="Investments">
                                <option value="Salary">
                                <option value="Business">

                                <!-- Debt Categories -->
                                <option value="Debt Payment">
                                <option value="Loan Payment">
                                <option value="Credit Card Payment">

                                <!-- Relationship-based Categories -->
                                <option value="Spouse/Partner Payment">
                                <option value="Family Support">
                                <option value="Child Support">
                                <option value="Relative Assistance">
                                <option value="Friend Loan">
                                <option value="Shared Expenses">
                                <option value="Allowance">
                                <option value="Gift to Person">

                                <option value="Other">
                            </datalist>
                        </div>

                        <div>
                            <label for="date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Date <span class="text-red-500">*</span>
                            </label>
                            <input type="date" name="date" id="date" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" value="<?= isset($data['date']) ? View::escape($data['date']) : date('Y-m-d') ?>" required>
                        </div>

                        <div>
                            <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Note (Optional)
                            </label>
                            <input type="text" name="description" id="description" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white" placeholder="Add a short note" value="<?= isset($data['description']) ? View::escape($data['description']) : '' ?>">
                        </div>

                        <div>
                            <label for="receipt" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Receipt (Optional)
                            </label>
                            <div class="mt-1 flex items-center">
                                <input type="file" name="receipt" id="receipt" accept="image/jpeg,image/png,image/gif,application/pdf" class="w-full text-sm text-gray-500 dark:text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary-50 file:text-primary-700 dark:file:bg-primary-900 dark:file:text-primary-300 hover:file:bg-primary-100 dark:hover:file:bg-primary-800">
                            </div>
                            <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                                Accepted formats: JPEG, PNG, GIF, PDF. Maximum size: 5MB.
                            </p>
                        </div>

                        <!-- Income Source Selection (only shown for income transactions) -->
                        <div id="income-source-field" class="<?= isset($data['type']) && $data['type'] === 'income' ? '' : 'hidden' ?>">
                            <label for="income_source_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Income Source (Optional)
                            </label>
                            <select name="income_source_id" id="income_source_id" class="w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                                <option value="">-- Select Income Source --</option>
                                <?php
                                // Get income sources from the controller
                                $incomeSourceModel = new IncomeSource();
                                $user = Session::getUser();
                                $incomeSources = $incomeSourceModel->getUserIncomeSources($user['id']);

                                foreach ($incomeSources as $source):
                                ?>
                                    <option value="<?= $source['id'] ?>" <?= isset($data['income_source_id']) && $data['income_source_id'] == $source['id'] ? 'selected' : '' ?>>
                                        <?= View::escape($source['name']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">Link this transaction to an income source for better tracking</p>
                        </div>
                    </div>

                    <div class="flex justify-between mt-6">
                        <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200" onclick="prevStep(1)">Back</button>
                        <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200" onclick="nextStep(3)">Continue</button>
                    </div>
                </div>

                <!-- Step 3: Review -->
                <div class="step-content hidden" id="step-3">
                    <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Transaction Summary</h3>

                        <div class="space-y-3">
                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Type:</span>
                                <span class="font-medium text-gray-900 dark:text-white" id="review-type"></span>
                            </div>

                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Form:</span>
                                <span class="font-medium text-gray-900 dark:text-white" id="review-transaction-type"></span>
                            </div>

                            <div class="flex justify-between" id="review-amount-row">
                                <span class="text-gray-500 dark:text-gray-400">Amount:</span>
                                <span class="font-medium text-gray-900 dark:text-white" id="review-amount"></span>
                            </div>

                            <div class="flex justify-between" id="review-payment-method-row">
                                <span class="text-gray-500 dark:text-gray-400">Payment Method:</span>
                                <span class="font-medium text-gray-900 dark:text-white" id="review-payment-method"></span>
                            </div>

                            <div class="flex justify-between" id="review-value-row">
                                <span class="text-gray-500 dark:text-gray-400">Estimated Value:</span>
                                <span class="font-medium text-gray-900 dark:text-white" id="review-value"></span>
                            </div>

                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Category:</span>
                                <span class="font-medium text-gray-900 dark:text-white" id="review-category"></span>
                            </div>

                            <div class="flex justify-between">
                                <span class="text-gray-500 dark:text-gray-400">Date:</span>
                                <span class="font-medium text-gray-900 dark:text-white" id="review-date"></span>
                            </div>

                            <div class="flex justify-between" id="review-description-row">
                                <span class="text-gray-500 dark:text-gray-400">Note:</span>
                                <span class="font-medium text-gray-900 dark:text-white" id="review-description"></span>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-between">
                        <button type="button" class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200" onclick="prevStep(2)">Back</button>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">Save Transaction</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
    .option-card-container {
        display: block;
        cursor: pointer;
    }

    .option-card {
        display: flex;
        align-items: center;
        padding: 1rem;
        border: 1px solid;
        border-color: #d1d5db;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.15s;
    }

    .dark .option-card {
        border-color: #374151;
    }

    .option-card:hover {
        background-color: #f9fafb;
    }

    .dark .option-card:hover {
        background-color: #374151;
    }

    .option-card.selected {
        border-color: #0ea5e9;
        box-shadow: 0 0 0 2px #0ea5e9;
    }

    /* Style for radio buttons */
    .option-card-container input[type="radio"]:checked + .option-card {
        border-color: #0ea5e9;
        box-shadow: 0 0 0 2px #0ea5e9;
    }

    .payment-method-option {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        padding: 0.75rem;
        border: 1px solid;
        border-color: #d1d5db;
        border-radius: 0.5rem;
        cursor: pointer;
        transition: all 0.15s;
        color: #4b5563;
    }

    .dark .payment-method-option {
        border-color: #374151;
        color: #d1d5db;
    }

    .payment-method-option:hover {
        background-color: #f9fafb;
    }

    .dark .payment-method-option:hover {
        background-color: #374151;
    }

    .payment-method-option.selected {
        border-color: #0ea5e9;
        background-color: #e0f2fe;
        color: #0369a1;
    }

    .dark .payment-method-option.selected {
        background-color: #0c4a6e;
        color: #7dd3fc;
    }

    .payment-method-option i {
        font-size: 1.25rem;
        margin-bottom: 0.25rem;
    }

    .payment-method-option span {
        font-size: 0.75rem;
    }

    .step-btn {
        flex: 1;
        padding: 0.75rem 1rem;
        text-align: center;
        border-bottom: 2px solid transparent;
        font-size: 0.875rem;
        font-weight: 500;
        color: #6b7280;
    }

    .dark .step-btn {
        color: #9ca3af;
    }

    .step-btn:hover {
        color: #374151;
        border-color: #d1d5db;
    }

    .dark .step-btn:hover {
        color: #e5e7eb;
        border-color: #4b5563;
    }

    .step-btn:focus {
        outline: none;
    }

    .step-btn.active {
        border-color: #0ea5e9;
        color: #0284c7;
    }

    .dark .step-btn.active {
        color: #38bdf8;
    }

    .step-number {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        width: 1.5rem;
        height: 1.5rem;
        border-radius: 9999px;
        background-color: #e5e7eb;
        color: #4b5563;
        margin-right: 0.5rem;
    }

    .dark .step-number {
        background-color: #374151;
        color: #d1d5db;
    }

    .step-btn.active .step-number {
        background-color: #e0f2fe;
        color: #0284c7;
    }

    .dark .step-btn.active .step-number {
        background-color: #0c4a6e;
        color: #7dd3fc;
    }
</style>

<script>
    // Multi-step form navigation
    function nextStep(step) {
        // Validate current step
        if (step === 2) {
            const type = document.getElementById('type-input').value;
            const transactionType = document.getElementById('transaction_type-input').value;

            if (!type || !transactionType) {
                alert('Please select both transaction type and form');
                return;
            }

            // Show/hide appropriate fields based on transaction type
            const monetaryFields = document.getElementById('monetary-fields');
            const nonMonetaryFields = document.getElementById('non-monetary-fields');

            if (transactionType === 'monetary') {
                monetaryFields.classList.remove('hidden');
                nonMonetaryFields.classList.add('hidden');
            } else {
                monetaryFields.classList.add('hidden');
                nonMonetaryFields.classList.remove('hidden');
            }
        } else if (step === 3) {
            // Validate step 2
            const transactionType = document.getElementById('transaction_type-input').value;
            let valid = true;

            if (transactionType === 'monetary') {
                const amount = document.getElementById('amount').value;
                const paymentMethod = document.getElementById('payment_method-input').value;

                if (!amount || amount <= 0) {
                    alert('Please enter a valid amount');
                    valid = false;
                }

                if (!paymentMethod) {
                    alert('Please select a payment method');
                    valid = false;
                }
            } else {
                const value = document.getElementById('fair_market_value').value;
                const description = document.getElementById('goods_services_description').value;

                if (!value || value <= 0) {
                    alert('Please enter a valid estimated value');
                    valid = false;
                }

                if (!description) {
                    alert('Please provide a description of the goods or services');
                    valid = false;
                }
            }

            const category = document.getElementById('category').value;
            const date = document.getElementById('date').value;

            if (!category) {
                alert('Please select or enter a category');
                valid = false;
            }

            if (!date) {
                alert('Please select a date');
                valid = false;
            }

            if (!valid) return;

            // Update review screen
            updateReview();
        }

        // Hide all steps
        document.querySelectorAll('.step-content').forEach(el => {
            el.classList.add('hidden');
        });

        // Show target step
        document.getElementById(`step-${step}`).classList.remove('hidden');

        // Update step buttons
        document.querySelectorAll('.step-btn').forEach(el => {
            el.classList.remove('active');
        });

        document.querySelector(`.step-btn[data-step="${step}"]`).classList.add('active');
    }

    function prevStep(step) {
        nextStep(step); // Reuse the same function
    }

    // Selection functions
    function selectOption(field, value, element) {
        // This function is kept for backward compatibility
        // The radio buttons now handle the selection
        document.getElementById(`${field}-input`).value = value;
    }

    function selectPaymentMethod(method) {
        // Clear previous selections
        document.querySelectorAll('.payment-method-option').forEach(el => {
            el.classList.remove('selected');
        });

        // Set new selection
        document.querySelector(`.payment-method-option[data-value="${method}"]`).classList.add('selected');
        document.getElementById('payment_method-input').value = method;
    }

    // Update review screen
    function updateReview() {
        const type = document.getElementById('type-input').value;
        const transactionType = document.getElementById('transaction_type-input').value;
        const category = document.getElementById('category').value;
        const date = document.getElementById('date').value;
        const description = document.getElementById('description').value;

        // Set review values
        document.getElementById('review-type').textContent = type === 'income' ? 'Income' : 'Expense';
        document.getElementById('review-transaction-type').textContent = transactionType === 'monetary' ? 'Money' : 'Goods/Services';
        document.getElementById('review-category').textContent = category;
        document.getElementById('review-date').textContent = formatDate(date);

        // Show/hide fields based on transaction type
        if (transactionType === 'monetary') {
            const amount = document.getElementById('amount').value;
            const paymentMethod = document.getElementById('payment_method-input').value;

            document.getElementById('review-amount').textContent = `Rs ${parseFloat(amount).toFixed(2)}`;
            document.getElementById('review-payment-method').textContent = paymentMethod.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

            document.getElementById('review-amount-row').classList.remove('hidden');
            document.getElementById('review-payment-method-row').classList.remove('hidden');
            document.getElementById('review-value-row').classList.add('hidden');
        } else {
            const value = document.getElementById('fair_market_value').value;

            document.getElementById('review-value').textContent = `Rs ${parseFloat(value).toFixed(2)}`;

            document.getElementById('review-amount-row').classList.add('hidden');
            document.getElementById('review-payment-method-row').classList.add('hidden');
            document.getElementById('review-value-row').classList.remove('hidden');
        }

        // Description
        if (description) {
            document.getElementById('review-description').textContent = description;
            document.getElementById('review-description-row').classList.remove('hidden');
        } else {
            document.getElementById('review-description-row').classList.add('hidden');
        }
    }

    // Format date for display
    function formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
    }

    // Add event listeners for radio buttons when the DOM is loaded
    document.addEventListener('DOMContentLoaded', function() {
        // Handle transaction type radio buttons
        const typeRadios = document.querySelectorAll('input[name="type_radio"]');
        typeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // Update the hidden input
                document.getElementById('type-input').value = this.value;

                // Update visual selection
                document.querySelectorAll('.option-card[data-value]').forEach(el => {
                    if (el.dataset.value === 'expense' || el.dataset.value === 'income') {
                        el.classList.remove('selected');
                        if (el.dataset.value === this.value) {
                            el.classList.add('selected');
                        }
                    }
                });

                // Show/hide income source field based on transaction type
                const incomeSourceField = document.getElementById('income-source-field');
                if (this.value === 'income') {
                    incomeSourceField.classList.remove('hidden');
                } else {
                    incomeSourceField.classList.add('hidden');
                }
            });
        });

        // Handle transaction form radio buttons
        const transactionTypeRadios = document.querySelectorAll('input[name="transaction_type_radio"]');
        transactionTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                // Update the hidden input
                document.getElementById('transaction_type-input').value = this.value;

                // Update visual selection
                document.querySelectorAll('.option-card[data-value]').forEach(el => {
                    if (el.dataset.value === 'monetary' || el.dataset.value === 'non_monetary') {
                        el.classList.remove('selected');
                        if (el.dataset.value === this.value) {
                            el.classList.add('selected');
                        }
                    }
                });
            });
        });
    });
</script>
