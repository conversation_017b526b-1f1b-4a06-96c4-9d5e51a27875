<?php
/**
 * YouTube Links Collection View
 */

$title = $collection['name'] . ' - YouTube Links';
$stylesheets = ['/momentum/css/youtube-links.css'];
$scripts = ['/momentum/js/youtube-links.js'];

ob_start();
?>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- Header -->
    <div class="bg-white dark:bg-gray-800 shadow">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center">
                    <a href="/momentum/tools/youtube-links" class="text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 mr-4">
                        <i class="fas fa-arrow-left"></i>
                    </a>
                    <div class="flex items-center">
                        <i class="<?= htmlspecialchars($collection['icon']) ?> text-2xl mr-3" style="color: <?= htmlspecialchars($collection['color']) ?>"></i>
                        <div>
                            <h1 class="text-2xl font-bold text-gray-900 dark:text-white"><?= htmlspecialchars($collection['name']) ?></h1>
                            <p class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($collection['description']) ?></p>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-3">
                    <button onclick="openQuickAddModal()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg flex items-center">
                        <i class="fas fa-plus mr-2"></i>
                        Add Link
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Collection Stats -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-blue-100 dark:bg-blue-900">
                        <i class="fas fa-link text-blue-600 dark:text-blue-400"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Total Links</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= $collection['total_links'] ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-yellow-100 dark:bg-yellow-900">
                        <i class="fas fa-clock text-yellow-600 dark:text-yellow-400"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">To Watch</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= $collection['to_watch_count'] ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-green-100 dark:bg-green-900">
                        <i class="fas fa-check text-green-600 dark:text-green-400"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">Completed</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= $collection['completed_count'] ?></p>
                    </div>
                </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center">
                    <div class="p-3 rounded-full bg-red-100 dark:bg-red-900">
                        <i class="fas fa-exclamation text-red-600 dark:text-red-400"></i>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500 dark:text-gray-400">High Priority</p>
                        <p class="text-2xl font-semibold text-gray-900 dark:text-white"><?= $collection['high_priority_count'] ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Links List -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white">Videos in Collection</h3>
                    <div class="flex space-x-2">
                        <button onclick="toggleBulkMode()" class="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">
                            Bulk Actions
                        </button>
                    </div>
                </div>
            </div>
            <div class="p-6">
                <?php if (empty($links)): ?>
                    <div class="text-center py-8">
                        <i class="fab fa-youtube text-gray-300 dark:text-gray-600 text-6xl mb-4"></i>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">No videos in this collection yet</p>
                        <button onclick="openQuickAddModal()" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg">
                            Add Your First Video
                        </button>
                    </div>
                <?php else: ?>
                    <div class="space-y-4" id="links-container">
                        <?php foreach ($links as $link): ?>
                            <div class="link-item border border-gray-200 dark:border-gray-700 rounded-lg p-4 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors" data-link-id="<?= $link['id'] ?>">
                                <div class="flex items-start space-x-4">
                                    <div class="bulk-checkbox hidden">
                                        <input type="checkbox" class="link-checkbox" value="<?= $link['id'] ?>">
                                    </div>
                                    
                                    <?php if ($link['thumbnail_url']): ?>
                                        <img src="<?= htmlspecialchars($link['thumbnail_url']) ?>" 
                                             alt="Thumbnail" 
                                             class="w-32 h-20 object-cover rounded">
                                    <?php else: ?>
                                        <div class="w-32 h-20 bg-gray-200 dark:bg-gray-600 rounded flex items-center justify-center">
                                            <i class="fab fa-youtube text-gray-400 text-3xl"></i>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="flex-1 min-w-0">
                                        <div class="flex items-start justify-between">
                                            <div class="flex-1">
                                                <h4 class="text-lg font-medium text-gray-900 dark:text-white">
                                                    <?= htmlspecialchars($link['title'] ?: 'Untitled Video') ?>
                                                </h4>
                                                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                                    <?= htmlspecialchars($link['channel_name'] ?: 'Unknown Channel') ?>
                                                    <?php if ($link['duration']): ?>
                                                        • <?= htmlspecialchars($link['duration']) ?>
                                                    <?php endif; ?>
                                                </p>
                                                
                                                <?php if ($link['personal_notes']): ?>
                                                    <p class="text-sm text-gray-600 dark:text-gray-300 mt-2">
                                                        <i class="fas fa-sticky-note mr-1"></i>
                                                        <?= htmlspecialchars($link['personal_notes']) ?>
                                                    </p>
                                                <?php endif; ?>
                                                
                                                <?php if (!empty($link['tags'])): ?>
                                                    <div class="flex flex-wrap gap-1 mt-2">
                                                        <?php foreach ($link['tags'] as $tag): ?>
                                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                                <?= htmlspecialchars($tag) ?>
                                                            </span>
                                                        <?php endforeach; ?>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <div class="flex items-center space-x-2 ml-4">
                                                <!-- Priority Badge -->
                                                <?php if ($link['priority'] === 'high'): ?>
                                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                        <i class="fas fa-exclamation mr-1"></i>
                                                        High
                                                    </span>
                                                <?php endif; ?>
                                                
                                                <!-- Status Badge -->
                                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium 
                                                    <?php if ($link['status'] === 'completed'): ?>
                                                        bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                                    <?php elseif ($link['status'] === 'watching'): ?>
                                                        bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200
                                                    <?php else: ?>
                                                        bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200
                                                    <?php endif; ?>">
                                                    <?= ucfirst(str_replace('_', ' ', $link['status'])) ?>
                                                </span>
                                                
                                                <!-- Actions -->
                                                <div class="flex space-x-1">
                                                    <a href="<?= htmlspecialchars($link['url']) ?>" 
                                                       target="_blank" 
                                                       class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 p-2"
                                                       title="Watch Video">
                                                        <i class="fas fa-external-link-alt"></i>
                                                    </a>
                                                    <button onclick="updateLinkStatus(<?= $link['id'] ?>, 'completed')" 
                                                            class="text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300 p-2"
                                                            title="Mark as Completed">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                    
                    <!-- Bulk Actions Bar (Hidden by default) -->
                    <div id="bulk-actions-bar" class="hidden mt-4 p-4 bg-blue-50 dark:bg-blue-900 rounded-lg">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-blue-800 dark:text-blue-200">
                                <span id="selected-count">0</span> items selected
                            </span>
                            <div class="flex space-x-2">
                                <button onclick="bulkAction('mark_completed')" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                                    Mark Completed
                                </button>
                                <button onclick="bulkAction('mark_to_watch')" class="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1 rounded text-sm">
                                    Mark To Watch
                                </button>
                                <button onclick="bulkAction('set_high_priority')" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                                    High Priority
                                </button>
                                <button onclick="cancelBulkMode()" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-1 rounded text-sm">
                                    Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<!-- Quick Add Modal -->
<div id="quickAddModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">Add YouTube Link</h3>
            </div>
            <form id="quickAddForm" class="p-6">
                <input type="hidden" id="quickCollectionId" value="<?= $collection['id'] ?>">
                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">YouTube URL</label>
                        <input type="url" id="quickUrl" required 
                               class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white"
                               placeholder="https://www.youtube.com/watch?v=...">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Priority</label>
                        <select id="quickPriority" 
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 dark:bg-gray-700 dark:text-white">
                            <option value="low">Low</option>
                            <option value="medium" selected>Medium</option>
                            <option value="high">High</option>
                        </select>
                    </div>
                </div>
                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="closeQuickAddModal()" 
                            class="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 hover:bg-gray-200 dark:hover:bg-gray-500 rounded-md">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md">
                        Add Link
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Override the collection ID for quick add
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('quickAddForm');
    if (form) {
        form.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const url = document.getElementById('quickUrl').value;
            const priority = document.getElementById('quickPriority').value;
            const collectionId = document.getElementById('quickCollectionId').value;

            if (!url) {
                alert('Please enter a YouTube URL');
                return;
            }

            try {
                const response = await fetch('/momentum/tools/youtube-links/quick-add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        url: url,
                        priority: priority,
                        collection_id: collectionId
                    })
                });

                const result = await response.json();

                if (result.success) {
                    closeQuickAddModal();
                    // Refresh the page to show the new link
                    window.location.reload();
                } else {
                    alert(result.message || 'Failed to add link');
                }
            } catch (error) {
                console.error('Error adding link:', error);
                alert('An error occurred while adding the link');
            }
        });
    }
});
</script>

<?php
$content = ob_get_clean();
include dirname(__DIR__, 2) . '/layouts/default.php';
?>
