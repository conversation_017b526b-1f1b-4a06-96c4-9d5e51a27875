<?php
/**
 * Capture Detail View
 */
?>

<div class="quick-capture-container">
    <div class="max-w-4xl mx-auto">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
            <div class="flex items-center">
                <a href="/momentum/quick-capture" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-300 mr-4">
                    <i class="fas fa-arrow-left"></i>
                </a>
                <div>
                    <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                        <?= htmlspecialchars($capture['title'] ?: ucfirst($capture['type']) . ' Capture') ?>
                    </h1>
                    <p class="text-gray-600 dark:text-gray-400 mt-1">
                        <?= ucfirst($capture['type']) ?> • Created <?= date('M j, Y \a\t g:i A', strtotime($capture['created_at'])) ?>
                    </p>
                </div>
            </div>
            
            <div class="flex items-center space-x-3">
                <button onclick="togglePin(<?= $capture['id'] ?>)" class="capture-action-btn <?= $capture['is_pinned'] ? 'pin' : '' ?>" title="<?= $capture['is_pinned'] ? 'Unpin' : 'Pin' ?>">
                    <i class="fas fa-thumbtack"></i>
                </button>
                <a href="/momentum/quick-capture/edit/<?= $capture['id'] ?>" class="capture-action-btn" title="Edit">
                    <i class="fas fa-edit"></i>
                </a>
                <button onclick="deleteCapture(<?= $capture['id'] ?>)" class="capture-action-btn delete" title="Delete">
                    <i class="fas fa-trash"></i>
                </button>
            </div>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Content Area -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    
                    <!-- Screenshot Display -->
                    <?php if ($capture['type'] === 'screenshot' && $capture['file_path']): ?>
                        <div class="mb-6">
                            <img src="<?= htmlspecialchars($capture['file_path']) ?>" 
                                 alt="Screenshot" 
                                 class="w-full rounded-lg border border-gray-200 dark:border-gray-700">
                        </div>
                    <?php endif; ?>

                    <!-- Content -->
                    <?php if ($capture['content']): ?>
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Content</h3>
                            <div class="prose dark:prose-invert max-w-none">
                                <?= nl2br(htmlspecialchars($capture['content'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- OCR Text -->
                    <?php if ($capture['ocr_text']): ?>
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Extracted Text (OCR)</h3>
                            <div class="ocr-results">
                                <div class="ocr-text"><?= htmlspecialchars($capture['ocr_text']) ?></div>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- Voice Player -->
                    <?php if ($capture['type'] === 'voice' && $capture['file_path']): ?>
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Audio Recording</h3>
                            <audio controls class="w-full">
                                <source src="<?= htmlspecialchars($capture['file_path']) ?>" type="audio/wav">
                                Your browser does not support the audio element.
                            </audio>
                        </div>
                    <?php endif; ?>

                    <!-- Metadata -->
                    <?php if (!empty($metadata)): ?>
                        <div class="mb-6">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Details</h3>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <dl class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                    <?php if (!empty($metadata['original_name'])): ?>
                                        <div>
                                            <dt class="font-medium text-gray-700 dark:text-gray-300">Original Name</dt>
                                            <dd class="text-gray-600 dark:text-gray-400"><?= htmlspecialchars($metadata['original_name']) ?></dd>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($capture['file_size']): ?>
                                        <div>
                                            <dt class="font-medium text-gray-700 dark:text-gray-300">File Size</dt>
                                            <dd class="text-gray-600 dark:text-gray-400"><?= formatFileSize($capture['file_size']) ?></dd>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php if ($capture['file_type']): ?>
                                        <div>
                                            <dt class="font-medium text-gray-700 dark:text-gray-300">File Type</dt>
                                            <dd class="text-gray-600 dark:text-gray-400"><?= htmlspecialchars($capture['file_type']) ?></dd>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div>
                                        <dt class="font-medium text-gray-700 dark:text-gray-300">Created</dt>
                                        <dd class="text-gray-600 dark:text-gray-400"><?= date('M j, Y \a\t g:i A', strtotime($capture['created_at'])) ?></dd>
                                    </div>
                                    
                                    <?php if ($capture['updated_at'] !== $capture['created_at']): ?>
                                        <div>
                                            <dt class="font-medium text-gray-700 dark:text-gray-300">Last Updated</dt>
                                            <dd class="text-gray-600 dark:text-gray-400"><?= date('M j, Y \a\t g:i A', strtotime($capture['updated_at'])) ?></dd>
                                        </div>
                                    <?php endif; ?>
                                </dl>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-6">
                <!-- Tags and Category -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Organization</h3>
                    
                    <!-- Category -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Category</label>
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <?= $capture['category'] ? htmlspecialchars($capture['category']) : 'Uncategorized' ?>
                        </div>
                    </div>

                    <!-- Tags -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Tags</label>
                        <?php if ($capture['tags']): ?>
                            <div class="flex flex-wrap gap-2">
                                <?php foreach (explode(',', $capture['tags']) as $tag): ?>
                                    <span class="capture-tag"><?= htmlspecialchars(trim($tag)) ?></span>
                                <?php endforeach; ?>
                            </div>
                        <?php else: ?>
                            <div class="text-sm text-gray-500 dark:text-gray-400">No tags</div>
                        <?php endif; ?>
                    </div>

                    <!-- Status -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Status</label>
                        <div class="flex items-center">
                            <?php if ($capture['is_pinned']): ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                                    <i class="fas fa-thumbtack mr-1"></i>
                                    Pinned
                                </span>
                            <?php else: ?>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                    Normal
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <!-- Linked Items -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Linked Items</h3>
                    
                    <?php if ($capture['linked_prompt_id'] || $capture['linked_task_id'] || $capture['linked_project_id']): ?>
                        <div class="space-y-3">
                            <?php if ($capture['linked_prompt_id']): ?>
                                <div class="flex items-center p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                                    <i class="fas fa-brain text-purple-600 dark:text-purple-400 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-purple-900 dark:text-purple-100">Linked to AI Prompt</p>
                                        <a href="/momentum/ai-prompts/view/<?= $capture['linked_prompt_id'] ?>" class="text-xs text-purple-600 dark:text-purple-400 hover:text-purple-800 dark:hover:text-purple-300">View Prompt</a>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($capture['linked_task_id']): ?>
                                <div class="flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                                    <i class="fas fa-tasks text-blue-600 dark:text-blue-400 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-blue-900 dark:text-blue-100">Linked to Task</p>
                                        <a href="/momentum/tasks/view/<?= $capture['linked_task_id'] ?>" class="text-xs text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300">View Task</a>
                                    </div>
                                </div>
                            <?php endif; ?>
                            
                            <?php if ($capture['linked_project_id']): ?>
                                <div class="flex items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                                    <i class="fas fa-project-diagram text-green-600 dark:text-green-400 mr-3"></i>
                                    <div>
                                        <p class="text-sm font-medium text-green-900 dark:text-green-100">Linked to Project</p>
                                        <a href="/momentum/projects/view/<?= $capture['linked_project_id'] ?>" class="text-xs text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-300">View Project</a>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-sm text-gray-500 dark:text-gray-400 mb-4">No linked items</p>
                        <div class="space-y-2">
                            <button class="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200">
                                <i class="fas fa-brain text-purple-600 dark:text-purple-400 mr-2"></i>
                                Link to AI Prompt
                            </button>
                            <button class="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200">
                                <i class="fas fa-tasks text-blue-600 dark:text-blue-400 mr-2"></i>
                                Link to Task
                            </button>
                            <button class="w-full text-left px-3 py-2 text-sm bg-gray-50 dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 rounded-lg transition-colors duration-200">
                                <i class="fas fa-project-diagram text-green-600 dark:text-green-400 mr-2"></i>
                                Link to Project
                            </button>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Actions -->
                <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">Actions</h3>
                    
                    <div class="space-y-2">
                        <a href="/momentum/quick-capture/edit/<?= $capture['id'] ?>" class="w-full inline-flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Capture
                        </a>
                        
                        <?php if ($capture['file_path']): ?>
                            <a href="/momentum/quick-capture/download/<?= $capture['id'] ?>" class="w-full inline-flex items-center justify-center px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors duration-200">
                                <i class="fas fa-download mr-2"></i>
                                Download File
                            </a>
                        <?php endif; ?>
                        
                        <button onclick="shareCapture(<?= $capture['id'] ?>)" class="w-full inline-flex items-center justify-center px-4 py-2 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-share mr-2"></i>
                            Share
                        </button>
                        
                        <button onclick="deleteCapture(<?= $capture['id'] ?>)" class="w-full inline-flex items-center justify-center px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors duration-200">
                            <i class="fas fa-trash mr-2"></i>
                            Delete
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePin(captureId) {
    fetch(`/momentum/quick-capture/toggle-pin/${captureId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert('Failed to toggle pin: ' + (data.error || 'Unknown error'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Failed to toggle pin');
    });
}

function deleteCapture(captureId) {
    if (confirm('Are you sure you want to delete this capture? This action cannot be undone.')) {
        fetch(`/momentum/quick-capture/delete/${captureId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = '/momentum/quick-capture';
            } else {
                alert('Failed to delete capture: ' + (data.error || 'Unknown error'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Failed to delete capture');
        });
    }
}

function shareCapture(captureId) {
    // Placeholder for share functionality
    alert('Share functionality coming soon!');
}

// Helper function for file size formatting
<?php
function formatFileSize($bytes) {
    if ($bytes >= 1073741824) {
        return number_format($bytes / 1073741824, 2) . ' GB';
    } elseif ($bytes >= 1048576) {
        return number_format($bytes / 1048576, 2) . ' MB';
    } elseif ($bytes >= 1024) {
        return number_format($bytes / 1024, 2) . ' KB';
    } else {
        return $bytes . ' bytes';
    }
}
?>
</script>
