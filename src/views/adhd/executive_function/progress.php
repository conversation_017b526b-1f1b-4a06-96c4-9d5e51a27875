<?php
/**
 * Executive Function Progress View
 *
 * Displays user's progress in executive function exercises
 */
?>

<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Back navigation -->
        <div class="mb-6">
            <a href="/momentum/adhd/executive-function" class="inline-flex items-center text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Exercises
            </a>
        </div>

        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">
                <i class="fas fa-chart-line text-primary-600 dark:text-primary-400 mr-2"></i> Executive Function Progress
            </h1>
            <div class="flex flex-wrap gap-2">
                <a href="/momentum/adhd" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-1"></i> Back to ADHD Dashboard
                </a>
            </div>
        </div>

        <!-- Progress Overview -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-chart-bar text-primary-600 dark:text-primary-400 mr-2"></i> Executive Function Skills Overview
                </h2>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (!empty($progressByCategory)): ?>
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php
                        $categoryIcons = [
                            'working_memory' => 'fas fa-memory',
                            'task_initiation' => 'fas fa-play',
                            'planning' => 'fas fa-clipboard-list',
                            'organization' => 'fas fa-folder',
                            'time_management' => 'fas fa-clock',
                            'emotional_regulation' => 'fas fa-heart'
                        ];

                        $categoryColors = [
                            'working_memory' => 'blue',
                            'task_initiation' => 'green',
                            'planning' => 'purple',
                            'organization' => 'indigo',
                            'time_management' => 'yellow',
                            'emotional_regulation' => 'pink'
                        ];

                        foreach ($progressByCategory as $progress):
                            $category = $progress['category'];
                            $icon = $categoryIcons[$category] ?? 'fas fa-brain';
                            $color = $categoryColors[$category] ?? 'gray';
                        ?>
                            <div class="bg-white dark:bg-gray-700 shadow rounded-lg overflow-hidden">
                                <div class="px-4 py-5 sm:p-6">
                                    <div class="flex items-center">
                                        <div class="flex-shrink-0 bg-<?= $color ?>-100 dark:bg-<?= $color ?>-800 rounded-md p-3">
                                            <i class="<?= $icon ?> text-<?= $color ?>-600 dark:text-<?= $color ?>-300 text-xl"></i>
                                        </div>
                                        <div class="ml-5 w-0 flex-1">
                                            <dl>
                                                <dt class="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                                                    <?= ucfirst(str_replace('_', ' ', $category)) ?>
                                                </dt>
                                                <dd>
                                                    <div class="flex items-center">
                                                        <div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5 mr-2">
                                                            <div class="bg-<?= $color ?>-600 h-2.5 rounded-full" style="width: <?= ($progress['avg_score'] * 10) ?>%"></div>
                                                        </div>
                                                        <span class="text-sm font-medium text-gray-900 dark:text-white"><?= number_format($progress['avg_score'], 1) ?>/10</span>
                                                    </div>
                                                </dd>
                                            </dl>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php else: ?>
                    <div class="text-center py-6">
                        <p class="text-gray-500 dark:text-gray-400">No progress data available yet. Complete some exercises to see your progress.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Progress Over Time Chart -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-chart-line text-primary-600 dark:text-primary-400 mr-2"></i> Progress Over Time
                </h2>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700 px-4 py-5 sm:p-6">
                <?php if (!empty($progressOverTime)): ?>
                    <div id="progress-chart" class="h-80"></div>
                <?php else: ?>
                    <div class="text-center py-6">
                        <p class="text-gray-500 dark:text-gray-400">No progress data available yet. Complete some exercises to see your progress over time.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Exercise Completion -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden mb-6">
            <div class="px-4 py-5 sm:px-6">
                <h2 class="text-lg font-medium text-gray-900 dark:text-white">
                    <i class="fas fa-tasks text-primary-600 dark:text-primary-400 mr-2"></i> Exercise Completion
                </h2>
            </div>
            <div class="border-t border-gray-200 dark:border-gray-700">
                <?php if (!empty($exerciseCounts)): ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Category</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Completed</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Total Available</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Completion Rate</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($exerciseCounts as $count): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                                            <?= ucfirst(str_replace('_', ' ', $count['category'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= $count['completed_exercises'] ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= $count['total_exercises'] ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <div class="flex items-center">
                                                <div class="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                                                    <div class="bg-blue-600 h-2.5 rounded-full" style="width: <?= ($count['completed_exercises'] / $count['total_exercises'] * 100) ?>%"></div>
                                                </div>
                                                <span class="ml-2"><?= round($count['completed_exercises'] / $count['total_exercises'] * 100) ?>%</span>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php else: ?>
                    <div class="text-center py-6">
                        <p class="text-gray-500 dark:text-gray-400">No exercise completion data available yet.</p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row sm:justify-between gap-3">
            <a href="/momentum/adhd/executive-function" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-brain mr-1"></i> Back to Exercises
            </a>
            <a href="/momentum/adhd" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-home mr-1"></i> ADHD Dashboard
            </a>
        </div>
    </div>
</div>

<?php if (!empty($progressOverTime)): ?>
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Progress Over Time Chart
    const progressData = <?= json_encode($progressOverTime) ?>;

    // Process data for chart
    const categories = ['working_memory', 'task_initiation', 'planning', 'organization', 'time_management', 'emotional_regulation'];
    const categoryNames = {
        'working_memory': 'Working Memory',
        'task_initiation': 'Task Initiation',
        'planning': 'Planning',
        'organization': 'Organization',
        'time_management': 'Time Management',
        'emotional_regulation': 'Emotional Regulation'
    };

    // Group data by date
    const dateGroups = {};
    progressData.forEach(item => {
        if (!dateGroups[item.date]) {
            dateGroups[item.date] = {};
        }
        dateGroups[item.date][item.category] = item.score;
    });

    // Convert to series format
    const dates = Object.keys(dateGroups).sort();
    const series = categories.map(category => {
        return {
            name: categoryNames[category] || category,
            data: dates.map(date => dateGroups[date][category] || null)
        };
    });

    // Chart options
    const options = {
        series: series,
        chart: {
            type: 'line',
            height: 350,
            fontFamily: 'Inter, system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
            toolbar: {
                show: false
            },
            background: 'transparent'
        },
        dataLabels: {
            enabled: false
        },
        stroke: {
            curve: 'smooth',
            width: 2
        },
        xaxis: {
            categories: dates.map(date => {
                const d = new Date(date);
                return d.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
            }),
            labels: {
                style: {
                    colors: '#6B7280'
                }
            }
        },
        yaxis: {
            min: 0,
            max: 10,
            title: {
                text: 'Score',
                style: {
                    color: '#6B7280'
                }
            },
            labels: {
                style: {
                    colors: '#6B7280'
                }
            }
        },
        tooltip: {
            y: {
                formatter: function(val) {
                    return val + "/10";
                }
            }
        },
        legend: {
            position: 'top',
            horizontalAlign: 'center',
            labels: {
                colors: '#6B7280'
            }
        },
        grid: {
            borderColor: '#E5E7EB',
            row: {
                colors: ['transparent', 'transparent']
            }
        },
        theme: {
            mode: document.documentElement.classList.contains('dark') ? 'dark' : 'light'
        }
    };

    const chart = new ApexCharts(document.querySelector("#progress-chart"), options);
    chart.render();
});
</script>
<?php endif; ?>
