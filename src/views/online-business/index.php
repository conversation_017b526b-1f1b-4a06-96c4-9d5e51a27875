<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Page Header -->
        <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-6 gap-4">
            <div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white">Online Business Dashboard</h1>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                    Monitor and manage your online business ventures
                </p>
            </div>

            <div class="flex flex-wrap items-center gap-3">
                <a href="/momentum/online-business/create" class="inline-flex items-center px-3 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-1.5"></i> New Venture
                </a>
                <a href="/momentum/help/online-business-features" class="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-question-circle mr-1.5"></i> Help
                </a>
            </div>
        </div>

        <!-- Performance Overview -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-6">
            <div class="px-5 py-5">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-chart-line text-cyan-500 mr-2"></i> Performance Overview
                    </h2>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                        <span>Last updated: <?= date('M d, Y H:i') ?></span>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <!-- Revenue Metric -->
                    <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Revenue</p>
                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white">$<?= number_format($performanceMetrics['revenue']['current_month'], 2) ?></h3>
                            </div>
                            <div class="<?= $performanceMetrics['revenue']['growth'] >= 0 ? 'text-green-500' : 'text-red-500' ?> flex items-center">
                                <?php if ($performanceMetrics['revenue']['growth'] >= 0): ?>
                                    <i class="fas fa-arrow-up mr-1"></i>
                                <?php else: ?>
                                    <i class="fas fa-arrow-down mr-1"></i>
                                <?php endif; ?>
                                <span><?= abs(number_format($performanceMetrics['revenue']['growth'], 1)) ?>%</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-xs text-gray-500 dark:text-gray-400">vs. previous month: $<?= number_format($performanceMetrics['revenue']['previous_month'], 2) ?></p>
                        </div>
                    </div>

                    <!-- Traffic Metric -->
                    <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Traffic</p>
                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($performanceMetrics['traffic']['current_month']) ?></h3>
                            </div>
                            <div class="<?= $performanceMetrics['traffic']['growth'] >= 0 ? 'text-green-500' : 'text-red-500' ?> flex items-center">
                                <?php if ($performanceMetrics['traffic']['growth'] >= 0): ?>
                                    <i class="fas fa-arrow-up mr-1"></i>
                                <?php else: ?>
                                    <i class="fas fa-arrow-down mr-1"></i>
                                <?php endif; ?>
                                <span><?= abs(number_format($performanceMetrics['traffic']['growth'], 1)) ?>%</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-xs text-gray-500 dark:text-gray-400">vs. previous month: <?= number_format($performanceMetrics['traffic']['previous_month']) ?></p>
                        </div>
                    </div>

                    <!-- Conversion Rate Metric -->
                    <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Conversion Rate</p>
                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white"><?= number_format($performanceMetrics['conversion_rate']['current_month'], 1) ?>%</h3>
                            </div>
                            <div class="<?= $performanceMetrics['conversion_rate']['growth'] >= 0 ? 'text-green-500' : 'text-red-500' ?> flex items-center">
                                <?php if ($performanceMetrics['conversion_rate']['growth'] >= 0): ?>
                                    <i class="fas fa-arrow-up mr-1"></i>
                                <?php else: ?>
                                    <i class="fas fa-arrow-down mr-1"></i>
                                <?php endif; ?>
                                <span><?= abs(number_format($performanceMetrics['conversion_rate']['growth'], 1)) ?>%</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-xs text-gray-500 dark:text-gray-400">vs. previous month: <?= number_format($performanceMetrics['conversion_rate']['previous_month'], 1) ?>%</p>
                        </div>
                    </div>

                    <!-- Customer Acquisition Cost Metric -->
                    <div class="bg-white dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <div class="flex justify-between items-start">
                            <div>
                                <p class="text-sm text-gray-500 dark:text-gray-400">Acquisition Cost</p>
                                <h3 class="text-2xl font-bold text-gray-900 dark:text-white">$<?= number_format($performanceMetrics['customer_acquisition_cost']['current_month'], 2) ?></h3>
                            </div>
                            <div class="<?= $performanceMetrics['customer_acquisition_cost']['growth'] <= 0 ? 'text-green-500' : 'text-red-500' ?> flex items-center">
                                <?php if ($performanceMetrics['customer_acquisition_cost']['growth'] <= 0): ?>
                                    <i class="fas fa-arrow-down mr-1"></i>
                                <?php else: ?>
                                    <i class="fas fa-arrow-up mr-1"></i>
                                <?php endif; ?>
                                <span><?= abs(number_format($performanceMetrics['customer_acquisition_cost']['growth'], 1)) ?>%</span>
                            </div>
                        </div>
                        <div class="mt-2">
                            <p class="text-xs text-gray-500 dark:text-gray-400">vs. previous month: $<?= number_format($performanceMetrics['customer_acquisition_cost']['previous_month'], 2) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Business Ventures -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg mb-6">
            <div class="px-5 py-5">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-store text-cyan-500 mr-2"></i> Business Ventures
                    </h2>
                    <a href="/momentum/online-business/create" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                        <i class="fas fa-plus mr-1"></i> Add New
                    </a>
                </div>

                <?php if (empty($businessVentures)): ?>
                    <div class="text-center py-8">
                        <div class="text-gray-400 dark:text-gray-500 mb-3">
                            <i class="fas fa-store text-5xl"></i>
                        </div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No business ventures yet</h3>
                        <p class="text-gray-500 dark:text-gray-400 mb-4">Start by creating your first online business venture</p>
                        <a href="/momentum/online-business/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                            <i class="fas fa-plus mr-1.5"></i> Create Venture
                        </a>
                    </div>
                <?php else: ?>
                    <div class="overflow-x-auto">
                        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                            <thead class="bg-gray-50 dark:bg-gray-700">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Description</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Status</th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Created</th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                                </tr>
                            </thead>
                            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                                <?php foreach ($businessVentures as $venture): ?>
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <div class="text-sm font-medium text-gray-900 dark:text-white"><?= htmlspecialchars($venture['name']) ?></div>
                                        </td>
                                        <td class="px-6 py-4">
                                            <div class="text-sm text-gray-500 dark:text-gray-400"><?= htmlspecialchars($venture['description']) ?></div>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                                <?php if ($venture['status'] === 'active'): ?>
                                                    bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200
                                                <?php elseif ($venture['status'] === 'planning'): ?>
                                                    bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200
                                                <?php else: ?>
                                                    bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300
                                                <?php endif; ?>
                                            ">
                                                <?= ucfirst(htmlspecialchars($venture['status'])) ?>
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                            <?= date('M d, Y', strtotime($venture['created_at'])) ?>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                            <a href="/momentum/online-business/view/<?= $venture['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300 mr-3">View</a>
                                            <a href="/momentum/online-business/edit/<?= $venture['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300 mr-3">Edit</a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Resources and Tips -->
        <div class="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg">
            <div class="px-5 py-5">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white flex items-center">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i> Resources & Tips
                    </h2>
                    <a href="/momentum/help/online-business-features" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300 flex items-center">
                        View all <i class="fas fa-chevron-right ml-1 text-xs"></i>
                    </a>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-cyan-50 dark:bg-cyan-900/30 p-4 rounded-lg">
                        <h3 class="font-medium text-cyan-800 dark:text-cyan-300 mb-2">Business Metrics Guide</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Learn how to interpret and improve your key business metrics</p>
                        <a href="/momentum/help/online-business-metrics" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                            Read guide <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                    
                    <div class="bg-cyan-50 dark:bg-cyan-900/30 p-4 rounded-lg">
                        <h3 class="font-medium text-cyan-800 dark:text-cyan-300 mb-2">Growth Strategies</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Effective strategies to scale your online business ventures</p>
                        <a href="/momentum/help/online-business-growth" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                            Read guide <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                    
                    <div class="bg-cyan-50 dark:bg-cyan-900/30 p-4 rounded-lg">
                        <h3 class="font-medium text-cyan-800 dark:text-cyan-300 mb-2">Marketing Essentials</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">Marketing strategies to increase visibility and conversions</p>
                        <a href="/momentum/help/online-business-marketing" class="text-sm text-primary-600 dark:text-primary-400 hover:text-primary-800 dark:hover:text-primary-300">
                            Read guide <i class="fas fa-arrow-right ml-1"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
