<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mt-4">
    <div class="row mb-4">
        <div class="col">
            <h1>Checklist Templates</h1>
            <p class="lead">Use templates to quickly create standardized checklists</p>
        </div>
        <div class="col-auto">
            <div class="btn-group">
                <a href="/momentum/checklists" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Checklists
                </a>
                <a href="/momentum/checklists/create-template" class="btn btn-primary">
                    <i class="fas fa-plus"></i> New Template
                </a>
            </div>
        </div>
    </div>
    
    <!-- System Templates -->
    <h2 class="mb-3">AI Agent Army Templates</h2>
    <div class="row">
        <?php if (empty($systemTemplates)): ?>
            <div class="col">
                <div class="alert alert-info">
                    No system templates available yet.
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($systemTemplates as $template): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header bg-primary text-white">
                            <h5 class="card-title mb-0"><?= htmlspecialchars($template['name']) ?></h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($template['description'])): ?>
                                <p class="card-text"><?= htmlspecialchars($template['description']) ?></p>
                            <?php else: ?>
                                <p class="card-text text-muted">No description provided</p>
                            <?php endif; ?>
                            
                            <span class="badge bg-info"><?= ucfirst($template['category']) ?></span>
                            <span class="badge bg-secondary">System Template</span>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <a href="/momentum/checklists/view-template/<?= $template['id'] ?>" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye"></i> View
                                </a>
                                <a href="/momentum/checklists/create?template_id=<?= $template['id'] ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> Use Template
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- User Templates -->
    <h2 class="mb-3 mt-4">Your Templates</h2>
    <div class="row">
        <?php if (empty($userTemplates)): ?>
            <div class="col">
                <div class="alert alert-info">
                    You haven't created any templates yet. 
                    <a href="/momentum/checklists/create-template" class="alert-link">Create your first template</a> or 
                    <a href="/momentum/checklists" class="alert-link">save an existing checklist as a template</a>.
                </div>
            </div>
        <?php else: ?>
            <?php foreach ($userTemplates as $template): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <div class="card h-100">
                        <div class="card-header">
                            <h5 class="card-title mb-0"><?= htmlspecialchars($template['name']) ?></h5>
                        </div>
                        <div class="card-body">
                            <?php if (!empty($template['description'])): ?>
                                <p class="card-text"><?= htmlspecialchars($template['description']) ?></p>
                            <?php else: ?>
                                <p class="card-text text-muted">No description provided</p>
                            <?php endif; ?>
                            
                            <span class="badge bg-info"><?= ucfirst($template['category']) ?></span>
                            <span class="badge bg-primary">Your Template</span>
                        </div>
                        <div class="card-footer bg-transparent">
                            <div class="d-flex justify-content-between">
                                <div>
                                    <a href="/momentum/checklists/view-template/<?= $template['id'] ?>" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye"></i> View
                                    </a>
                                    <a href="/momentum/checklists/delete-template/<?= $template['id'] ?>" class="btn btn-sm btn-outline-danger" 
                                       onclick="return confirm('Are you sure you want to delete this template?')">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                                <a href="/momentum/checklists/create?template_id=<?= $template['id'] ?>" class="btn btn-sm btn-primary">
                                    <i class="fas fa-plus"></i> Use Template
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <!-- Template Categories -->
    <div class="card mt-4">
        <div class="card-header bg-primary text-white">
            <h5 class="card-title mb-0">Template Categories</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-tasks fa-3x text-primary mb-3"></i>
                            <h5>Implementation</h5>
                            <p class="small">Step-by-step guides for implementing AI Agent Army brigades</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-cogs fa-3x text-success mb-3"></i>
                            <h5>Configuration</h5>
                            <p class="small">Detailed checklists for configuring agents and workflows</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-line fa-3x text-info mb-3"></i>
                            <h5>Operations</h5>
                            <p class="small">Daily, weekly, and monthly operational checklists</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3 mb-3">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-rocket fa-3x text-warning mb-3"></i>
                            <h5>Optimization</h5>
                            <p class="small">Performance tracking and improvement checklists</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
