<div class="py-6">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white mb-3 md:mb-0">Tasks</h1>
            <div class="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
                <a href="/momentum/tasks/calendar" class="inline-flex items-center justify-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-calendar-alt mr-2"></i> Calendar View
                </a>
                <a href="/momentum/tasks/create" class="inline-flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                    <i class="fas fa-plus mr-2"></i> New Task
                </a>
                <div class="view-toggle ml-3 flex items-center">
                    <label for="compact-view-toggle" class="view-toggle-label mr-2">Compact View</label>
                    <div class="relative inline-block w-10 mr-2 align-middle select-none">
                        <input type="checkbox" id="compact-view-toggle" class="toggle-checkbox absolute block w-6 h-6 rounded-full bg-white border-4 appearance-none cursor-pointer" />
                        <label for="compact-view-toggle" class="toggle-label block overflow-hidden h-6 rounded-full bg-gray-300 dark:bg-gray-600 cursor-pointer"></label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Filters -->
        <div class="quick-filter-buttons mb-4">
            <button type="button" class="quick-filter-button" data-filter="today">
                <svg class="w-4 h-4 mr-1 inline-block" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor">
                    <path d="M128 0c17.7 0 32 14.3 32 32V64H288V32c0-17.7 14.3-32 32-32s32 14.3 32 32V64h48c26.5 0 48 21.5 48 48v48H0V112C0 85.5 21.5 64 48 64H96V32c0-17.7 14.3-32 32-32zM0 192H448V464c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V192zm80 64c-8.8 0-16 7.2-16 16v64c0 8.8 7.2 16 16 16h64c8.8 0 16-7.2 16-16V272c0-8.8-7.2-16-16-16H80z"/>
                </svg>
                Today
            </button>
            <button type="button" class="quick-filter-button" data-filter="this-week">
                <svg class="w-4 h-4 mr-1 inline-block" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor">
                    <path d="M128 0c17.7 0 32 14.3 32 32V64H288V32c0-17.7 14.3-32 32-32s32 14.3 32 32V64h48c26.5 0 48 21.5 48 48v48H0V112C0 85.5 21.5 64 48 64H96V32c0-17.7 14.3-32 32-32zM0 192H448V464c0 26.5-21.5 48-48 48H48c-26.5 0-48-21.5-48-48V192zm80 64c-8.8 0-16 7.2-16 16v96c0 8.8 7.2 16 16 16H368c8.8 0 16-7.2 16-16V272c0-8.8-7.2-16-16-16H80z"/>
                </svg>
                This Week
            </button>
            <button type="button" class="quick-filter-button" data-filter="overdue">
                <svg class="w-4 h-4 mr-1 inline-block" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">
                    <path d="M256 512A256 256 0 1 0 256 0a256 256 0 1 0 0 512zm0-384c13.3 0 24 10.7 24 24V264c0 13.3-10.7 24-24 24s-24-10.7-24-24V152c0-13.3 10.7-24 24-24zM224 352a32 32 0 1 1 64 0 32 32 0 1 1 -64 0z"/>
                </svg>
                Overdue
            </button>
            <button type="button" class="quick-filter-button" data-filter="high-priority">
                <svg class="w-4 h-4 mr-1 inline-block" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 448 512" fill="currentColor">
                    <path d="M64 32C64 14.3 49.7 0 32 0S0 14.3 0 32V64 368 480c0 17.7 14.3 32 32 32s32-14.3 32-32V352l64.3-16.1c41.1-10.3 84.6-5.5 122.5 13.4c44.2 22.1 95.5 24.8 141.7 7.4l34.7-13c12.5-4.7 20.8-16.6 20.8-30V66.1c0-23-24.2-38-44.8-27.7l-9.6 4.8c-46.3 23.2-100.8 23.2-147.1 0c-35.1-17.6-75.4-22-113.5-12.5L64 48V32z"/>
                </svg>
                High Priority
            </button>
            <button type="button" class="quick-filter-button" data-filter="in-progress">
                <svg class="w-4 h-4 mr-1 inline-block" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">
                    <path d="M222.7 32.1c5 16.9-4.6 34.8-21.5 39.8C121.8 95.6 64 169.1 64 256c0 106 86 192 192 192s192-86 192-192c0-86.9-57.8-160.4-137.1-184.1c-16.9-5-26.6-22.9-21.5-39.8s22.9-26.6 39.8-21.5C434.9 42.1 512 140 512 256c0 141.4-114.6 256-256 256S0 397.4 0 256C0 140 77.1 42.1 182.9 10.6c16.9-5 34.8 4.6 39.8 21.5z"/>
                </svg>
                In Progress
            </button>
        </div>

        <!-- Filters -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg mb-6 overflow-hidden">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">Filters</h3>
                <form action="/momentum/tasks" method="GET" class="grid grid-cols-1 md:grid-cols-4 gap-4" id="task-filter-form">
                    <div>
                        <label for="status" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
                        <select id="status" name="status" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">All Statuses</option>
                            <option value="todo" <?= isset($filters['status']) && $filters['status'] === 'todo' ? 'selected' : '' ?>>To Do</option>
                            <option value="in_progress" <?= isset($filters['status']) && $filters['status'] === 'in_progress' ? 'selected' : '' ?>>In Progress</option>
                            <option value="done" <?= isset($filters['status']) && $filters['status'] === 'done' ? 'selected' : '' ?>>Done</option>
                        </select>
                    </div>
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Priority</label>
                        <select id="priority" name="priority" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">All Priorities</option>
                            <option value="low" <?= isset($filters['priority']) && $filters['priority'] === 'low' ? 'selected' : '' ?>>Low</option>
                            <option value="medium" <?= isset($filters['priority']) && $filters['priority'] === 'medium' ? 'selected' : '' ?>>Medium</option>
                            <option value="high" <?= isset($filters['priority']) && $filters['priority'] === 'high' ? 'selected' : '' ?>>High</option>
                            <option value="urgent" <?= isset($filters['priority']) && $filters['priority'] === 'urgent' ? 'selected' : '' ?>>Urgent</option>
                        </select>
                    </div>
                    <div>
                        <label for="category_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Category</label>
                        <select id="category_id" name="category_id" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                            <option value="">All Categories</option>
                            <?php foreach ($categories as $category): ?>
                                <option value="<?= $category['id'] ?>" <?= isset($filters['category_id']) && $filters['category_id'] == $category['id'] ? 'selected' : '' ?>>
                                    <?= View::escape($category['name']) ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div>
                        <label for="due_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Due Date</label>
                        <input type="date" id="due_date" name="due_date" value="<?= isset($filters['due_date']) ? $filters['due_date'] : '' ?>" class="block w-full rounded-md border-gray-300 dark:border-gray-700 shadow-sm focus:border-primary-500 focus:ring-primary-500 sm:text-sm dark:bg-gray-700 dark:text-white">
                    </div>
                    <div class="md:col-span-4 flex justify-between items-center">
                        <div>
                            <?php if (!empty($filters)): ?>
                                <div class="text-sm text-primary-600 dark:text-primary-400">
                                    <i class="fas fa-info-circle mr-1"></i> Filters applied
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="flex">
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-filter mr-2"></i> Apply Filters
                            </button>
                            <a href="/momentum/tasks" id="clear-filters-btn" class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                                <i class="fas fa-times mr-2"></i> Clear Filters
                            </a>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <!-- Task List -->
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <?php if (empty($tasks)): ?>
                <div class="px-4 py-5 sm:p-6 text-center">
                    <div class="text-gray-500 dark:text-gray-400 mb-4">
                        <i class="fas fa-tasks text-4xl"></i>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-2">No tasks found</h3>
                    <p class="text-gray-500 dark:text-gray-400 mb-4">
                        <?php if (!empty($filters)): ?>
                            Try adjusting your filters or
                        <?php endif; ?>
                        create a new task to get started.
                    </p>
                    <a href="/momentum/tasks/create" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-plus mr-2"></i> Create Task
                    </a>
                </div>
            <?php else: ?>
                <div class="px-4 py-3 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                    <div class="flex flex-wrap items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <div>
                                <label for="task-grouping" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Group by</label>
                                <select id="task-grouping" class="mt-1 block w-full pl-3 pr-10 py-1 text-base border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm rounded-md">
                                    <option value="status">Status</option>
                                    <option value="priority">Priority</option>
                                    <option value="category">Category</option>
                                    <option value="due_date">Due Date</option>
                                </select>
                            </div>
                            <div>
                                <span class="text-sm text-gray-500 dark:text-gray-400 task-count"><?= count($tasks) ?> tasks</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto task-list-container">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700 task-table">
                        <thead class="bg-gray-50 dark:bg-gray-700 sticky top-0 z-10">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Status
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Task
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Priority
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Due Date
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Category
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            <?php foreach ($tasks as $task): ?>
                                <tr class="task-row priority-<?= $task['priority'] ?>"
                                    data-status="<?= $task['status'] ?>"
                                    data-priority="<?= $task['priority'] ?>"
                                    data-category="<?= $task['category_name'] ?? 'Uncategorized' ?>"
                                    data-category-color="<?= $task['category_color'] ?? '#6b7280' ?>"
                                    data-due-date="<?= $task['due_date'] ?? '' ?>"
                                    data-task-id="<?= $task['id'] ?>">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            <input type="checkbox" data-task-id="<?= $task['id'] ?>" class="task-checkbox h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 dark:border-gray-700 rounded" <?= $task['status'] === 'done' ? 'checked' : '' ?>>
                                            <span class="ml-2 text-xs">
                                                <?php if ($task['status'] === 'todo'): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                        To Do
                                                    </span>
                                                <?php elseif ($task['status'] === 'in_progress'): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                        In Progress
                                                    </span>
                                                <?php elseif ($task['status'] === 'done'): ?>
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
                                                        Done
                                                    </span>
                                                <?php endif; ?>
                                            </span>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4">
                                        <div class="flex items-center">
                                            <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-sm font-medium text-gray-900 dark:text-white <?= $task['status'] === 'done' ? 'line-through text-gray-500 dark:text-gray-400' : '' ?>">
                                                <?= View::escape($task['title']) ?>
                                            </a>
                                            <?php if ($task['parent_id']): ?>
                                                <span class="ml-2 text-xs text-gray-500 dark:text-gray-400">
                                                    <i class="fas fa-level-up-alt fa-rotate-90"></i> Subtask
                                                </span>
                                            <?php endif; ?>
                                        </div>
                                        <?php if (!empty($task['description'])): ?>
                                            <div class="text-xs text-gray-500 dark:text-gray-400 mt-1 truncate max-w-xs">
                                                <?= View::escape(substr($task['description'], 0, 100)) ?><?= strlen($task['description']) > 100 ? '...' : '' ?>
                                            </div>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($task['priority'] === 'low'): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                                                Low
                                            </span>
                                        <?php elseif ($task['priority'] === 'medium'): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                                                Medium
                                            </span>
                                        <?php elseif ($task['priority'] === 'high'): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                                                High
                                            </span>
                                        <?php elseif ($task['priority'] === 'urgent'): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200">
                                                Urgent
                                            </span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if ($task['due_date']): ?>
                                            <div class="text-sm text-gray-900 dark:text-white">
                                                <?= View::formatDate($task['due_date']) ?>
                                            </div>
                                            <?php if ($task['due_time']): ?>
                                                <div class="text-xs text-gray-500 dark:text-gray-400">
                                                    <?= View::formatTime($task['due_time']) ?>
                                                </div>
                                            <?php endif; ?>
                                            <?php
                                                $dueDate = strtotime($task['due_date']);
                                                $today = strtotime(date('Y-m-d'));
                                                $tomorrow = strtotime('+1 day', $today);

                                                if ($dueDate < $today && $task['status'] !== 'done'):
                                            ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200 mt-1">
                                                    Overdue
                                                </span>
                                            <?php elseif ($dueDate === $today): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200 mt-1">
                                                    Today
                                                </span>
                                            <?php elseif ($dueDate === $tomorrow): ?>
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200 mt-1">
                                                    Tomorrow
                                                </span>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <span class="text-gray-500 dark:text-gray-400">No due date</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <?php if (!empty($task['category_id']) && !empty($task['category_color'])): ?>
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium" style="background-color: <?= $task['category_color'] ?>25; color: <?= $task['category_color'] ?>;">
                                                <?= View::escape($task['category_name'] ?? 'Category') ?>
                                            </span>
                                        <?php else: ?>
                                            <span class="text-gray-500 dark:text-gray-400">Uncategorized</span>
                                        <?php endif; ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                        <div class="flex justify-end space-x-2">
                                            <a href="/momentum/tasks/view/<?= $task['id'] ?>" class="text-primary-600 dark:text-primary-400 hover:text-primary-900 dark:hover:text-primary-300" title="View">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="/momentum/tasks/edit/<?= $task['id'] ?>" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-900 dark:hover:text-indigo-300" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="/momentum/tasks/delete/<?= $task['id'] ?>" class="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300" title="Delete" onclick="return confirm('Are you sure you want to delete this task?')">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
    // Handle filter form submission
    document.addEventListener('DOMContentLoaded', function() {
        const filterForm = document.getElementById('task-filter-form');
        if (filterForm) {
            filterForm.addEventListener('submit', function(e) {
                // Remove empty filter values before submitting
                const formElements = filterForm.elements;
                let hasFilters = false;

                for (let i = 0; i < formElements.length; i++) {
                    const element = formElements[i];
                    if (element.type !== 'submit' && element.name && element.value === '') {
                        // Create a hidden input to track that we've processed this form
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = 'filter_processed';
                        hiddenInput.value = '1';
                        filterForm.appendChild(hiddenInput);

                        // Remove the element from form submission
                        element.disabled = true;
                    } else if (element.type !== 'submit' && element.name && element.value !== '') {
                        hasFilters = true;
                    }
                }
            });
        }

        // Handle clear filters button
        const clearFiltersBtn = document.getElementById('clear-filters-btn');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', function(e) {
                e.preventDefault();

                // Reset all form fields
                if (filterForm) {
                    filterForm.reset();

                    // Submit the form with no filters
                    window.location.href = '/momentum/tasks?filter_processed=1';
                }
            });
        }
    });
</script>

<!-- Include custom CSS and JS for task list enhancements -->
<link rel="stylesheet" href="/momentum/public/css/custom-task-list.css">
<script src="/momentum/public/js/task-list-enhancements.js" defer></script>

<style>
    /* Toggle Switch Styling */
    .toggle-checkbox:checked {
        right: 0;
        border-color: #6366f1;
    }
    .toggle-checkbox:checked + .toggle-label {
        background-color: #6366f1;
    }
    .dark .toggle-checkbox:checked + .toggle-label {
        background-color: #818cf8;
    }
    .toggle-label {
        transition: background-color 0.2s ease;
    }

    /* Compact View Styling */
    .task-table.compact-view td {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }
    .task-table.compact-view .text-sm {
        font-size: 0.75rem;
    }
    .task-table.compact-view .truncate.max-w-xs,
    .task-table.compact-view td:nth-child(4) .text-xs {
        display: none;
    }

    /* Animation for new elements - optimized for performance */
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(-10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    .quick-filter-buttons {
        animation: fadeIn 0.3s ease-out;
        will-change: opacity, transform;
    }

    /* Performance optimizations */
    .task-list-container {
        contain: content;
    }

    /* Loading indicator */
    .loading-indicator {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(to right, #6366f1, #8b5cf6);
        z-index: 9999;
        animation: loading 1.5s infinite linear;
        transform-origin: 0% 50%;
    }

    @keyframes loading {
        0% { transform: scaleX(0); }
        50% { transform: scaleX(0.5); }
        100% { transform: scaleX(1); }
    }
</style>

<!-- Preload critical resources -->
<link rel="preload" href="/momentum/public/css/custom-task-list.css" as="style">
<link rel="preload" href="/momentum/public/js/task-list-enhancements.js" as="script">
<link rel="preload" href="/momentum/public/js/dropdown-page-check.js" as="script">

<!-- Prevent dropdown errors -->
<script src="/momentum/public/js/dropdown-page-check.js"></script>
