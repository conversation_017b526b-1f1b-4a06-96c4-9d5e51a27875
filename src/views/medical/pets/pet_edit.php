<?php
/**
 * Pet Edit Form View
 *
 * Form for editing an existing pet
 */
?>

<div class="py-6">
    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between mb-6">
            <h1 class="text-2xl font-semibold text-gray-900 dark:text-white">
                <i class="fas fa-paw text-primary-600 dark:text-primary-400 mr-2"></i> Edit Pet: <?= htmlspecialchars($pet['name']) ?>
            </h1>
            <a href="/momentum/medical/pets/view/<?= $pet['id'] ?>" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-1"></i> Back to Pet
            </a>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
            <form action="/momentum/medical/pets/update/<?= $pet['id'] ?>" method="post" class="p-6">
                <!-- Pet Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-info-circle text-primary-600 dark:text-primary-400 mr-2"></i> Basic Information
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Pet Name <span class="text-red-600">*</span>
                            </label>
                            <input type="text" name="name" id="name" required value="<?= htmlspecialchars($pet['name']) ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>
                        <div>
                            <label for="species" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Species <span class="text-red-600">*</span>
                            </label>
                            <select name="species" id="species" required
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="">Select Species</option>
                                <option value="Dog" <?= $pet['species'] === 'Dog' ? 'selected' : '' ?>>Dog</option>
                                <option value="Cat" <?= $pet['species'] === 'Cat' ? 'selected' : '' ?>>Cat</option>
                                <option value="Bird" <?= $pet['species'] === 'Bird' ? 'selected' : '' ?>>Bird</option>
                                <option value="Fish" <?= $pet['species'] === 'Fish' ? 'selected' : '' ?>>Fish</option>
                                <option value="Rabbit" <?= $pet['species'] === 'Rabbit' ? 'selected' : '' ?>>Rabbit</option>
                                <option value="Hamster" <?= $pet['species'] === 'Hamster' ? 'selected' : '' ?>>Hamster</option>
                                <option value="Guinea Pig" <?= $pet['species'] === 'Guinea Pig' ? 'selected' : '' ?>>Guinea Pig</option>
                                <option value="Reptile" <?= $pet['species'] === 'Reptile' ? 'selected' : '' ?>>Reptile</option>
                                <option value="Other" <?= $pet['species'] === 'Other' ? 'selected' : '' ?>>Other</option>
                            </select>
                        </div>
                        <div>
                            <label for="breed" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Breed
                            </label>
                            <input type="text" name="breed" id="breed" value="<?= htmlspecialchars($pet['breed'] ?? '') ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>
                        <div>
                            <label for="gender" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Gender
                            </label>
                            <select name="gender" id="gender"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                <option value="unknown" <?= $pet['gender'] === 'unknown' ? 'selected' : '' ?>>Unknown</option>
                                <option value="male" <?= $pet['gender'] === 'male' ? 'selected' : '' ?>>Male</option>
                                <option value="female" <?= $pet['gender'] === 'female' ? 'selected' : '' ?>>Female</option>
                            </select>
                        </div>
                        <div>
                            <label for="birth_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Birth Date
                            </label>
                            <input type="date" name="birth_date" id="birth_date" value="<?= $pet['birth_date'] ?? '' ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>
                        <div>
                            <label for="adoption_date" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Adoption Date
                            </label>
                            <input type="date" name="adoption_date" id="adoption_date" value="<?= $pet['adoption_date'] ?? '' ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>
                    </div>
                </div>

                <!-- Physical Characteristics -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-paw text-primary-600 dark:text-primary-400 mr-2"></i> Physical Characteristics
                    </h2>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="color" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Color
                            </label>
                            <input type="text" name="color" id="color" value="<?= htmlspecialchars($pet['color'] ?? '') ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>
                        <div class="grid grid-cols-2 gap-2">
                            <div>
                                <label for="weight" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Weight
                                </label>
                                <input type="number" name="weight" id="weight" step="0.01" value="<?= $pet['weight'] ?? '' ?>"
                                    class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                            </div>
                            <div>
                                <label for="weight_unit" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                    Unit
                                </label>
                                <select name="weight_unit" id="weight_unit"
                                    class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                                    <option value="kg" <?= ($pet['weight_unit'] ?? '') === 'kg' ? 'selected' : '' ?>>kg</option>
                                    <option value="lb" <?= ($pet['weight_unit'] ?? '') === 'lb' ? 'selected' : '' ?>>lb</option>
                                    <option value="g" <?= ($pet['weight_unit'] ?? '') === 'g' ? 'selected' : '' ?>>g</option>
                                </select>
                            </div>
                        </div>
                        <div>
                            <label for="microchip_id" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                Microchip ID
                            </label>
                            <input type="text" name="microchip_id" id="microchip_id" value="<?= htmlspecialchars($pet['microchip_id'] ?? '') ?>"
                                class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500">
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="mb-6">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
                        <i class="fas fa-sticky-note text-primary-600 dark:text-primary-400 mr-2"></i> Additional Information
                    </h2>
                    <div>
                        <label for="notes" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            Notes
                        </label>
                        <textarea name="notes" id="notes" rows="4"
                            class="block w-full rounded-md border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white shadow-sm focus:border-primary-500 focus:ring-primary-500"
                            placeholder="Enter any additional information about your pet..."><?= htmlspecialchars($pet['notes'] ?? '') ?></textarea>
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit"
                        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200">
                        <i class="fas fa-save mr-1"></i> Update Pet
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
