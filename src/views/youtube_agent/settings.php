<?php require_once __DIR__ . '/../partials/header.php'; ?>

<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-3xl font-bold text-gray-900 dark:text-white">YouTube Agent Settings</h1>
        <a href="/momentum/youtube-agent" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
            <i class="fas fa-arrow-left mr-2"></i> Back to Dashboard
        </a>
    </div>
    
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="md:col-span-2">
            <!-- API Settings Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">YouTube API Settings</h2>
                </div>
                <div class="p-4">
                    <form action="/momentum/youtube-agent/save-settings" method="POST">
                        <div class="mb-4">
                            <label for="api_key" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">YouTube API Key</label>
                            <input type="text" id="api_key" name="api_key" value="<?= htmlspecialchars($apiKey) ?>" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md" placeholder="Enter your YouTube API key">
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                Your YouTube API key is used to search for videos and retrieve video information.
                            </p>
                        </div>
                        
                        <div class="mb-4">
                            <label for="quota_limit" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Daily Quota Limit</label>
                            <input type="number" id="quota_limit" name="quota_limit" value="<?= $quotaUsage ? $quotaUsage['quota_limit'] : 10000 ?>" min="1" class="shadow-sm focus:ring-primary-500 focus:border-primary-500 block w-full sm:text-sm border-gray-300 dark:border-gray-600 dark:bg-gray-700 dark:text-white rounded-md">
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                The YouTube API has a daily quota limit. The default is 10,000 units per day.
                            </p>
                        </div>
                        
                        <div class="flex justify-end">
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                                <i class="fas fa-save mr-2"></i> Save Settings
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- API Usage Guide Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">YouTube API Usage Guide</h2>
                </div>
                <div class="p-4">
                    <div class="mb-4">
                        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-2">Getting a YouTube API Key</h3>
                        <ol class="list-decimal pl-5 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                            <li>Go to the <a href="https://console.developers.google.com/" target="_blank" class="text-primary-600 hover:text-primary-800 dark:text-primary-400">Google Developers Console</a></li>
                            <li>Create a new project or select an existing one</li>
                            <li>Enable the YouTube Data API v3</li>
                            <li>Create credentials for an API key</li>
                            <li>Copy the API key and paste it in the field above</li>
                        </ol>
                    </div>
                    
                    <div class="mb-4">
                        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-2">Understanding Quota Usage</h3>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">
                            The YouTube API uses a quota system to limit the number of requests you can make per day. Different operations consume different amounts of quota:
                        </p>
                        <ul class="list-disc pl-5 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                            <li>Search operation: 100 units</li>
                            <li>Video details: 1 unit per video</li>
                            <li>Captions/transcripts: 50 units</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-md font-medium text-gray-900 dark:text-white mb-2">Best Practices</h3>
                        <ul class="list-disc pl-5 space-y-1 text-sm text-gray-600 dark:text-gray-400">
                            <li>Be specific with your search queries to get the most relevant results</li>
                            <li>Save videos you want to analyze later to avoid repeated API calls</li>
                            <li>Use the quota reset feature only when necessary</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="md:col-span-1">
            <!-- Quota Usage Card -->
            <?php if ($quotaUsage): ?>
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Quota Usage</h2>
                </div>
                <div class="p-4">
                    <div class="mb-4">
                        <div class="flex justify-between text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                            <span>Used: <?= number_format($quotaUsage['quota_used']) ?></span>
                            <span>Limit: <?= number_format($quotaUsage['quota_limit']) ?></span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5 mb-1">
                            <?php $percentage = min(100, ($quotaUsage['quota_used'] / $quotaUsage['quota_limit']) * 100); ?>
                            <div class="bg-primary-600 h-2.5 rounded-full" style="width: <?= $percentage ?>%"></div>
                        </div>
                        <p class="text-xs text-gray-500 dark:text-gray-400">
                            <?= number_format($quotaUsage['quota_limit'] - $quotaUsage['quota_used']) ?> units remaining
                        </p>
                    </div>
                    
                    <div class="mb-4">
                        <p class="text-sm text-gray-600 dark:text-gray-400">
                            Resets on: <span class="font-medium text-gray-900 dark:text-white"><?= date('F j, Y', strtotime($quotaUsage['reset_date'])) ?></span>
                        </p>
                    </div>
                    
                    <div class="flex justify-center">
                        <a href="/momentum/youtube-agent/reset-quota" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500" onclick="return confirm('Are you sure you want to reset your quota usage? This should only be done if your actual YouTube API quota has been reset.')">
                            <i class="fas fa-redo mr-2"></i> Reset Quota Usage
                        </a>
                    </div>
                </div>
            </div>
            <?php endif; ?>
            
            <!-- Quick Links Card -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="px-4 py-3 border-b border-gray-200 dark:border-gray-700">
                    <h2 class="text-lg font-medium text-gray-900 dark:text-white">Quick Links</h2>
                </div>
                <div class="p-4">
                    <ul class="space-y-2">
                        <li>
                            <a href="https://console.developers.google.com/" target="_blank" class="flex items-center text-primary-600 hover:text-primary-800 dark:text-primary-400">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                <span>Google Developers Console</span>
                            </a>
                        </li>
                        <li>
                            <a href="https://developers.google.com/youtube/v3/docs" target="_blank" class="flex items-center text-primary-600 hover:text-primary-800 dark:text-primary-400">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                <span>YouTube API Documentation</span>
                            </a>
                        </li>
                        <li>
                            <a href="https://developers.google.com/youtube/v3/determine_quota_cost" target="_blank" class="flex items-center text-primary-600 hover:text-primary-800 dark:text-primary-400">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                <span>Quota Calculator</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../partials/footer.php'; ?>
