<?php
/**
 * Create Research Plan View
 */

$pageTitle = $data['page_title'] ?? 'Create Research Plan';
$session = $data['session'] ?? [];

if (empty($session)) {
    echo '<div class="container mx-auto px-4 py-8"><div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">Research session not found.</div></div>';
    return;
}
?>

<div class="container mx-auto px-4 py-8">
    <!-- Header -->
    <div class="flex justify-between items-center mb-6">
        <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">Create Action Plan</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-1">
                Convert research from "<strong><?= htmlspecialchars($session['title']) ?></strong>" into actionable plan
            </p>
        </div>
        <a href="/momentum/research/session/<?= $session['id'] ?>" 
           class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg font-medium transition-colors">
            <i class="fas fa-arrow-left mr-2"></i>Back to Session
        </a>
    </div>

    <!-- Create Plan Form -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        <div class="p-6">
            <form method="POST" action="/momentum/research/session/<?= $session['id'] ?>/create-plan" class="space-y-6">
                <!-- Plan Title -->
                <div>
                    <label for="title" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Plan Title *
                    </label>
                    <input type="text" 
                           id="title" 
                           name="title" 
                           required
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                           placeholder="e.g., AI Agent Platform Implementation Plan, Market Entry Strategy">
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Plan Description
                    </label>
                    <textarea id="description" 
                              name="description" 
                              rows="4"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                              placeholder="Describe the overall plan, objectives, and approach..."></textarea>
                </div>

                <!-- Plan Type -->
                <div>
                    <label for="plan_type" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Plan Type
                    </label>
                    <select id="plan_type" 
                            name="plan_type"
                            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                        <option value="implementation">Implementation Plan</option>
                        <option value="strategy">Strategic Plan</option>
                        <option value="roadmap">Roadmap</option>
                        <option value="experiment">Experiment Plan</option>
                        <option value="other">Other</option>
                    </select>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        Choose the type that best describes your plan
                    </p>
                </div>

                <!-- Priority and Effort Row -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Priority -->
                    <div>
                        <label for="priority" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Priority Level
                        </label>
                        <select id="priority" 
                                name="priority"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                            <option value="low">Low Priority</option>
                            <option value="medium" selected>Medium Priority</option>
                            <option value="high">High Priority</option>
                            <option value="urgent">Urgent</option>
                        </select>
                    </div>

                    <!-- Estimated Effort -->
                    <div>
                        <label for="estimated_effort" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                            Estimated Effort
                        </label>
                        <select id="estimated_effort" 
                                name="estimated_effort"
                                class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white">
                            <option value="small">Small (1-2 weeks)</option>
                            <option value="medium" selected>Medium (1-2 months)</option>
                            <option value="large">Large (3-6 months)</option>
                            <option value="xl">Extra Large (6+ months)</option>
                        </select>
                    </div>
                </div>

                <!-- Duration -->
                <div>
                    <label for="estimated_duration_days" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Estimated Duration (Days)
                    </label>
                    <input type="number" 
                           id="estimated_duration_days" 
                           name="estimated_duration_days" 
                           min="1"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                           placeholder="e.g., 30, 90, 180">
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        Optional: Estimate how many days this plan will take to complete
                    </p>
                </div>

                <!-- Success Criteria -->
                <div>
                    <label for="success_criteria" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Success Criteria
                    </label>
                    <textarea id="success_criteria" 
                              name="success_criteria" 
                              rows="3"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                              placeholder="Define what success looks like for this plan..."></textarea>
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        What measurable outcomes will indicate this plan was successful?
                    </p>
                </div>

                <!-- Risks and Mitigation -->
                <div>
                    <label for="risks_and_mitigation" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Risks & Mitigation Strategies
                    </label>
                    <textarea id="risks_and_mitigation" 
                              name="risks_and_mitigation" 
                              rows="3"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                              placeholder="Identify potential risks and how to mitigate them..."></textarea>
                </div>

                <!-- Resources Needed -->
                <div>
                    <label for="resources_needed" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Resources Needed
                    </label>
                    <textarea id="resources_needed" 
                              name="resources_needed" 
                              rows="3"
                              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                              placeholder="List the resources, tools, people, or budget needed..."></textarea>
                </div>

                <!-- Tags -->
                <div>
                    <label for="tags" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        Tags
                    </label>
                    <input type="text" 
                           id="tags" 
                           name="tags"
                           class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 dark:bg-gray-700 dark:text-white"
                           placeholder="implementation, mvp, ai-agents, revenue (comma-separated)">
                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                        Add tags to help organize and find your plans later
                    </p>
                </div>

                <!-- ADHD-Friendly Options -->
                <div class="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4">
                    <h3 class="text-lg font-semibold text-purple-800 dark:text-purple-200 mb-3">
                        🧠 ADHD-Friendly Planning Options
                    </h3>
                    
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   id="auto_generate_actions" 
                                   name="auto_generate_actions" 
                                   checked
                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Auto-generate action items based on plan type
                            </span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   id="break_into_chunks" 
                                   name="break_into_chunks" 
                                   checked
                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Break large tasks into smaller, manageable chunks
                            </span>
                        </label>
                        
                        <label class="flex items-center">
                            <input type="checkbox" 
                                   id="set_reminders" 
                                   name="set_reminders"
                                   class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">
                                Set up progress check reminders
                            </span>
                        </label>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-700">
                    <a href="/momentum/research/session/<?= $session['id'] ?>" 
                       class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        Cancel
                    </a>
                    <button type="submit" 
                            class="px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors">
                        <i class="fas fa-plus mr-2"></i>Create Action Plan
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Planning Tips -->
    <div class="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-800 dark:text-blue-200 mb-3">
            💡 Effective Planning Tips
        </h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-700 dark:text-blue-300">
            <div>
                <h4 class="font-medium mb-2">Plan Structure:</h4>
                <ul class="space-y-1">
                    <li>• Start with clear, measurable objectives</li>
                    <li>• Break down into specific action items</li>
                    <li>• Set realistic timelines and milestones</li>
                    <li>• Identify dependencies and blockers</li>
                </ul>
            </div>
            <div>
                <h4 class="font-medium mb-2">ADHD Considerations:</h4>
                <ul class="space-y-1">
                    <li>• Keep action items small and specific</li>
                    <li>• Build in buffer time for unexpected delays</li>
                    <li>• Include regular progress check-ins</li>
                    <li>• Plan for motivation and energy fluctuations</li>
                </ul>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on title field
    document.getElementById('title').focus();
    
    // Auto-populate title based on session
    const sessionTitle = "<?= htmlspecialchars($session['title']) ?>";
    if (sessionTitle) {
        document.getElementById('title').placeholder = sessionTitle + " - Implementation Plan";
    }
    
    // Update duration based on effort selection
    const effortSelect = document.getElementById('estimated_effort');
    const durationInput = document.getElementById('estimated_duration_days');
    
    effortSelect.addEventListener('change', function() {
        const effort = this.value;
        let suggestedDays = '';
        
        switch(effort) {
            case 'small': suggestedDays = '14'; break;
            case 'medium': suggestedDays = '60'; break;
            case 'large': suggestedDays = '120'; break;
            case 'xl': suggestedDays = '180'; break;
        }
        
        if (suggestedDays && !durationInput.value) {
            durationInput.value = suggestedDays;
        }
    });
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const title = document.getElementById('title').value.trim();
        if (!title) {
            e.preventDefault();
            alert('Please enter a plan title');
            document.getElementById('title').focus();
            return false;
        }
    });
});
</script>
