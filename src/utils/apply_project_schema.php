<?php
/**
 * Apply Project Management Schema
 *
 * This script applies the project management schema to the database.
 * It checks if tables already exist before creating them to avoid errors.
 */

require_once __DIR__ . '/../utils/Database.php';

// Get database connection
$db = Database::getInstance();
$conn = $db->getConnection();

echo "Checking and applying project management schema...\n";

// Check if projects table exists
$checkProjectsTable = "SELECT COUNT(*) as table_exists
                      FROM information_schema.TABLES
                      WHERE TABLE_SCHEMA = DATABASE()
                      AND TABLE_NAME = 'projects'";
$projectsTableExists = $db->fetchOne($checkProjectsTable);

if (!$projectsTableExists || $projectsTableExists['table_exists'] == 0) {
    echo "Creating projects table...\n";

    // Create projects table
    $createProjectsTable = "CREATE TABLE projects (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        start_date DATE,
        end_date DATE,
        status ENUM('planning', 'in_progress', 'on_hold', 'completed', 'archived') DEFAULT 'planning',
        progress INT DEFAULT 0 COMMENT 'Progress percentage (0-100)',
        is_template BOOLEAN DEFAULT FALSE,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->query($createProjectsTable);
    echo "Projects table created successfully.\n";
} else {
    echo "Projects table already exists.\n";
}

// Check if project_members table exists
$checkProjectMembersTable = "SELECT COUNT(*) as table_exists
                            FROM information_schema.TABLES
                            WHERE TABLE_SCHEMA = DATABASE()
                            AND TABLE_NAME = 'project_members'";
$projectMembersTableExists = $db->fetchOne($checkProjectMembersTable);

if (!$projectMembersTableExists || $projectMembersTableExists['table_exists'] == 0) {
    echo "Creating project_members table...\n";

    // Create project_members table
    $createProjectMembersTable = "CREATE TABLE project_members (
        id INT AUTO_INCREMENT PRIMARY KEY,
        project_id INT NOT NULL,
        user_id INT NOT NULL,
        role ENUM('owner', 'manager', 'member', 'viewer') DEFAULT 'member',
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY unique_project_member (project_id, user_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->query($createProjectMembersTable);
    echo "Project members table created successfully.\n";
} else {
    echo "Project members table already exists.\n";
}

// Check if tasks table has project_id column
$checkProjectIdColumn = "SELECT COUNT(*) as column_exists
                        FROM information_schema.COLUMNS
                        WHERE TABLE_SCHEMA = DATABASE()
                        AND TABLE_NAME = 'tasks'
                        AND COLUMN_NAME = 'project_id'";
$projectIdColumnExists = $db->fetchOne($checkProjectIdColumn);

if (!$projectIdColumnExists || $projectIdColumnExists['column_exists'] == 0) {
    echo "Adding project_id column to tasks table...\n";

    // Add project_id column to tasks table
    $addProjectIdColumn = "ALTER TABLE tasks
                          ADD COLUMN project_id INT NULL AFTER parent_id,
                          ADD FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE SET NULL";

    $db->query($addProjectIdColumn);
    echo "Project_id column added to tasks table successfully.\n";
} else {
    echo "Project_id column already exists in tasks table.\n";
}

// Check if task_dependencies table exists
$checkTaskDependenciesTable = "SELECT COUNT(*) as table_exists
                              FROM information_schema.TABLES
                              WHERE TABLE_SCHEMA = DATABASE()
                              AND TABLE_NAME = 'task_dependencies'";
$taskDependenciesTableExists = $db->fetchOne($checkTaskDependenciesTable);

if (!$taskDependenciesTableExists || $taskDependenciesTableExists['table_exists'] == 0) {
    echo "Creating task_dependencies table...\n";

    // Create task_dependencies table
    $createTaskDependenciesTable = "CREATE TABLE task_dependencies (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        depends_on_task_id INT NOT NULL,
        dependency_type ENUM('finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish') DEFAULT 'finish_to_start',
        lag_time INT DEFAULT 0 COMMENT 'Lag time in minutes',
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        FOREIGN KEY (depends_on_task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        UNIQUE KEY unique_dependency (task_id, depends_on_task_id)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->query($createTaskDependenciesTable);
    echo "Task dependencies table created successfully.\n";
} else {
    echo "Task dependencies table already exists.\n";
}

// Check if project_comments table exists
$checkProjectCommentsTable = "SELECT COUNT(*) as table_exists
                            FROM information_schema.TABLES
                            WHERE TABLE_SCHEMA = DATABASE()
                            AND TABLE_NAME = 'project_comments'";
$projectCommentsTableExists = $db->fetchOne($checkProjectCommentsTable);

if (!$projectCommentsTableExists || $projectCommentsTableExists['table_exists'] == 0) {
    echo "Creating project_comments table...\n";

    // Create project_comments table
    $createProjectCommentsTable = "CREATE TABLE project_comments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        project_id INT NOT NULL,
        user_id INT NOT NULL,
        content TEXT NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->query($createProjectCommentsTable);
    echo "Project comments table created successfully.\n";
} else {
    echo "Project comments table already exists.\n";
}

// Check if task_comments table exists
$checkTaskCommentsTable = "SELECT COUNT(*) as table_exists
                          FROM information_schema.TABLES
                          WHERE TABLE_SCHEMA = DATABASE()
                          AND TABLE_NAME = 'task_comments'";
$taskCommentsTableExists = $db->fetchOne($checkTaskCommentsTable);

if (!$taskCommentsTableExists || $taskCommentsTableExists['table_exists'] == 0) {
    echo "Creating task_comments table...\n";

    // Create task_comments table
    $createTaskCommentsTable = "CREATE TABLE task_comments (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        user_id INT NOT NULL,
        content TEXT NOT NULL,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->query($createTaskCommentsTable);
    echo "Task comments table created successfully.\n";
} else {
    echo "Task comments table already exists.\n";
}

// Check if project_activity_log table exists
$checkProjectActivityLogTable = "SELECT COUNT(*) as table_exists
                               FROM information_schema.TABLES
                               WHERE TABLE_SCHEMA = DATABASE()
                               AND TABLE_NAME = 'project_activity_log'";
$projectActivityLogTableExists = $db->fetchOne($checkProjectActivityLogTable);

if (!$projectActivityLogTableExists || $projectActivityLogTableExists['table_exists'] == 0) {
    echo "Creating project_activity_log table...\n";

    // Create project_activity_log table
    $createProjectActivityLogTable = "CREATE TABLE project_activity_log (
        id INT AUTO_INCREMENT PRIMARY KEY,
        project_id INT NOT NULL,
        user_id INT NOT NULL,
        action VARCHAR(100) NOT NULL,
        details TEXT,
        created_at DATETIME NOT NULL,
        FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

    $db->query($createProjectActivityLogTable);
    echo "Project activity log table created successfully.\n";
} else {
    echo "Project activity log table already exists.\n";
}

echo "Project management schema applied successfully.\n";
