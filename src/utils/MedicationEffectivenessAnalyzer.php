<?php
/**
 * Medication Effectiveness Analyzer
 * 
 * Analyzes correlation between medication adherence and symptom tracking data
 */

class MedicationEffectivenessAnalyzer {
    private $db;
    
    public function __construct() {
        require_once __DIR__ . '/Database.php';
        $this->db = Database::getInstance();
    }
    
    /**
     * Get medication effectiveness analysis for a user
     */
    public function getEffectivenessAnalysis($userId, $days = 30) {
        $medications = $this->getMedicationData($userId, $days);
        $symptoms = $this->getSymptomData($userId, $days);
        
        $analysis = [];
        
        foreach ($medications as $medication) {
            $medicationAnalysis = $this->analyzeMedicationEffectiveness(
                $medication, 
                $symptoms, 
                $days
            );
            
            $analysis[] = $medicationAnalysis;
        }
        
        return [
            'medications' => $analysis,
            'overall_correlation' => $this->calculateOverallCorrelation($analysis),
            'recommendations' => $this->generateRecommendations($analysis),
            'period' => $days
        ];
    }
    
    /**
     * Get medication adherence and timing data
     */
    private function getMedicationData($userId, $days) {
        $sql = "SELECT m.id, m.name, m.dosage, m.dosage_unit,
                       l.log_date, l.log_time, l.taken, l.actual_dosage,
                       r.reminder_time
                FROM medications m
                LEFT JOIN medication_logs l ON m.id = l.medication_id 
                    AND l.log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                LEFT JOIN medication_reminders r ON m.id = r.medication_id AND r.active = 1
                WHERE m.user_id = ?
                ORDER BY m.id, l.log_date DESC, l.log_time DESC";
        
        $results = $this->db->fetchAll($sql, [$days, $userId]);
        
        // Group by medication
        $medications = [];
        foreach ($results as $row) {
            $medId = $row['id'];
            
            if (!isset($medications[$medId])) {
                $medications[$medId] = [
                    'id' => $row['id'],
                    'name' => $row['name'],
                    'dosage' => $row['dosage'],
                    'dosage_unit' => $row['dosage_unit'],
                    'logs' => [],
                    'reminders' => []
                ];
            }
            
            if ($row['log_date']) {
                $medications[$medId]['logs'][] = [
                    'date' => $row['log_date'],
                    'time' => $row['log_time'],
                    'taken' => $row['taken'],
                    'actual_dosage' => $row['actual_dosage']
                ];
            }
            
            if ($row['reminder_time'] && !in_array($row['reminder_time'], $medications[$medId]['reminders'])) {
                $medications[$medId]['reminders'][] = $row['reminder_time'];
            }
        }
        
        return array_values($medications);
    }
    
    /**
     * Get symptom tracking data
     */
    private function getSymptomData($userId, $days) {
        $sql = "SELECT log_date, 
                       AVG(CASE WHEN symptom_name = 'focus' THEN severity END) as focus_avg,
                       AVG(CASE WHEN symptom_name = 'hyperactivity' THEN severity END) as hyperactivity_avg,
                       AVG(CASE WHEN symptom_name = 'impulsivity' THEN severity END) as impulsivity_avg,
                       AVG(CASE WHEN symptom_name = 'mood' THEN severity END) as mood_avg,
                       AVG(CASE WHEN symptom_name = 'energy' THEN severity END) as energy_avg,
                       AVG(CASE WHEN symptom_name = 'sleep' THEN severity END) as sleep_avg
                FROM adhd_symptom_logs 
                WHERE user_id = ? 
                AND log_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                GROUP BY log_date
                ORDER BY log_date DESC";
        
        return $this->db->fetchAll($sql, [$userId, $days]);
    }
    
    /**
     * Analyze effectiveness for a specific medication
     */
    private function analyzeMedicationEffectiveness($medication, $symptoms, $days) {
        $adherenceRate = $this->calculateAdherenceRate($medication, $days);
        $timingConsistency = $this->calculateTimingConsistency($medication);
        $symptomCorrelation = $this->calculateSymptomCorrelation($medication, $symptoms);
        
        return [
            'medication' => [
                'id' => $medication['id'],
                'name' => $medication['name'],
                'dosage' => $medication['dosage'] . ' ' . $medication['dosage_unit']
            ],
            'adherence_rate' => $adherenceRate,
            'timing_consistency' => $timingConsistency,
            'symptom_correlation' => $symptomCorrelation,
            'effectiveness_score' => $this->calculateEffectivenessScore($adherenceRate, $timingConsistency, $symptomCorrelation),
            'insights' => $this->generateMedicationInsights($medication, $adherenceRate, $timingConsistency, $symptomCorrelation)
        ];
    }
    
    /**
     * Calculate medication adherence rate
     */
    private function calculateAdherenceRate($medication, $days) {
        $totalLogs = count($medication['logs']);
        $takenLogs = count(array_filter($medication['logs'], function($log) {
            return $log['taken'] == 1;
        }));
        
        // Calculate expected doses based on reminders
        $expectedDoses = count($medication['reminders']) * $days;
        
        if ($expectedDoses == 0) {
            return 0;
        }
        
        return round(($takenLogs / $expectedDoses) * 100, 1);
    }
    
    /**
     * Calculate timing consistency
     */
    private function calculateTimingConsistency($medication) {
        if (empty($medication['logs']) || empty($medication['reminders'])) {
            return 0;
        }
        
        $consistencyScores = [];
        
        foreach ($medication['logs'] as $log) {
            if ($log['taken'] != 1) continue;
            
            $logTime = strtotime($log['time']);
            $bestMatch = null;
            $smallestDiff = PHP_INT_MAX;
            
            foreach ($medication['reminders'] as $reminderTime) {
                $reminderTimestamp = strtotime($reminderTime);
                $diff = abs($logTime - $reminderTimestamp);
                
                if ($diff < $smallestDiff) {
                    $smallestDiff = $diff;
                    $bestMatch = $reminderTime;
                }
            }
            
            // Score based on how close to reminder time (within 2 hours = 100%)
            $maxDiff = 2 * 60 * 60; // 2 hours in seconds
            $score = max(0, 100 - (($smallestDiff / $maxDiff) * 100));
            $consistencyScores[] = $score;
        }
        
        return empty($consistencyScores) ? 0 : round(array_sum($consistencyScores) / count($consistencyScores), 1);
    }
    
    /**
     * Calculate correlation with symptom improvements
     */
    private function calculateSymptomCorrelation($medication, $symptoms) {
        if (empty($medication['logs']) || empty($symptoms)) {
            return ['overall' => 0, 'by_symptom' => []];
        }
        
        // Group symptoms by date
        $symptomsByDate = [];
        foreach ($symptoms as $symptom) {
            $symptomsByDate[$symptom['log_date']] = $symptom;
        }
        
        // Group medication logs by date
        $medicationByDate = [];
        foreach ($medication['logs'] as $log) {
            if ($log['taken'] == 1) {
                $medicationByDate[$log['date']] = true;
            }
        }
        
        $correlations = [];
        $symptomTypes = ['focus_avg', 'hyperactivity_avg', 'impulsivity_avg', 'mood_avg', 'energy_avg', 'sleep_avg'];
        
        foreach ($symptomTypes as $symptomType) {
            $takenDays = [];
            $notTakenDays = [];
            
            foreach ($symptomsByDate as $date => $symptomData) {
                if ($symptomData[$symptomType] !== null) {
                    if (isset($medicationByDate[$date])) {
                        $takenDays[] = $symptomData[$symptomType];
                    } else {
                        $notTakenDays[] = $symptomData[$symptomType];
                    }
                }
            }
            
            if (!empty($takenDays) && !empty($notTakenDays)) {
                $takenAvg = array_sum($takenDays) / count($takenDays);
                $notTakenAvg = array_sum($notTakenDays) / count($notTakenDays);
                
                // For symptoms, lower is better, so improvement is negative difference
                $improvement = $notTakenAvg - $takenAvg;
                $correlations[str_replace('_avg', '', $symptomType)] = round($improvement, 2);
            }
        }
        
        $overallCorrelation = empty($correlations) ? 0 : round(array_sum($correlations) / count($correlations), 2);
        
        return [
            'overall' => $overallCorrelation,
            'by_symptom' => $correlations
        ];
    }
    
    /**
     * Calculate overall effectiveness score
     */
    private function calculateEffectivenessScore($adherence, $timing, $correlation) {
        $adherenceWeight = 0.4;
        $timingWeight = 0.3;
        $correlationWeight = 0.3;
        
        // Normalize correlation (0-10 scale)
        $normalizedCorrelation = max(0, min(100, ($correlation['overall'] + 5) * 10));
        
        $score = ($adherence * $adherenceWeight) + 
                 ($timing * $timingWeight) + 
                 ($normalizedCorrelation * $correlationWeight);
        
        return round($score, 1);
    }
    
    /**
     * Generate insights for a medication
     */
    private function generateMedicationInsights($medication, $adherence, $timing, $correlation) {
        $insights = [];
        
        if ($adherence < 70) {
            $insights[] = [
                'type' => 'warning',
                'message' => 'Low adherence rate. Consider setting more reminders or discussing barriers with your healthcare provider.'
            ];
        } elseif ($adherence > 90) {
            $insights[] = [
                'type' => 'success',
                'message' => 'Excellent adherence! You\'re consistently taking your medication as prescribed.'
            ];
        }
        
        if ($timing < 60) {
            $insights[] = [
                'type' => 'info',
                'message' => 'Timing could be more consistent. Try setting alarms or using the app notifications.'
            ];
        }
        
        if ($correlation['overall'] > 1) {
            $insights[] = [
                'type' => 'success',
                'message' => 'This medication appears to be helping with your symptoms.'
            ];
        } elseif ($correlation['overall'] < -1) {
            $insights[] = [
                'type' => 'warning',
                'message' => 'Consider discussing this medication\'s effectiveness with your healthcare provider.'
            ];
        }
        
        return $insights;
    }
    
    /**
     * Calculate overall correlation across all medications
     */
    private function calculateOverallCorrelation($analysis) {
        if (empty($analysis)) return 0;
        
        $scores = array_column($analysis, 'effectiveness_score');
        return round(array_sum($scores) / count($scores), 1);
    }
    
    /**
     * Generate general recommendations
     */
    private function generateRecommendations($analysis) {
        $recommendations = [];
        
        $avgAdherence = array_sum(array_column($analysis, 'adherence_rate')) / count($analysis);
        $avgTiming = array_sum(array_column($analysis, 'timing_consistency')) / count($analysis);
        
        if ($avgAdherence < 80) {
            $recommendations[] = [
                'type' => 'adherence',
                'priority' => 'high',
                'title' => 'Improve Medication Adherence',
                'description' => 'Consider using pill organizers, setting multiple alarms, or discussing simpler dosing schedules with your doctor.'
            ];
        }
        
        if ($avgTiming < 70) {
            $recommendations[] = [
                'type' => 'timing',
                'priority' => 'medium',
                'title' => 'Improve Timing Consistency',
                'description' => 'Taking medications at consistent times can improve effectiveness. Use the app\'s notification system.'
            ];
        }
        
        $recommendations[] = [
            'type' => 'tracking',
            'priority' => 'low',
            'title' => 'Continue Tracking',
            'description' => 'Keep logging your medications and symptoms to build a comprehensive picture of what works best for you.'
        ];
        
        return $recommendations;
    }

    /**
     * Generate healthcare provider report
     */
    public function generateHealthcareReport($userId, $days = 90) {
        $analysis = $this->getEffectivenessAnalysis($userId, $days);
        $patientInfo = $this->getPatientInfo($userId);

        return [
            'patient' => $patientInfo,
            'report_period' => [
                'start_date' => date('Y-m-d', strtotime("-{$days} days")),
                'end_date' => date('Y-m-d'),
                'days' => $days
            ],
            'summary' => [
                'total_medications' => count($analysis['medications']),
                'average_adherence' => $this->calculateAverageAdherence($analysis['medications']),
                'average_effectiveness' => $analysis['overall_correlation'],
                'key_concerns' => $this->identifyKeyConcerns($analysis['medications'])
            ],
            'medications' => $analysis['medications'],
            'recommendations' => $analysis['recommendations'],
            'generated_at' => date('Y-m-d H:i:s')
        ];
    }

    private function getPatientInfo($userId) {
        $sql = "SELECT name, email, created_at FROM users WHERE id = ?";
        $user = $this->db->fetchOne($sql, [$userId]);

        return [
            'name' => $user['name'] ?? 'Unknown',
            'user_id' => $userId,
            'member_since' => $user['created_at'] ?? null
        ];
    }

    private function calculateAverageAdherence($medications) {
        if (empty($medications)) return 0;

        $adherenceRates = array_column($medications, 'adherence_rate');
        return round(array_sum($adherenceRates) / count($adherenceRates), 1);
    }

    private function identifyKeyConcerns($medications) {
        $concerns = [];

        foreach ($medications as $med) {
            if ($med['adherence_rate'] < 70) {
                $concerns[] = "Low adherence for {$med['medication']['name']} ({$med['adherence_rate']}%)";
            }

            if ($med['timing_consistency'] < 60) {
                $concerns[] = "Inconsistent timing for {$med['medication']['name']}";
            }

            if ($med['symptom_correlation']['overall'] < -1) {
                $concerns[] = "Possible effectiveness issues with {$med['medication']['name']}";
            }
        }

        return array_slice($concerns, 0, 5); // Top 5 concerns
    }
}
