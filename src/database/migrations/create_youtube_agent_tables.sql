-- Create youtube_searches table
CREATE TABLE IF NOT EXISTS youtube_searches (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    search_query VARCHAR(255) NOT NULL,
    search_type VARCHAR(50) NOT NULL DEFAULT 'keyword', -- keyword, channel, trending, etc.
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, in_progress, completed, failed
    results_count INT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create youtube_videos table
CREATE TABLE IF NOT EXISTS youtube_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    youtube_id VARCHAR(50) NOT NULL,
    search_id INT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NULL,
    channel_id VARCHAR(100) NULL,
    channel_title VARCHAR(255) NULL,
    published_at DATETIME NULL,
    view_count BIGINT NULL,
    like_count INT NULL,
    comment_count INT NULL,
    duration VARCHAR(50) NULL,
    thumbnail_url VARCHAR(255) NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (search_id) REFERENCES youtube_searches(id) ON DELETE SET NULL,
    UNIQUE KEY (youtube_id)
);

-- Create youtube_analysis table
CREATE TABLE IF NOT EXISTS youtube_analysis (
    id INT AUTO_INCREMENT PRIMARY KEY,
    video_id INT NOT NULL,
    user_id INT NOT NULL,
    analysis_type VARCHAR(50) NOT NULL DEFAULT 'money_making', -- money_making, content_ideas, etc.
    status VARCHAR(50) NOT NULL DEFAULT 'pending', -- pending, in_progress, completed, failed
    summary TEXT NULL,
    transcript TEXT NULL,
    key_points TEXT NULL,
    opportunities TEXT NULL,
    implementation_steps TEXT NULL,
    estimated_roi VARCHAR(255) NULL,
    difficulty_level VARCHAR(50) NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (video_id) REFERENCES youtube_videos(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create youtube_tags table
CREATE TABLE IF NOT EXISTS youtube_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    video_id INT NOT NULL,
    tag VARCHAR(255) NOT NULL,
    created_at DATETIME NOT NULL,
    FOREIGN KEY (video_id) REFERENCES youtube_videos(id) ON DELETE CASCADE,
    UNIQUE KEY (video_id, tag)
);

-- Create youtube_money_making_strategies table
CREATE TABLE IF NOT EXISTS youtube_money_making_strategies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    analysis_id INT NOT NULL,
    strategy_name VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    potential_revenue VARCHAR(255) NULL,
    time_investment VARCHAR(255) NULL,
    required_skills TEXT NULL,
    required_resources TEXT NULL,
    implementation_difficulty VARCHAR(50) NULL,
    suitable_brigade VARCHAR(50) NULL, -- content_creation, lead_generation, etc.
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (analysis_id) REFERENCES youtube_analysis(id) ON DELETE CASCADE
);

-- Create youtube_saved_videos table
CREATE TABLE IF NOT EXISTS youtube_saved_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    video_id INT NOT NULL,
    folder VARCHAR(255) NULL,
    notes TEXT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (video_id) REFERENCES youtube_videos(id) ON DELETE CASCADE,
    UNIQUE KEY (user_id, video_id)
);

-- Create youtube_api_quota table
CREATE TABLE IF NOT EXISTS youtube_api_quota (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    quota_used INT NOT NULL DEFAULT 0,
    quota_limit INT NOT NULL DEFAULT 10000,
    reset_date DATE NOT NULL,
    created_at DATETIME NOT NULL,
    updated_at DATETIME NOT NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY (user_id, reset_date)
);
