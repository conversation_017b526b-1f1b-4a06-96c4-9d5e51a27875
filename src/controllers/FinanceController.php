<?php
/**
 * Finance Controller
 *
 * Handles finance-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/Finance.php';
require_once __DIR__ . '/../models/Subscription.php';
require_once __DIR__ . '/../models/Debt.php';
require_once __DIR__ . '/../models/Budget.php';
require_once __DIR__ . '/../models/IncomeSource.php';
require_once __DIR__ . '/../models/FinancialGoal.php';
require_once __DIR__ . '/../models/Receipt.php';
require_once __DIR__ . '/../utils/FileUploadUtil.php';

class FinanceController extends BaseController {
    private $financeModel;
    private $subscriptionModel;
    private $debtModel;
    private $budgetModel;
    private $incomeSourceModel;
    private $goalModel;
    private $receiptModel;

    public function __construct() {
        $this->financeModel = new Finance();
        $this->subscriptionModel = new Subscription();
        $this->debtModel = new Debt();
        $this->budgetModel = new Budget();
        $this->incomeSourceModel = new IncomeSource();
        $this->goalModel = new FinancialGoal();
        $this->receiptModel = new Receipt();
    }

    /**
     * Show finance dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Sanitize and validate filters
        $filters = [
            'start_date' => isset($filters['start_date']) && strtotime($filters['start_date'])
                ? date('Y-m-d', strtotime($filters['start_date']))
                : date('Y-m-01'),
            'end_date' => isset($filters['end_date']) && strtotime($filters['end_date'])
                ? date('Y-m-d', strtotime($filters['end_date']))
                : date('Y-m-t'),
            'type' => in_array($filters['type'] ?? '', ['income', 'expense']) ? $filters['type'] : null,
            'category' => !empty($filters['category']) ? trim($filters['category']) : null,
            'transaction_type' => in_array($filters['transaction_type'] ?? '', ['monetary', 'non_monetary']) ? $filters['transaction_type'] : null,
            'payment_method' => in_array($filters['payment_method'] ?? '', ['cash', 'credit_card', 'debit_card', 'bank_transfer', 'other']) ? $filters['payment_method'] : null
        ];

        // Get all unique categories for the filter dropdown
        $categories = $this->financeModel->getUniqueCategories($userId);

        // Get transactions based on filters
        $transactions = $this->financeModel->getUserTransactions($userId, $filters);

        // Get financial summary
        $summary = $this->financeModel->getSummary($userId, $filters['start_date'], $filters['end_date']);

        // Get comprehensive summary including non-monetary transactions
        $comprehensiveSummary = $this->financeModel->getComprehensiveSummary($userId, $filters['start_date'], $filters['end_date']);

        // Get spending by category
        $spendingByCategory = $this->financeModel->getSpendingByCategory($userId, $filters['start_date'], $filters['end_date']);

        // Get subscriptions
        $subscriptions = $this->subscriptionModel->getUserSubscriptions($userId);
        $monthlyCost = $this->subscriptionModel->getTotalMonthlyCost($userId);

        // Get debt summary
        $debtSummary = $this->debtModel->getDebtSummary($userId);

        // Get active budget
        $activeBudget = $this->budgetModel->getActiveBudget($userId);

        // Get budget progress if active budget exists
        $budgetProgress = null;
        if ($activeBudget) {
            $budgetProgress = $this->budgetModel->getBudgetProgress($activeBudget['id']);
        }

        // Get income sources summary
        $incomeSources = $this->incomeSourceModel->getSourcesWithTotalIncome(
            $userId,
            $filters['start_date'],
            $filters['end_date']
        );

        // Get total income from sources
        $totalSourceIncome = 0;
        foreach ($incomeSources as $source) {
            $totalSourceIncome += (float)($source['total_income'] ?? 0);
        }

        // Get active financial goals
        $activeGoals = $this->goalModel->getActiveGoals($userId);

        // Get financial goals summary
        $goalsSummary = $this->goalModel->getGoalsSummary($userId);

        $this->view('finances/index', [
            'transactions' => $transactions,
            'summary' => $summary,
            'comprehensiveSummary' => $comprehensiveSummary,
            'spendingByCategory' => $spendingByCategory,
            'subscriptions' => $subscriptions,
            'monthlyCost' => $monthlyCost,
            'debtSummary' => $debtSummary,
            'activeBudget' => $activeBudget,
            'budgetProgress' => $budgetProgress,
            'incomeSources' => $incomeSources,
            'totalSourceIncome' => $totalSourceIncome,
            'activeGoals' => $activeGoals,
            'goalsSummary' => $goalsSummary,
            'filters' => $filters,
            'categories' => $categories
        ]);
    }

    /**
     * Show transaction creation form
     */
    public function createTransaction() {
        $this->requireLogin();

        $this->view('finances/create_transaction');
    }

    /**
     * Process transaction creation
     */
    public function storeTransaction() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['type', 'category', 'date'];

        // Add required fields based on transaction type
        if (isset($data['transaction_type']) && $data['transaction_type'] === 'non_monetary') {
            $requiredFields[] = 'fair_market_value';
            $requiredFields[] = 'goods_services_description';
        } else {
            $requiredFields[] = 'amount';
            $requiredFields[] = 'payment_method';
        }

        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            $this->view('finances/create_transaction', [
                'errors' => $errors,
                'data' => $data
            ]);
            return;
        }

        // Validate amount or fair market value
        if (isset($data['transaction_type']) && $data['transaction_type'] === 'non_monetary') {
            if (!is_numeric($data['fair_market_value']) || $data['fair_market_value'] <= 0) {
                $errors['fair_market_value'] = 'Estimated value must be a positive number';
                $this->view('finances/create_transaction', [
                    'errors' => $errors,
                    'data' => $data
                ]);
                return;
            }
        } else {
            if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
                $errors['amount'] = 'Amount must be a positive number';
                $this->view('finances/create_transaction', [
                    'errors' => $errors,
                    'data' => $data
                ]);
                return;
            }
        }

        // Prepare transaction data
        $transactionData = [
            'user_id' => $userId,
            'type' => $data['type'],
            'amount' => $data['amount'] ?? 0,
            'category' => $data['category'],
            'description' => $data['description'] ?? null,
            'transaction_type' => $data['transaction_type'] ?? 'monetary',
            'date' => $data['date'],
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add payment method for monetary transactions
        if (!isset($data['transaction_type']) || $data['transaction_type'] === 'monetary') {
            $transactionData['payment_method'] = $data['payment_method'] ?? 'cash';
        }

        // Add non-monetary transaction details
        if (isset($data['transaction_type']) && $data['transaction_type'] === 'non_monetary') {
            $transactionData['fair_market_value'] = $data['fair_market_value'];
            $transactionData['goods_services_description'] = $data['goods_services_description'];

            // For non-monetary transactions, set amount to 0 explicitly
            $transactionData['amount'] = 0;

            // Debug log for non-monetary transaction
            error_log("Creating non-monetary transaction: " . print_r($transactionData, true));
            error_log("POST data: " . print_r($data, true));
        }

        // Create transaction
        $transactionId = $this->financeModel->create($transactionData);

        if ($transactionId) {
            // If this is an income transaction and an income source was selected, link them
            if ($data['type'] === 'income' && !empty($data['income_source_id'])) {
                $this->incomeSourceModel->linkTransaction($data['income_source_id'], $transactionId);
            }

            // Handle receipt upload if file was submitted
            if (isset($_FILES['receipt']) && $_FILES['receipt']['error'] !== UPLOAD_ERR_NO_FILE) {
                $this->handleReceiptUpload($_FILES['receipt'], $transactionId, $userId);
            }

            Session::setFlash('success', 'Transaction added successfully');
            $this->redirect('/finances');
        } else {
            Session::setFlash('error', 'Failed to add transaction');

            $this->view('finances/create_transaction', [
                'data' => $data
            ]);
        }
    }

    /**
     * Show transaction edit form
     */
    public function editTransaction($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get transaction
        $transaction = $this->financeModel->find($id);

        // Verify transaction exists and belongs to user
        if (!$transaction || $transaction['user_id'] != $userId) {
            Session::setFlash('error', 'Transaction not found');
            $this->redirect('/finances');
        }

        $this->view('finances/edit_transaction', [
            'transaction' => $transaction
        ]);
    }

    /**
     * Process transaction update
     */
    public function updateTransaction($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get transaction
        $transaction = $this->financeModel->find($id);

        // Verify transaction exists and belongs to user
        if (!$transaction || $transaction['user_id'] != $userId) {
            Session::setFlash('error', 'Transaction not found');
            $this->redirect('/finances');
        }

        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['type', 'category', 'date'];

        // Add required fields based on transaction type
        if (isset($data['transaction_type']) && $data['transaction_type'] === 'non_monetary') {
            $requiredFields[] = 'fair_market_value';
            $requiredFields[] = 'goods_services_description';
        } else {
            $requiredFields[] = 'amount';
            $requiredFields[] = 'payment_method';
        }

        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            $this->view('finances/edit_transaction', [
                'errors' => $errors,
                'transaction' => array_merge($transaction, $data)
            ]);
            return;
        }

        // Validate amount or fair market value
        if (isset($data['transaction_type']) && $data['transaction_type'] === 'non_monetary') {
            if (!is_numeric($data['fair_market_value']) || $data['fair_market_value'] <= 0) {
                $errors['fair_market_value'] = 'Estimated value must be a positive number';
                $this->view('finances/edit_transaction', [
                    'errors' => $errors,
                    'transaction' => array_merge($transaction, $data)
                ]);
                return;
            }
        } else {
            if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
                $errors['amount'] = 'Amount must be a positive number';
                $this->view('finances/edit_transaction', [
                    'errors' => $errors,
                    'transaction' => array_merge($transaction, $data)
                ]);
                return;
            }
        }

        // Prepare transaction data
        $transactionData = [
            'type' => $data['type'],
            'amount' => $data['amount'] ?? 0,
            'category' => $data['category'],
            'description' => $data['description'] ?? null,
            'transaction_type' => $data['transaction_type'] ?? 'monetary',
            'date' => $data['date'],
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Add payment method for monetary transactions
        if (!isset($data['transaction_type']) || $data['transaction_type'] === 'monetary') {
            $transactionData['payment_method'] = $data['payment_method'] ?? 'cash';
        }

        // Add non-monetary transaction details
        if (isset($data['transaction_type']) && $data['transaction_type'] === 'non_monetary') {
            $transactionData['fair_market_value'] = $data['fair_market_value'];
            $transactionData['goods_services_description'] = $data['goods_services_description'];

            // For non-monetary transactions, set amount to 0 explicitly
            $transactionData['amount'] = 0;

            // Debug log for non-monetary transaction update
            error_log("Updating non-monetary transaction: " . print_r($transactionData, true));
            error_log("POST data for update: " . print_r($data, true));
        }

        // Update transaction
        $result = $this->financeModel->update($id, $transactionData);

        if ($result) {
            // Handle income source linking for income transactions
            if ($data['type'] === 'income') {
                // First, remove any existing links
                $sourceTransactions = $this->incomeSourceModel->getSourceTransactionsByTransactionId($id);
                foreach ($sourceTransactions as $sourceTransaction) {
                    $this->incomeSourceModel->unlinkTransaction($sourceTransaction['income_source_id'], $id);
                }

                // Then, add new link if an income source was selected
                if (!empty($data['income_source_id'])) {
                    $this->incomeSourceModel->linkTransaction($data['income_source_id'], $id);
                }
            }

            // Handle receipt upload if file was submitted
            if (isset($_FILES['receipt']) && $_FILES['receipt']['error'] !== UPLOAD_ERR_NO_FILE) {
                $this->handleReceiptUpload($_FILES['receipt'], $id, $userId);
            }

            Session::setFlash('success', 'Transaction updated successfully');
            $this->redirect('/finances');
        } else {
            Session::setFlash('error', 'Failed to update transaction');

            $this->view('finances/edit_transaction', [
                'transaction' => array_merge($transaction, $data)
            ]);
        }
    }

    /**
     * Delete transaction
     */
    public function deleteTransaction($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get transaction
        $transaction = $this->financeModel->find($id);

        // Verify transaction exists and belongs to user
        if (!$transaction || $transaction['user_id'] != $userId) {
            Session::setFlash('error', 'Transaction not found');
            $this->redirect('/finances');
        }

        // Remove any income source links
        $sourceTransactions = $this->incomeSourceModel->getSourceTransactionsByTransactionId($id);
        foreach ($sourceTransactions as $sourceTransaction) {
            $this->incomeSourceModel->unlinkTransaction($sourceTransaction['income_source_id'], $id);
        }

        // Delete associated receipts
        $receipts = $this->receiptModel->getTransactionReceipts($id);
        foreach ($receipts as $receipt) {
            $this->receiptModel->deleteReceipt($receipt['id'], $userId);
        }

        // Delete transaction
        $result = $this->financeModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Transaction deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete transaction');
        }

        $this->redirect('/finances');
    }

    /**
     * Show subscription creation form
     */
    public function createSubscription() {
        $this->requireLogin();

        $this->view('finances/create_subscription');
    }

    /**
     * Process subscription creation
     */
    public function storeSubscription() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name', 'amount', 'billing_cycle', 'next_billing_date']);

        if (!empty($errors)) {
            $this->view('finances/create_subscription', [
                'errors' => $errors,
                'data' => $data
            ]);
            return;
        }

        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            $errors['amount'] = 'Amount must be a positive number';
            $this->view('finances/create_subscription', [
                'errors' => $errors,
                'data' => $data
            ]);
            return;
        }

        // Process features list
        $featuresList = [
            'main_title' => $data['main_title'] ?? '',
            'main_features' => [],
            'additional_title' => $data['additional_title'] ?? '',
            'additional_features' => []
        ];

        // Process main features
        if (isset($data['main_features']) && is_array($data['main_features'])) {
            foreach ($data['main_features'] as $feature) {
                if (!empty(trim($feature))) {
                    $featuresList['main_features'][] = trim($feature);
                }
            }
        }

        // Process additional features
        if (isset($data['additional_features']) && is_array($data['additional_features'])) {
            foreach ($data['additional_features'] as $feature) {
                if (!empty(trim($feature))) {
                    $featuresList['additional_features'][] = trim($feature);
                }
            }
        }

        // Prepare subscription data
        $subscriptionData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'amount' => $data['amount'],
            'billing_cycle' => $data['billing_cycle'],
            'category' => $data['category'] ?? null,
            'description' => $data['description'] ?? null,
            'features_list' => !empty($featuresList['main_features']) || !empty($featuresList['additional_features'])
                ? json_encode($featuresList)
                : null,
            'next_billing_date' => $data['next_billing_date'],
            'status' => $data['status'] ?? 'active',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create subscription
        $subscriptionId = $this->subscriptionModel->create($subscriptionData);

        if ($subscriptionId) {
            Session::setFlash('success', 'Subscription added successfully');
            $this->redirect('/finances/subscriptions');
        } else {
            Session::setFlash('error', 'Failed to add subscription');

            $this->view('finances/create_subscription', [
                'data' => $data
            ]);
        }
    }

    /**
     * Show subscriptions list
     */
    public function subscriptions() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get subscriptions
        $subscriptions = $this->subscriptionModel->getUserSubscriptions($userId);
        $monthlyCost = $this->subscriptionModel->getTotalMonthlyCost($userId);

        $this->view('finances/subscriptions', [
            'subscriptions' => $subscriptions,
            'monthlyCost' => $monthlyCost
        ]);
    }

    /**
     * Show subscription edit form
     */
    public function editSubscription($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get subscription
        $subscription = $this->subscriptionModel->find($id);

        // Verify subscription exists and belongs to user
        if (!$subscription || $subscription['user_id'] != $userId) {
            Session::setFlash('error', 'Subscription not found');
            $this->redirect('/finances/subscriptions');
        }

        $this->view('finances/edit_subscription', [
            'subscription' => $subscription
        ]);
    }

    /**
     * Process subscription update
     */
    public function updateSubscription($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get subscription
        $subscription = $this->subscriptionModel->find($id);

        // Verify subscription exists and belongs to user
        if (!$subscription || $subscription['user_id'] != $userId) {
            Session::setFlash('error', 'Subscription not found');
            $this->redirect('/finances/subscriptions');
        }

        $data = $this->getPostData();

        // Validate required fields
        $errors = $this->validateRequired($data, ['name', 'amount', 'billing_cycle', 'next_billing_date']);

        if (!empty($errors)) {
            $this->view('finances/edit_subscription', [
                'errors' => $errors,
                'subscription' => array_merge($subscription, $data)
            ]);
            return;
        }

        // Validate amount
        if (!is_numeric($data['amount']) || $data['amount'] <= 0) {
            $errors['amount'] = 'Amount must be a positive number';
            $this->view('finances/edit_subscription', [
                'errors' => $errors,
                'subscription' => array_merge($subscription, $data)
            ]);
            return;
        }

        // Process features list
        $featuresList = [
            'main_title' => $data['main_title'] ?? '',
            'main_features' => [],
            'additional_title' => $data['additional_title'] ?? '',
            'additional_features' => []
        ];

        // Process main features
        if (isset($data['main_features']) && is_array($data['main_features'])) {
            foreach ($data['main_features'] as $feature) {
                if (!empty(trim($feature))) {
                    $featuresList['main_features'][] = trim($feature);
                }
            }
        }

        // Process additional features
        if (isset($data['additional_features']) && is_array($data['additional_features'])) {
            foreach ($data['additional_features'] as $feature) {
                if (!empty(trim($feature))) {
                    $featuresList['additional_features'][] = trim($feature);
                }
            }
        }

        // Prepare subscription data
        $subscriptionData = [
            'name' => $data['name'],
            'amount' => $data['amount'],
            'billing_cycle' => $data['billing_cycle'],
            'category' => $data['category'] ?? null,
            'description' => $data['description'] ?? null,
            'features_list' => !empty($featuresList['main_features']) || !empty($featuresList['additional_features'])
                ? json_encode($featuresList)
                : null,
            'next_billing_date' => $data['next_billing_date'],
            'status' => $data['status'] ?? 'active',
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update subscription
        $result = $this->subscriptionModel->update($id, $subscriptionData);

        if ($result) {
            Session::setFlash('success', 'Subscription updated successfully');
            $this->redirect('/finances/subscriptions');
        } else {
            Session::setFlash('error', 'Failed to update subscription');

            $this->view('finances/edit_subscription', [
                'subscription' => array_merge($subscription, $data)
            ]);
        }
    }

    /**
     * Delete subscription
     */
    public function deleteSubscription($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get subscription
        $subscription = $this->subscriptionModel->find($id);

        // Verify subscription exists and belongs to user
        if (!$subscription || $subscription['user_id'] != $userId) {
            Session::setFlash('error', 'Subscription not found');
            $this->redirect('/finances/subscriptions');
        }

        // Delete subscription
        $result = $this->subscriptionModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Subscription deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete subscription');
        }

        $this->redirect('/finances/subscriptions');
    }

    /**
     * Show subscription details
     */
    public function subscriptionDetails($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get subscription
        $subscription = $this->subscriptionModel->find($id);

        // Verify subscription exists and belongs to user
        if (!$subscription || $subscription['user_id'] != $userId) {
            Session::setFlash('error', 'Subscription not found');
            $this->redirect('/finances/subscriptions');
        }

        $this->view('finances/subscription_details', [
            'subscription' => $subscription
        ]);
    }

    /**
     * Handle receipt upload for a transaction
     *
     * @param array $file The uploaded file from $_FILES
     * @param int $transactionId The transaction ID
     * @param int $userId The user ID
     * @return bool True on success, false on failure
     */
    private function handleReceiptUpload($file, $transactionId, $userId) {
        // Upload the receipt file
        $uploadResult = FileUploadUtil::uploadReceipt($file, $userId);

        if (!$uploadResult['success']) {
            // Log the error
            error_log('Receipt upload failed: ' . print_r($uploadResult['errors'], true));
            Session::setFlash('error', 'Failed to upload receipt: ' . implode(', ', $uploadResult['errors']));
            return false;
        }

        // Prepare receipt data
        $receiptData = [
            'transaction_id' => $transactionId,
            'user_id' => $userId,
            'file_name' => $uploadResult['file_name'],
            'file_path' => $uploadResult['file_path'],
            'file_type' => $uploadResult['file_type'],
            'file_size' => $uploadResult['file_size'],
            'upload_date' => date('Y-m-d H:i:s'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Save receipt to database
        $receiptId = $this->receiptModel->addReceipt($receiptData);

        if (!$receiptId) {
            // Log the error
            error_log('Failed to save receipt to database');
            Session::setFlash('error', 'Failed to save receipt information');
            return false;
        }

        Session::setFlash('success', 'Receipt uploaded successfully');
        return true;
    }

    /**
     * View transaction receipts
     */
    public function viewReceipts($transactionId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get transaction
        $transaction = $this->financeModel->find($transactionId);

        // Verify transaction exists and belongs to user
        if (!$transaction || $transaction['user_id'] != $userId) {
            Session::setFlash('error', 'Transaction not found');
            $this->redirect('/finances');
        }

        // Get receipts for this transaction
        $receipts = $this->receiptModel->getTransactionReceipts($transactionId);

        $this->view('finances/view_receipts', [
            'transaction' => $transaction,
            'receipts' => $receipts
        ]);
    }

    /**
     * Delete a receipt
     */
    public function deleteReceipt($receiptId) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get receipt
        $receipt = $this->receiptModel->find($receiptId);

        // Verify receipt exists and belongs to user
        if (!$receipt || $receipt['user_id'] != $userId) {
            Session::setFlash('error', 'Receipt not found');
            $this->redirect('/finances');
        }

        // Get transaction ID for redirect
        $transactionId = $receipt['transaction_id'];

        // Delete receipt
        $result = $this->receiptModel->deleteReceipt($receiptId, $userId);

        if ($result) {
            Session::setFlash('success', 'Receipt deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete receipt');
        }

        // Check if we came from the all receipts page
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        if (strpos($referer, '/finances/receipts') !== false) {
            $this->redirect('/finances/receipts');
        } else {
            $this->redirect('/finances/transactions/' . $transactionId . '/receipts');
        }
    }

    /**
     * View all receipts
     */
    public function viewAllReceipts() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filters from query parameters
        $filters = [];
        if (!empty($_GET['transaction_type'])) {
            $filters['transaction_type'] = $_GET['transaction_type'];
        }
        if (!empty($_GET['category'])) {
            $filters['category'] = $_GET['category'];
        }
        if (!empty($_GET['date_from'])) {
            $filters['date_from'] = $_GET['date_from'];
        }
        if (!empty($_GET['date_to'])) {
            $filters['date_to'] = $_GET['date_to'];
        }

        // Get all receipts for this user with transaction details
        $receipts = $this->receiptModel->getUserReceipts($userId, $filters);

        $this->view('finances/receipts', [
            'receipts' => $receipts,
            'filters' => $filters
        ]);
    }
}





