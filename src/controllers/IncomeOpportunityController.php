<?php
/**
 * Income Opportunity Controller
 *
 * Handles income opportunity-related functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/IncomeOpportunity.php';
require_once __DIR__ . '/../utils/Database.php';

class IncomeOpportunityController extends BaseController {
    private $opportunityModel;

    public function __construct() {
        $this->opportunityModel = new IncomeOpportunity();
    }

    /**
     * Show income opportunities dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Add debug logging
        error_log("Income Opportunities Index - User ID: $userId");

        // Get filter parameters
        $filters = $this->getQueryData();
        error_log("Filters: " . print_r($filters, true));

        // Get opportunities based on filters
        $opportunities = $this->opportunityModel->getUserOpportunities($userId, $filters);
        error_log("Opportunities count: " . ($opportunities ? count($opportunities) : 0));

        // Get opportunity summary
        $opportunitySummary = $this->opportunityModel->getOpportunitySummary($userId);
        error_log("Opportunity Summary: " . print_r($opportunitySummary, true));

        // Get categories for filter dropdown
        $categories = $this->opportunityModel->getCategories($userId);

        // Check if we have any opportunities directly from the database
        $db = Database::getInstance();
        $directDbCheck = $db->fetchAll("SELECT * FROM income_opportunities WHERE user_id = ?", [$userId]);
        error_log("Direct DB check - Opportunities count: " . ($directDbCheck ? count($directDbCheck) : 0));

        $this->view('income/index', [
            'opportunities' => $opportunities,
            'opportunitySummary' => $opportunitySummary,
            'categories' => $categories,
            'filters' => $filters
        ]);
    }

    /**
     * Show opportunity creation form
     */
    public function create() {
        $this->requireLogin();

        $this->view('income/create');
    }

    /**
     * Store a new opportunity
     */
    public function store() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'category', 'income_type', 'skill_level', 'startup_cost', 'time_commitment', 'income_frequency'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->view('income/create', [
                'data' => $data,
                'errors' => $errors
            ]);
            return;
        }

        // Prepare opportunity data
        $opportunityData = [
            'user_id' => $userId,
            'name' => $data['name'],
            'description' => !empty($data['description']) ? $data['description'] : null,
            'category' => $data['category'],
            'income_type' => $data['income_type'],
            'skill_level' => $data['skill_level'],
            'startup_cost' => $data['startup_cost'],
            'time_commitment' => $data['time_commitment'],
            'estimated_income_min' => !empty($data['estimated_income_min']) ? $data['estimated_income_min'] : null,
            'estimated_income_max' => !empty($data['estimated_income_max']) ? $data['estimated_income_max'] : null,
            'income_frequency' => $data['income_frequency'],
            'time_to_first_income' => !empty($data['time_to_first_income']) ? $data['time_to_first_income'] : null,
            'status' => $data['status'] ?? 'considering',
            'priority' => $data['priority'] ?? 0,
            'notes' => !empty($data['notes']) ? $data['notes'] : null,
            'resources' => !empty($data['resources']) ? $data['resources'] : null,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Create opportunity
        $opportunityId = $this->opportunityModel->create($opportunityData);

        if ($opportunityId) {
            Session::setFlash('success', 'Income opportunity added successfully');
            // Add debug information to help diagnose the issue
            error_log("Income opportunity created with ID: $opportunityId for user: $userId");
            error_log("Redirecting to: /income-opportunities");
            $this->redirect('/income-opportunities');
        } else {
            Session::setFlash('error', 'Failed to add income opportunity');
            error_log("Failed to create income opportunity for user: $userId");

            $this->view('income/create', [
                'data' => $data
            ]);
        }
    }

    /**
     * Show opportunity details
     */
    public function viewOpportunity($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get opportunity details
        $opportunity = $this->opportunityModel->find($id);

        if (!$opportunity || $opportunity['user_id'] != $userId) {
            Session::setFlash('error', 'Income opportunity not found');
            $this->redirect('/income-opportunities');
        }

        // Get opportunity logs
        $logs = $this->opportunityModel->getOpportunityLogs($id);

        // Get opportunity milestones
        $milestones = $this->opportunityModel->getOpportunityMilestones($id);

        // Calculate metrics
        $totalEarnings = $this->opportunityModel->getTotalEarnings($id);
        $totalTimeInvested = $this->opportunityModel->getTotalTimeInvested($id);
        $roi = $this->opportunityModel->calculateROI($id);

        $this->view('income/view', [
            'opportunity' => $opportunity,
            'logs' => $logs,
            'milestones' => $milestones,
            'totalEarnings' => $totalEarnings,
            'totalTimeInvested' => $totalTimeInvested,
            'roi' => $roi
        ]);
    }

    /**
     * Show opportunity edit form
     */
    public function edit($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get opportunity details
        $opportunity = $this->opportunityModel->find($id);

        if (!$opportunity || $opportunity['user_id'] != $userId) {
            Session::setFlash('error', 'Income opportunity not found');
            $this->redirect('/income-opportunities');
        }

        $this->view('income/edit', [
            'opportunity' => $opportunity
        ]);
    }

    /**
     * Update an opportunity
     */
    public function update($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get opportunity details
        $opportunity = $this->opportunityModel->find($id);

        if (!$opportunity || $opportunity['user_id'] != $userId) {
            Session::setFlash('error', 'Income opportunity not found');
            $this->redirect('/income-opportunities');
        }

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['name', 'category', 'income_type', 'skill_level', 'startup_cost', 'time_commitment', 'income_frequency'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->view('income/edit', [
                'opportunity' => array_merge($opportunity, $data),
                'errors' => $errors
            ]);
            return;
        }

        // Prepare opportunity data
        $opportunityData = [
            'name' => $data['name'],
            'description' => !empty($data['description']) ? $data['description'] : null,
            'category' => $data['category'],
            'income_type' => $data['income_type'],
            'skill_level' => $data['skill_level'],
            'startup_cost' => $data['startup_cost'],
            'time_commitment' => $data['time_commitment'],
            'estimated_income_min' => !empty($data['estimated_income_min']) ? $data['estimated_income_min'] : null,
            'estimated_income_max' => !empty($data['estimated_income_max']) ? $data['estimated_income_max'] : null,
            'income_frequency' => $data['income_frequency'],
            'time_to_first_income' => !empty($data['time_to_first_income']) ? $data['time_to_first_income'] : null,
            'status' => $data['status'] ?? 'considering',
            'priority' => $data['priority'] ?? 0,
            'notes' => !empty($data['notes']) ? $data['notes'] : null,
            'resources' => !empty($data['resources']) ? $data['resources'] : null,
            'updated_at' => date('Y-m-d H:i:s')
        ];

        // Update opportunity
        $result = $this->opportunityModel->update($id, $opportunityData);

        if ($result) {
            Session::setFlash('success', 'Income opportunity updated successfully');
            $this->redirect('/income-opportunities/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to update income opportunity');

            $this->view('income/edit', [
                'opportunity' => array_merge($opportunity, $data)
            ]);
        }
    }

    /**
     * Delete an opportunity
     */
    public function delete($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get opportunity details
        $opportunity = $this->opportunityModel->find($id);

        if (!$opportunity || $opportunity['user_id'] != $userId) {
            Session::setFlash('error', 'Income opportunity not found');
            $this->redirect('/income-opportunities');
        }

        // Delete opportunity
        $result = $this->opportunityModel->delete($id);

        if ($result) {
            Session::setFlash('success', 'Income opportunity deleted successfully');
        } else {
            Session::setFlash('error', 'Failed to delete income opportunity');
        }

        $this->redirect('/income-opportunities');
    }

    /**
     * Show form to log activity for an opportunity
     */
    public function logActivity($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get opportunity details
        $opportunity = $this->opportunityModel->find($id);

        if (!$opportunity || $opportunity['user_id'] != $userId) {
            Session::setFlash('error', 'Income opportunity not found');
            $this->redirect('/income-opportunities');
        }

        $this->view('income/log_activity', [
            'opportunity' => $opportunity
        ]);
    }

    /**
     * Store activity log for an opportunity
     */
    public function saveActivityLog($id) {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get opportunity details
        $opportunity = $this->opportunityModel->find($id);

        if (!$opportunity || $opportunity['user_id'] != $userId) {
            Session::setFlash('error', 'Income opportunity not found');
            $this->redirect('/income-opportunities');
        }

        // Get form data
        $data = $this->getPostData();

        // Validate required fields
        $requiredFields = ['log_date'];
        $errors = $this->validateRequired($data, $requiredFields);

        if (!empty($errors)) {
            Session::setFlash('error', 'Please fill in all required fields');
            $this->view('income/log_activity', [
                'opportunity' => $opportunity,
                'data' => $data,
                'errors' => $errors
            ]);
            return;
        }

        // Prepare log data
        $logData = [
            'opportunity_id' => $id,
            'log_date' => $data['log_date'],
            'hours_spent' => $data['hours_spent'] ?? null,
            'amount_earned' => $data['amount_earned'] ?? null,
            'activities' => $data['activities'] ?? null,
            'challenges' => $data['challenges'] ?? null,
            'wins' => $data['wins'] ?? null,
            'next_steps' => $data['next_steps'] ?? null,
            'created_at' => date('Y-m-d H:i:s')
        ];

        // Add log
        $logId = $this->opportunityModel->addLog($logData);

        if ($logId) {
            Session::setFlash('success', 'Activity log added successfully');
            $this->redirect('/income-opportunities/view/' . $id);
        } else {
            Session::setFlash('error', 'Failed to add activity log');

            $this->view('income/log_activity', [
                'opportunity' => $opportunity,
                'data' => $data
            ]);
        }
    }
}
