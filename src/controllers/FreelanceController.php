<?php
/**
 * Freelance Controller
 *
 * Handles freelance management functionality.
 */

require_once __DIR__ . '/BaseController.php';
require_once __DIR__ . '/../models/FreelanceClient.php';
require_once __DIR__ . '/../models/FreelanceProject.php';
require_once __DIR__ . '/../models/FreelanceInvoice.php';
require_once __DIR__ . '/../models/FreelancePayment.php';

class FreelanceController extends BaseController {
    private $clientModel;
    private $projectModel;
    private $invoiceModel;
    private $paymentModel;

    public function __construct() {
        $this->clientModel = new FreelanceClient();
        $this->projectModel = new FreelanceProject();
        $this->invoiceModel = new FreelanceInvoice();
        $this->paymentModel = new FreelancePayment();
    }

    /**
     * Show freelance dashboard
     */
    public function index() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get summary statistics
        $clientSummary = $this->clientModel->getClientSummary($userId);
        $projectSummary = $this->projectModel->getProjectSummary($userId);
        $invoiceSummary = $this->invoiceModel->getInvoiceSummary($userId);
        $paymentSummary = $this->paymentModel->getPaymentSummary($userId);

        // Get active projects
        $activeProjects = $this->projectModel->getActiveProjects($userId, 5);

        // Get recent payments
        $recentPayments = $this->paymentModel->getRecentPayments($userId, 5);

        // Get monthly payment totals for chart
        $monthlyPayments = $this->paymentModel->getMonthlyPaymentTotals($userId);

        $this->view('freelance/dashboard', [
            'clientSummary' => $clientSummary,
            'projectSummary' => $projectSummary,
            'invoiceSummary' => $invoiceSummary,
            'paymentSummary' => $paymentSummary,
            'activeProjects' => $activeProjects,
            'recentPayments' => $recentPayments,
            'monthlyPayments' => $monthlyPayments
        ]);
    }

    /**
     * Show clients list
     */
    public function clients() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get clients based on filters
        $clients = $this->clientModel->getUserClients($userId, $filters);

        // Get client summary
        $clientSummary = $this->clientModel->getClientSummary($userId);

        $this->view('freelance/clients/index', [
            'clients' => $clients,
            'clientSummary' => $clientSummary,
            'filters' => $filters
        ]);
    }

    /**
     * Show projects list
     */
    public function projects() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get projects based on filters
        $projects = $this->projectModel->getUserProjects($userId, $filters);

        // Get project summary
        $projectSummary = $this->projectModel->getProjectSummary($userId);

        // Get clients for filter dropdown
        $clients = $this->clientModel->getUserClients($userId);

        $this->view('freelance/projects/index', [
            'projects' => $projects,
            'projectSummary' => $projectSummary,
            'clients' => $clients,
            'filters' => $filters
        ]);
    }

    /**
     * Show invoices list
     */
    public function invoices() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get invoices based on filters
        $invoices = $this->invoiceModel->getUserInvoices($userId, $filters);

        // Get invoice summary
        $invoiceSummary = $this->invoiceModel->getInvoiceSummary($userId);

        // Get clients and projects for filter dropdowns
        $clients = $this->clientModel->getUserClients($userId);
        $projects = $this->projectModel->getUserProjects($userId);

        $this->view('freelance/invoices/index', [
            'invoices' => $invoices,
            'invoiceSummary' => $invoiceSummary,
            'clients' => $clients,
            'projects' => $projects,
            'filters' => $filters
        ]);
    }

    /**
     * Show payments list
     */
    public function payments() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Get payments based on filters
        $payments = $this->paymentModel->getUserPayments($userId, $filters);

        // Get payment summary
        $paymentSummary = $this->paymentModel->getPaymentSummary($userId);

        // Get monthly payment totals for chart
        $monthlyPayments = $this->paymentModel->getMonthlyPaymentTotals($userId);

        // Get clients and projects for filter dropdowns
        $clients = $this->clientModel->getUserClients($userId);
        $projects = $this->projectModel->getUserProjects($userId);

        $this->view('freelance/payments/index', [
            'payments' => $payments,
            'paymentSummary' => $paymentSummary,
            'monthlyPayments' => $monthlyPayments,
            'clients' => $clients,
            'projects' => $projects,
            'filters' => $filters
        ]);
    }

    /**
     * Show reports page
     */
    public function reports() {
        $this->requireLogin();

        $user = Session::getUser();
        $userId = $user['id'];

        // Get filter parameters
        $filters = $this->getQueryData();

        // Default to current year if not specified
        if (empty($filters['year'])) {
            $filters['year'] = date('Y');
        }

        // Get monthly payment totals for chart
        $monthlyPayments = $this->paymentModel->getMonthlyPaymentTotals($userId);

        // Get clients and projects for filter dropdowns
        $clients = $this->clientModel->getUserClients($userId);
        $projects = $this->projectModel->getUserProjects($userId);

        $this->view('freelance/reports', [
            'monthlyPayments' => $monthlyPayments,
            'clients' => $clients,
            'projects' => $projects,
            'filters' => $filters
        ]);
    }
}
