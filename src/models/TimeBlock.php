<?php
/**
 * Time Block Model
 *
 * Handles time blocking functionality for productivity management
 */

require_once __DIR__ . '/BaseModel.php';
require_once __DIR__ . '/Task.php';

class TimeBlock extends BaseModel {
    protected $table = 'time_blocks';
    protected $taskModel;

    public function __construct() {
        parent::__construct();
        $this->taskModel = new Task();
    }

    /**
     * Get all time blocks for a user
     */
    public function getUserTimeBlocks($userId) {
        $sql = "SELECT tb.*, t.title as task_title
                FROM {$this->table} tb
                LEFT JOIN tasks t ON tb.task_id = t.id
                WHERE tb.user_id = ?
                ORDER BY tb.start_time ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get time blocks for a specific date range
     */
    public function getTimeBlocksByDateRange($userId, $startDate, $endDate) {
        $sql = "SELECT tb.*, t.title as task_title
                FROM {$this->table} tb
                LEFT JOIN tasks t ON tb.task_id = t.id
                WHERE tb.user_id = ?
                AND (
                    (tb.start_time BETWEEN ? AND ?) OR
                    (tb.end_time BETWEEN ? AND ?) OR
                    (tb.start_time <= ? AND tb.end_time >= ?)
                )
                ORDER BY tb.start_time ASC";

        return $this->db->fetchAll($sql, [
            $userId,
            $startDate, $endDate,
            $startDate, $endDate,
            $startDate, $endDate
        ]);
    }

    /**
     * Get time blocks for today
     */
    public function getTodayTimeBlocks($userId) {
        $today = date('Y-m-d');
        $startDate = $today . ' 00:00:00';
        $endDate = $today . ' 23:59:59';

        return $this->getTimeBlocksByDateRange($userId, $startDate, $endDate);
    }

    /**
     * Get time blocks for a specific task
     */
    public function getTimeBlocksByTask($userId, $taskId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ? AND task_id = ?
                ORDER BY start_time ASC";

        return $this->db->fetchAll($sql, [$userId, $taskId]);
    }

    /**
     * Create a new time block
     */
    public function create($data) {
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->db->insert($this->table, $data);
    }

    /**
     * Update a time block
     */
    public function update($id, $data) {
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->db->update($this->table, $data, ['id' => $id]);
    }

    /**
     * Delete a time block
     */
    public function delete($id) {
        return $this->db->delete($this->table, "{$this->primaryKey} = ?", [$id]);
    }

    /**
     * Find a time block by ID
     *
     * @param int $id Record ID
     * @param bool $useCache Whether to use cache
     * @return array|false Record data or false if not found
     */
    public function find($id, $useCache = null) {
        // Use class default if not specified
        $useCache = $useCache !== null ? $useCache : $this->useCache;

        // Generate cache key
        $cacheKey = "model_{$this->table}_find_{$id}_with_task";

        // Try to get from cache first
        if ($useCache && $this->cache->has($cacheKey)) {
            return $this->cache->get($cacheKey);
        }

        $sql = "SELECT tb.*, t.title as task_title
                FROM {$this->table} tb
                LEFT JOIN tasks t ON tb.task_id = t.id
                WHERE tb.id = ?
                LIMIT 1";

        $result = $this->db->fetchOne($sql, [$id]);

        // Store in cache if found
        if ($result && $useCache) {
            $this->cache->set($cacheKey, $result, $this->cacheTTL);
        }

        return $result;
    }

    /**
     * Check if a time block belongs to a user
     */
    public function belongsToUser($id, $userId) {
        $sql = "SELECT COUNT(*) as count FROM {$this->table}
                WHERE id = ? AND user_id = ?";

        $result = $this->db->fetchOne($sql, [$id, $userId]);

        return $result['count'] > 0;
    }

    /**
     * Check for time block conflicts
     * Returns conflicting time blocks if any
     */
    public function checkConflicts($userId, $startTime, $endTime, $excludeId = null) {
        $params = [$userId, $startTime, $endTime, $startTime, $endTime];
        $excludeClause = '';

        if ($excludeId) {
            $excludeClause = "AND id != ?";
            $params[] = $excludeId;
        }

        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                AND (
                    (start_time <= ? AND end_time > ?) OR
                    (start_time < ? AND end_time >= ?) OR
                    (start_time >= ? AND end_time <= ?)
                )
                $excludeClause
                ORDER BY start_time ASC";

        // Add the additional parameters for the last condition
        $params[] = $startTime;
        $params[] = $endTime;

        return $this->db->fetchAll($sql, $params);
    }
}
