<?php
/**
 * Pet Tracker Model
 *
 * Handles pet management, medications, treatments, and training logs
 */

require_once __DIR__ . '/BaseModel.php';

class PetTracker extends BaseModel {
    protected $table = 'pets';
    protected $medicationsTable = 'pet_medications';
    protected $medicationLogsTable = 'pet_medication_logs';
    protected $treatmentsTable = 'pet_treatments';
    protected $trainingLogsTable = 'pet_training_logs';

    // New tables for enhanced features
    protected $vitalsTable = 'pet_vitals';
    protected $healthAssessmentsTable = 'pet_health_assessments';
    protected $dietPlansTable = 'pet_diet_plans';
    protected $mealsTable = 'pet_meals';
    protected $foodAllergiesTable = 'pet_food_allergies';
    protected $activitiesTable = 'pet_activities';
    protected $behaviorLogsTable = 'pet_behavior_logs';
    protected $vetVisitsTable = 'pet_vet_visits';
    protected $expensesTable = 'pet_expenses';
    protected $breedingRecordsTable = 'pet_breeding_records';
    protected $offspringTable = 'pet_offspring';
    protected $documentsTable = 'pet_documents';
    protected $contactsTable = 'pet_contacts';
    protected $remindersTable = 'pet_reminders';
    protected $growthMetricsTable = 'pet_growth_metrics';
    protected $growthMilestonesTable = 'pet_growth_milestones';
    protected $growthStandardsTable = 'pet_growth_standards';

    /**
     * Get all pets for a user
     */
    public function getUserPets($userId) {
        $sql = "SELECT * FROM {$this->table}
                WHERE user_id = ?
                ORDER BY name ASC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get pet by ID
     */
    public function getPet($id) {
        return $this->find($id);
    }

    /**
     * Create a new pet
     */
    public function createPet($data) {
        return $this->create($data);
    }

    /**
     * Update a pet
     */
    public function updatePet($id, $data) {
        return $this->update($id, $data);
    }

    /**
     * Delete a pet
     */
    public function deletePet($id) {
        return $this->delete($id);
    }

    /**
     * Get all medications for a pet
     */
    public function getPetMedications($petId) {
        $sql = "SELECT * FROM {$this->medicationsTable}
                WHERE pet_id = ?
                ORDER BY name ASC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get medication by ID
     */
    public function getMedication($id) {
        $sql = "SELECT * FROM {$this->medicationsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new medication
     */
    public function createMedication($data) {
        return $this->db->insert($this->medicationsTable, $data);
    }

    /**
     * Update a medication
     */
    public function updateMedication($id, $data) {
        return $this->db->update($this->medicationsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a medication
     */
    public function deleteMedication($id) {
        return $this->db->delete($this->medicationsTable, "id = ?", [$id]);
    }

    /**
     * Get medication logs for a medication
     */
    public function getMedicationLogs($medicationId, $limit = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";

        $sql = "SELECT * FROM {$this->medicationLogsTable}
                WHERE medication_id = ?
                ORDER BY log_date DESC, log_time DESC
                {$limitClause}";

        return $this->db->fetchAll($sql, [$medicationId]);
    }

    /**
     * Create a medication log
     */
    public function createMedicationLog($data) {
        return $this->db->insert($this->medicationLogsTable, $data);
    }

    /**
     * Update a medication log
     */
    public function updateMedicationLog($id, $data) {
        return $this->db->update($this->medicationLogsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a medication log
     */
    public function deleteMedicationLog($id) {
        return $this->db->delete($this->medicationLogsTable, "id = ?", [$id]);
    }

    /**
     * Get all treatments for a pet
     */
    public function getPetTreatments($petId) {
        $sql = "SELECT * FROM {$this->treatmentsTable}
                WHERE pet_id = ?
                ORDER BY treatment_date DESC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get treatments for a pet by type
     */
    public function getPetTreatmentsByType($petId, $treatmentType) {
        $sql = "SELECT * FROM {$this->treatmentsTable}
                WHERE pet_id = ? AND treatment_type = ?
                ORDER BY treatment_date DESC";

        return $this->db->fetchAll($sql, [$petId, $treatmentType]);
    }

    /**
     * Get treatment by ID
     */
    public function getTreatment($id) {
        $sql = "SELECT * FROM {$this->treatmentsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new treatment
     */
    public function createTreatment($data) {
        return $this->db->insert($this->treatmentsTable, $data);
    }

    /**
     * Update a treatment
     */
    public function updateTreatment($id, $data) {
        return $this->db->update($this->treatmentsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a treatment
     */
    public function deleteTreatment($id) {
        return $this->db->delete($this->treatmentsTable, "id = ?", [$id]);
    }

    /**
     * Get upcoming treatments for a pet
     */
    public function getUpcomingTreatments($petId, $days = 30) {
        $sql = "SELECT * FROM {$this->treatmentsTable}
                WHERE pet_id = ?
                AND next_due_date IS NOT NULL
                AND next_due_date <= DATE_ADD(CURDATE(), INTERVAL ? DAY)
                AND next_due_date >= CURDATE()
                ORDER BY next_due_date ASC";

        return $this->db->fetchAll($sql, [$petId, $days]);
    }

    /**
     * Get upcoming treatments for a pet by type
     */
    public function getUpcomingTreatmentsByType($petId, $treatmentType, $days = 30) {
        $sql = "SELECT * FROM {$this->treatmentsTable}
                WHERE pet_id = ?
                AND treatment_type = ?
                AND next_due_date IS NOT NULL
                AND next_due_date <= DATE_ADD(CURDATE(), INTERVAL ? DAY)
                ORDER BY next_due_date ASC";

        return $this->db->fetchAll($sql, [$petId, $treatmentType, $days]);
    }

    /**
     * Get all training logs for a pet
     */
    public function getPetTrainingLogs($petId) {
        $sql = "SELECT * FROM {$this->trainingLogsTable}
                WHERE pet_id = ?
                ORDER BY training_date DESC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get training log by ID
     */
    public function getTrainingLog($id) {
        $sql = "SELECT * FROM {$this->trainingLogsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new training log
     */
    public function createTrainingLog($data) {
        return $this->db->insert($this->trainingLogsTable, $data);
    }

    /**
     * Update a training log
     */
    public function updateTrainingLog($id, $data) {
        return $this->db->update($this->trainingLogsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a training log
     */
    public function deleteTrainingLog($id) {
        return $this->db->delete($this->trainingLogsTable, "id = ?", [$id]);
    }

    /**
     * Get recent training logs for a pet
     */
    public function getRecentTrainingLogs($petId, $limit = 5) {
        $sql = "SELECT * FROM {$this->trainingLogsTable}
                WHERE pet_id = ?
                ORDER BY training_date DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$petId, $limit]);
    }

    /*
     * Pet Vitals Methods
     */

    /**
     * Get all vitals records for a pet
     */
    public function getPetVitals($petId, $limit = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";

        $sql = "SELECT * FROM {$this->vitalsTable}
                WHERE pet_id = ?
                ORDER BY record_date DESC
                {$limitClause}";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get vital record by ID
     */
    public function getVitalRecord($id) {
        $sql = "SELECT * FROM {$this->vitalsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new vital record
     */
    public function createVitalRecord($data) {
        return $this->db->insert($this->vitalsTable, $data);
    }

    /**
     * Update a vital record
     */
    public function updateVitalRecord($id, $data) {
        return $this->db->update($this->vitalsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a vital record
     */
    public function deleteVitalRecord($id) {
        return $this->db->delete($this->vitalsTable, "id = ?", [$id]);
    }

    /**
     * Get weight history for a pet
     */
    public function getPetWeightHistory($petId, $limit = null) {
        // First get weight from vitals table
        $sqlVitals = "SELECT record_date, weight, weight_unit FROM {$this->vitalsTable}
                WHERE pet_id = ? AND weight IS NOT NULL
                ORDER BY record_date ASC";

        $vitalsWeights = $this->db->fetchAll($sqlVitals, [$petId]);

        // Then get weight from growth metrics table
        $sqlGrowth = "SELECT record_date, weight, weight_unit FROM {$this->growthMetricsTable}
                WHERE pet_id = ? AND weight IS NOT NULL
                ORDER BY record_date ASC";

        $growthWeights = $this->db->fetchAll($sqlGrowth, [$petId]);

        // Combine both results
        $allWeights = array_merge($vitalsWeights, $growthWeights);

        // Sort by date
        usort($allWeights, function($a, $b) {
            return strtotime($a['record_date']) - strtotime($b['record_date']);
        });

        // Apply limit if specified
        if ($limit !== null) {
            $allWeights = array_slice($allWeights, 0, intval($limit));
        }

        return $allWeights;
    }

    /*
     * Pet Growth Metrics Methods
     */

    /**
     * Get all growth metrics for a pet
     */
    public function getPetGrowthMetrics($petId, $limit = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";

        $sql = "SELECT * FROM {$this->growthMetricsTable}
                WHERE pet_id = ?
                ORDER BY record_date DESC
                {$limitClause}";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get growth metric by ID
     */
    public function getGrowthMetric($id) {
        $sql = "SELECT * FROM {$this->growthMetricsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new growth metric record
     */
    public function createGrowthMetric($data) {
        return $this->db->insert($this->growthMetricsTable, $data);
    }

    /**
     * Update a growth metric record
     */
    public function updateGrowthMetric($id, $data) {
        return $this->db->update($this->growthMetricsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a growth metric record
     */
    public function deleteGrowthMetric($id) {
        return $this->db->delete($this->growthMetricsTable, "id = ?", [$id]);
    }

    /**
     * Get height history for a pet
     */
    public function getPetHeightHistory($petId, $limit = null) {
        $sql = "SELECT record_date, height, height_unit FROM {$this->growthMetricsTable}
                WHERE pet_id = ? AND height IS NOT NULL
                ORDER BY record_date ASC";

        if ($limit !== null) {
            $sql .= " LIMIT " . intval($limit);
        }

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get length history for a pet
     */
    public function getPetLengthHistory($petId, $limit = null) {
        $sql = "SELECT record_date, length, length_unit FROM {$this->growthMetricsTable}
                WHERE pet_id = ? AND length IS NOT NULL
                ORDER BY record_date ASC";

        if ($limit !== null) {
            $sql .= " LIMIT " . intval($limit);
        }

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get chest girth history for a pet
     */
    public function getPetChestGirthHistory($petId, $limit = null) {
        $sql = "SELECT record_date, chest_girth, chest_girth_unit FROM {$this->growthMetricsTable}
                WHERE pet_id = ? AND chest_girth IS NOT NULL
                ORDER BY record_date ASC";

        if ($limit !== null) {
            $sql .= " LIMIT " . intval($limit);
        }

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Calculate growth rate for a pet
     *
     * @param int $petId The pet ID
     * @param string $metric The metric to calculate (weight, height, length, chest_girth)
     * @param int $months Number of months to look back
     * @return array Growth rate data
     */
    public function calculateGrowthRate($petId, $metric = 'weight', $months = 6) {
        // Get pet birth date
        $pet = $this->getPet($petId);
        if (!$pet || empty($pet['birth_date'])) {
            return [
                'rate' => 0,
                'unit' => 'per month',
                'data' => []
            ];
        }

        // Get metric history based on months to look back
        $cutoffDate = date('Y-m-d', strtotime("-{$months} months"));
        $history = [];

        // Get full history first
        switch ($metric) {
            case 'weight':
                $history = $this->getPetWeightHistory($petId);
                break;
            case 'height':
                $history = $this->getPetHeightHistory($petId);
                break;
            case 'length':
                $history = $this->getPetLengthHistory($petId);
                break;
            case 'chest_girth':
                $history = $this->getPetChestGirthHistory($petId);
                break;
            default:
                return [
                    'rate' => 0,
                    'unit' => 'per month',
                    'data' => []
                ];
        }

        // Filter by cutoff date if needed
        if ($months > 0) {
            $history = array_filter($history, function($record) use ($cutoffDate) {
                return $record['record_date'] >= $cutoffDate;
            });
        }

        if (count($history) < 2) {
            return [
                'rate' => 0,
                'unit' => 'per month',
                'data' => $history
            ];
        }

        // Calculate age in months for each record
        $birthDate = new DateTime($pet['birth_date']);
        foreach ($history as &$record) {
            $recordDate = new DateTime($record['record_date']);
            $interval = $birthDate->diff($recordDate);
            $ageInMonths = ($interval->y * 12) + $interval->m;
            $record['age_months'] = $ageInMonths;
        }

        // Calculate growth rate (units per month)
        $firstRecord = reset($history);
        $lastRecord = end($history);

        $firstValue = $firstRecord[$metric];
        $lastValue = $lastRecord[$metric];
        $firstAge = $firstRecord['age_months'];
        $lastAge = $lastRecord['age_months'];

        $ageDiff = $lastAge - $firstAge;
        if ($ageDiff <= 0) {
            $ageDiff = 1; // Avoid division by zero
        }

        $growthRate = ($lastValue - $firstValue) / $ageDiff;

        return [
            'rate' => round($growthRate, 2),
            'unit' => 'per month',
            'data' => $history
        ];
    }

    /*
     * Pet Growth Milestones Methods
     */

    /**
     * Get all growth milestones for a pet
     */
    public function getPetGrowthMilestones($petId) {
        $sql = "SELECT * FROM {$this->growthMilestonesTable}
                WHERE pet_id = ?
                ORDER BY milestone_date DESC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get growth milestone by ID
     */
    public function getGrowthMilestone($id) {
        $sql = "SELECT * FROM {$this->growthMilestonesTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new growth milestone
     */
    public function createGrowthMilestone($data) {
        return $this->db->insert($this->growthMilestonesTable, $data);
    }

    /**
     * Update a growth milestone
     */
    public function updateGrowthMilestone($id, $data) {
        return $this->db->update($this->growthMilestonesTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a growth milestone
     */
    public function deleteGrowthMilestone($id) {
        return $this->db->delete($this->growthMilestonesTable, "id = ?", [$id]);
    }

    /**
     * Get growth standards for a pet
     */
    public function getGrowthStandards($species, $breed = null, $gender = null) {
        $whereClause = "species = ?";
        $params = [$species];

        if ($breed) {
            $whereClause .= " AND (breed = ? OR breed IS NULL)";
            $params[] = $breed;
        } else {
            $whereClause .= " AND breed IS NULL";
        }

        if ($gender) {
            $whereClause .= " AND (gender = ? OR gender = 'both')";
            $params[] = $gender;
        } else {
            $whereClause .= " AND gender = 'both'";
        }

        $sql = "SELECT * FROM {$this->growthStandardsTable}
                WHERE {$whereClause}
                ORDER BY age_months ASC";

        return $this->db->fetchAll($sql, $params);
    }

    /*
     * Pet Health Assessment Methods
     */

    /**
     * Get all health assessments for a pet
     */
    public function getPetHealthAssessments($petId, $limit = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";

        $sql = "SELECT * FROM {$this->healthAssessmentsTable}
                WHERE pet_id = ?
                ORDER BY assessment_date DESC
                {$limitClause}";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get health assessment by ID
     */
    public function getHealthAssessment($id) {
        $sql = "SELECT * FROM {$this->healthAssessmentsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new health assessment
     */
    public function createHealthAssessment($data) {
        return $this->db->insert($this->healthAssessmentsTable, $data);
    }

    /**
     * Update a health assessment
     */
    public function updateHealthAssessment($id, $data) {
        return $this->db->update($this->healthAssessmentsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a health assessment
     */
    public function deleteHealthAssessment($id) {
        return $this->db->delete($this->healthAssessmentsTable, "id = ?", [$id]);
    }

    /*
     * Pet Diet Plan Methods
     */

    /**
     * Get all diet plans for a pet
     */
    public function getPetDietPlans($petId) {
        $sql = "SELECT * FROM {$this->dietPlansTable}
                WHERE pet_id = ?
                ORDER BY is_active DESC, start_date DESC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get active diet plan for a pet
     */
    public function getActiveDietPlan($petId) {
        $sql = "SELECT * FROM {$this->dietPlansTable}
                WHERE pet_id = ? AND is_active = 1
                LIMIT 1";

        return $this->db->fetchOne($sql, [$petId]);
    }

    /**
     * Get diet plan by ID
     */
    public function getDietPlan($id) {
        $sql = "SELECT * FROM {$this->dietPlansTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new diet plan
     */
    public function createDietPlan($data) {
        // If this is set as active, deactivate all other diet plans for this pet
        if (isset($data['is_active']) && $data['is_active'] == 1) {
            $this->deactivateAllDietPlans($data['pet_id']);
        }

        return $this->db->insert($this->dietPlansTable, $data);
    }

    /**
     * Update a diet plan
     */
    public function updateDietPlan($id, $data) {
        // If this is set as active, deactivate all other diet plans for this pet
        if (isset($data['is_active']) && $data['is_active'] == 1) {
            $dietPlan = $this->getDietPlan($id);
            if ($dietPlan) {
                $this->deactivateAllDietPlans($dietPlan['pet_id'], $id);
            }
        }

        return $this->db->update($this->dietPlansTable, $data, "id = ?", [$id]);
    }

    /**
     * Deactivate all diet plans for a pet
     */
    private function deactivateAllDietPlans($petId, $exceptId = null) {
        $whereClause = "pet_id = ?";
        $params = [$petId];

        if ($exceptId) {
            $whereClause .= " AND id != ?";
            $params[] = $exceptId;
        }

        return $this->db->update($this->dietPlansTable, ['is_active' => 0], $whereClause, $params);
    }

    /**
     * Delete a diet plan
     */
    public function deleteDietPlan($id) {
        return $this->db->delete($this->dietPlansTable, "id = ?", [$id]);
    }

    /*
     * Pet Meal Methods
     */

    /**
     * Get all meals for a pet
     */
    public function getPetMeals($petId, $limit = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";

        $sql = "SELECT * FROM {$this->mealsTable}
                WHERE pet_id = ?
                ORDER BY meal_date DESC, meal_time DESC
                {$limitClause}";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get meals for a pet by date range
     */
    public function getPetMealsByDateRange($petId, $startDate, $endDate) {
        $sql = "SELECT * FROM {$this->mealsTable}
                WHERE pet_id = ?
                AND meal_date BETWEEN ? AND ?
                ORDER BY meal_date DESC, meal_time DESC";

        return $this->db->fetchAll($sql, [$petId, $startDate, $endDate]);
    }

    /**
     * Get meal by ID
     */
    public function getMeal($id) {
        $sql = "SELECT * FROM {$this->mealsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new meal
     */
    public function createMeal($data) {
        return $this->db->insert($this->mealsTable, $data);
    }

    /**
     * Update a meal
     */
    public function updateMeal($id, $data) {
        return $this->db->update($this->mealsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a meal
     */
    public function deleteMeal($id) {
        return $this->db->delete($this->mealsTable, "id = ?", [$id]);
    }

    /*
     * Pet Food Allergy Methods
     */

    /**
     * Get all food allergies for a pet
     */
    public function getPetFoodAllergies($petId) {
        $sql = "SELECT * FROM {$this->foodAllergiesTable}
                WHERE pet_id = ?
                ORDER BY severity DESC, food_name ASC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get food allergy by ID
     */
    public function getFoodAllergy($id) {
        $sql = "SELECT * FROM {$this->foodAllergiesTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new food allergy
     */
    public function createFoodAllergy($data) {
        return $this->db->insert($this->foodAllergiesTable, $data);
    }

    /**
     * Update a food allergy
     */
    public function updateFoodAllergy($id, $data) {
        return $this->db->update($this->foodAllergiesTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a food allergy
     */
    public function deleteFoodAllergy($id) {
        return $this->db->delete($this->foodAllergiesTable, "id = ?", [$id]);
    }

    /*
     * Pet Activity Methods
     */

    /**
     * Get all activities for a pet
     */
    public function getPetActivities($petId, $limit = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";

        $sql = "SELECT * FROM {$this->activitiesTable}
                WHERE pet_id = ?
                ORDER BY activity_date DESC, activity_time DESC
                {$limitClause}";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get activities for a pet by date range
     */
    public function getPetActivitiesByDateRange($petId, $startDate, $endDate) {
        $sql = "SELECT * FROM {$this->activitiesTable}
                WHERE pet_id = ?
                AND activity_date BETWEEN ? AND ?
                ORDER BY activity_date DESC, activity_time DESC";

        return $this->db->fetchAll($sql, [$petId, $startDate, $endDate]);
    }

    /**
     * Get activity by ID
     */
    public function getActivity($id) {
        $sql = "SELECT * FROM {$this->activitiesTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new activity
     */
    public function createActivity($data) {
        return $this->db->insert($this->activitiesTable, $data);
    }

    /**
     * Update an activity
     */
    public function updateActivity($id, $data) {
        return $this->db->update($this->activitiesTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete an activity
     */
    public function deleteActivity($id) {
        return $this->db->delete($this->activitiesTable, "id = ?", [$id]);
    }

    /*
     * Pet Behavior Log Methods
     */

    /**
     * Get all behavior logs for a pet
     */
    public function getPetBehaviorLogs($petId, $limit = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";

        $sql = "SELECT * FROM {$this->behaviorLogsTable}
                WHERE pet_id = ?
                ORDER BY log_date DESC, log_time DESC
                {$limitClause}";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get behavior log by ID
     */
    public function getBehaviorLog($id) {
        $sql = "SELECT * FROM {$this->behaviorLogsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new behavior log
     */
    public function createBehaviorLog($data) {
        return $this->db->insert($this->behaviorLogsTable, $data);
    }

    /**
     * Update a behavior log
     */
    public function updateBehaviorLog($id, $data) {
        return $this->db->update($this->behaviorLogsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a behavior log
     */
    public function deleteBehaviorLog($id) {
        return $this->db->delete($this->behaviorLogsTable, "id = ?", [$id]);
    }

    /*
     * Pet Vet Visit Methods
     */

    /**
     * Get all vet visits for a pet
     */
    public function getPetVetVisits($petId, $limit = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";

        $sql = "SELECT * FROM {$this->vetVisitsTable}
                WHERE pet_id = ?
                ORDER BY visit_date DESC, visit_time DESC
                {$limitClause}";

        return $this->db->fetchAll($sql, [$petId]);
    }



    /**
     * Get upcoming vet visits for a pet
     */
    public function getUpcomingVetVisits($petId, $days = 30) {
        $sql = "SELECT * FROM {$this->vetVisitsTable}
                WHERE pet_id = ?
                AND follow_up_date IS NOT NULL
                AND follow_up_date <= DATE_ADD(CURDATE(), INTERVAL ? DAY)
                AND follow_up_date >= CURDATE()
                ORDER BY follow_up_date ASC";

        return $this->db->fetchAll($sql, [$petId, $days]);
    }

    /**
     * Get vet visit by ID
     */
    public function getVetVisit($id) {
        $sql = "SELECT * FROM {$this->vetVisitsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new vet visit
     */
    public function createVetVisit($data) {
        return $this->db->insert($this->vetVisitsTable, $data);
    }

    /**
     * Update a vet visit
     */
    public function updateVetVisit($id, $data) {
        return $this->db->update($this->vetVisitsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a vet visit
     */
    public function deleteVetVisit($id) {
        return $this->db->delete($this->vetVisitsTable, "id = ?", [$id]);
    }

    /*
     * Pet Expense Methods
     */

    /**
     * Get all expenses for a pet
     */
    public function getPetExpenses($petId, $limit = null) {
        $limitClause = $limit ? " LIMIT {$limit}" : "";

        $sql = "SELECT * FROM {$this->expensesTable}
                WHERE pet_id = ?
                ORDER BY expense_date DESC
                {$limitClause}";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get expenses for a pet by date range
     */
    public function getPetExpensesByDateRange($petId, $startDate, $endDate) {
        $sql = "SELECT * FROM {$this->expensesTable}
                WHERE pet_id = ?
                AND expense_date BETWEEN ? AND ?
                ORDER BY expense_date DESC";

        return $this->db->fetchAll($sql, [$petId, $startDate, $endDate]);
    }

    /**
     * Get expenses for a pet by category
     */
    public function getPetExpensesByCategory($petId, $category) {
        $sql = "SELECT * FROM {$this->expensesTable}
                WHERE pet_id = ? AND category = ?
                ORDER BY expense_date DESC";

        return $this->db->fetchAll($sql, [$petId, $category]);
    }

    /**
     * Get expense by ID
     */
    public function getExpense($id) {
        $sql = "SELECT * FROM {$this->expensesTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new expense
     */
    public function createExpense($data) {
        return $this->db->insert($this->expensesTable, $data);
    }

    /**
     * Update an expense
     */
    public function updateExpense($id, $data) {
        return $this->db->update($this->expensesTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete an expense
     */
    public function deleteExpense($id) {
        return $this->db->delete($this->expensesTable, "id = ?", [$id]);
    }

    /**
     * Get total expenses for a pet
     */
    public function getTotalPetExpenses($petId) {
        $sql = "SELECT SUM(amount) as total FROM {$this->expensesTable}
                WHERE pet_id = ?";

        $result = $this->db->fetchOne($sql, [$petId]);
        return $result ? $result['total'] : 0;
    }

    /**
     * Get expenses summary by category for a pet
     */
    public function getPetExpensesByCategories($petId) {
        $sql = "SELECT category, SUM(amount) as total FROM {$this->expensesTable}
                WHERE pet_id = ?
                GROUP BY category
                ORDER BY total DESC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /*
     * Pet Breeding Record Methods
     */

    /**
     * Get all breeding records for a pet
     */
    public function getPetBreedingRecords($petId) {
        $sql = "SELECT * FROM {$this->breedingRecordsTable}
                WHERE pet_id = ?
                ORDER BY breeding_date DESC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get breeding record by ID
     */
    public function getBreedingRecord($id) {
        $sql = "SELECT * FROM {$this->breedingRecordsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new breeding record
     */
    public function createBreedingRecord($data) {
        return $this->db->insert($this->breedingRecordsTable, $data);
    }

    /**
     * Update a breeding record
     */
    public function updateBreedingRecord($id, $data) {
        return $this->db->update($this->breedingRecordsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a breeding record
     */
    public function deleteBreedingRecord($id) {
        return $this->db->delete($this->breedingRecordsTable, "id = ?", [$id]);
    }

    /**
     * Get offspring for a breeding record
     */
    public function getBreedingOffspring($breedingRecordId) {
        $sql = "SELECT * FROM {$this->offspringTable}
                WHERE breeding_record_id = ?
                ORDER BY id ASC";

        return $this->db->fetchAll($sql, [$breedingRecordId]);
    }

    /**
     * Get offspring by ID
     */
    public function getOffspring($id) {
        $sql = "SELECT * FROM {$this->offspringTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new offspring
     */
    public function createOffspring($data) {
        return $this->db->insert($this->offspringTable, $data);
    }

    /**
     * Update an offspring
     */
    public function updateOffspring($id, $data) {
        return $this->db->update($this->offspringTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete an offspring
     */
    public function deleteOffspring($id) {
        return $this->db->delete($this->offspringTable, "id = ?", [$id]);
    }

    /*
     * Pet Document Methods
     */

    /**
     * Get all documents for a pet
     */
    public function getPetDocuments($petId) {
        $sql = "SELECT * FROM {$this->documentsTable}
                WHERE pet_id = ?
                ORDER BY document_type, title ASC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get documents for a pet by type
     */
    public function getPetDocumentsByType($petId, $documentType) {
        $sql = "SELECT * FROM {$this->documentsTable}
                WHERE pet_id = ? AND document_type = ?
                ORDER BY title ASC";

        return $this->db->fetchAll($sql, [$petId, $documentType]);
    }

    /**
     * Get document by ID
     */
    public function getDocument($id) {
        $sql = "SELECT * FROM {$this->documentsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new document
     */
    public function createDocument($data) {
        return $this->db->insert($this->documentsTable, $data);
    }

    /**
     * Update a document
     */
    public function updateDocument($id, $data) {
        return $this->db->update($this->documentsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a document
     */
    public function deleteDocument($id) {
        return $this->db->delete($this->documentsTable, "id = ?", [$id]);
    }

    /*
     * Pet Contact Methods
     */

    /**
     * Get all contacts for a pet
     */
    public function getPetContacts($petId) {
        $sql = "SELECT * FROM {$this->contactsTable}
                WHERE pet_id = ?
                ORDER BY role, name ASC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get contacts for a pet by role
     */
    public function getPetContactsByRole($petId, $role) {
        $sql = "SELECT * FROM {$this->contactsTable}
                WHERE pet_id = ? AND role = ?
                ORDER BY name ASC";

        return $this->db->fetchAll($sql, [$petId, $role]);
    }

    /**
     * Get contact by ID
     */
    public function getContact($id) {
        $sql = "SELECT * FROM {$this->contactsTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new contact
     */
    public function createContact($data) {
        return $this->db->insert($this->contactsTable, $data);
    }

    /**
     * Update a contact
     */
    public function updateContact($id, $data) {
        return $this->db->update($this->contactsTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a contact
     */
    public function deleteContact($id) {
        return $this->db->delete($this->contactsTable, "id = ?", [$id]);
    }

    /*
     * Pet Reminder Methods
     */

    /**
     * Get all reminders for a pet
     */
    public function getPetReminders($petId) {
        $sql = "SELECT * FROM {$this->remindersTable}
                WHERE pet_id = ?
                ORDER BY due_date ASC";

        return $this->db->fetchAll($sql, [$petId]);
    }

    /**
     * Get upcoming reminders for a pet
     */
    public function getUpcomingReminders($petId, $days = 30) {
        $sql = "SELECT * FROM {$this->remindersTable}
                WHERE pet_id = ?
                AND is_completed = 0
                AND due_date <= DATE_ADD(CURDATE(), INTERVAL ? DAY)
                ORDER BY due_date ASC";

        return $this->db->fetchAll($sql, [$petId, $days]);
    }

    /**
     * Get reminders for a pet by type
     */
    public function getPetRemindersByType($petId, $reminderType) {
        $sql = "SELECT * FROM {$this->remindersTable}
                WHERE pet_id = ? AND reminder_type = ?
                ORDER BY due_date ASC";

        return $this->db->fetchAll($sql, [$petId, $reminderType]);
    }

    /**
     * Get reminder by ID
     */
    public function getReminder($id) {
        $sql = "SELECT * FROM {$this->remindersTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Create a new reminder
     */
    public function createReminder($data) {
        return $this->db->insert($this->remindersTable, $data);
    }

    /**
     * Update a reminder
     */
    public function updateReminder($id, $data) {
        return $this->db->update($this->remindersTable, $data, "id = ?", [$id]);
    }

    /**
     * Mark a reminder as completed
     */
    public function completeReminder($id) {
        $data = [
            'is_completed' => 1,
            'completed_date' => date('Y-m-d'),
            'updated_at' => date('Y-m-d H:i:s')
        ];

        return $this->db->update($this->remindersTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a reminder
     */
    public function deleteReminder($id) {
        return $this->db->delete($this->remindersTable, "id = ?", [$id]);
    }

    /**
     * Get completed reminders for a pet
     */
    public function getCompletedReminders($petId, $limit = null) {
        $sql = "SELECT * FROM {$this->remindersTable}
                WHERE pet_id = ?
                AND is_completed = 1
                ORDER BY completed_at DESC";

        if ($limit !== null) {
            $sql .= " LIMIT " . intval($limit);
        }

        $params = [$petId];

        return $this->db->fetchAll($sql, $params);
    }

    /**
     * Calculate comprehensive health score for a pet
     *
     * @param int $petId The pet ID
     * @return array Health score data with value, class, and components
     */
    public function calculateHealthScore($petId) {
        // Initialize score components
        $scoreComponents = [
            'health_assessment' => [
                'score' => 0,
                'weight' => 0.30,
                'max_score' => 100,
                'description' => 'Based on most recent health assessment'
            ],
            'vitals' => [
                'score' => 0,
                'weight' => 0.15,
                'max_score' => 100,
                'description' => 'Based on vital signs within normal ranges'
            ],
            'medication_adherence' => [
                'score' => 0,
                'weight' => 0.15,
                'max_score' => 100,
                'description' => 'Based on medication schedule adherence'
            ],
            'preventive_care' => [
                'score' => 0,
                'weight' => 0.20,
                'max_score' => 100,
                'description' => 'Based on vaccinations and preventive treatments'
            ],
            'activity' => [
                'score' => 0,
                'weight' => 0.10,
                'max_score' => 100,
                'description' => 'Based on regular exercise and activity'
            ],
            'nutrition' => [
                'score' => 0,
                'weight' => 0.10,
                'max_score' => 100,
                'description' => 'Based on diet quality and feeding regularity'
            ]
        ];

        // 1. Health Assessment Component
        $recentAssessment = $this->getPetHealthAssessments($petId, 1);
        if (!empty($recentAssessment)) {
            $assessment = $recentAssessment[0];
            // Convert health rating to numeric value
            switch ($assessment['overall_health']) {
                case 'excellent':
                    $scoreComponents['health_assessment']['score'] = 100;
                    break;
                case 'good':
                    $scoreComponents['health_assessment']['score'] = 80;
                    break;
                case 'fair':
                    $scoreComponents['health_assessment']['score'] = 60;
                    break;
                case 'poor':
                    $scoreComponents['health_assessment']['score'] = 40;
                    break;
                case 'critical':
                    $scoreComponents['health_assessment']['score'] = 20;
                    break;
                default:
                    $scoreComponents['health_assessment']['score'] = 50;
            }
        } else {
            // No assessment, use default score
            $scoreComponents['health_assessment']['score'] = 50;
            $scoreComponents['health_assessment']['description'] .= ' (No recent assessment)';
        }

        // 2. Vitals Component
        $recentVitals = $this->getPetVitals($petId, 1);
        if (!empty($recentVitals)) {
            $vitals = $recentVitals[0];
            $vitalsScore = 100; // Start with perfect score

            // Check if vitals are within normal ranges
            // This is simplified - in a real app, you'd check against breed-specific normal ranges
            if (isset($vitals['heart_rate']) && ($vitals['heart_rate'] < 60 || $vitals['heart_rate'] > 160)) {
                $vitalsScore -= 20;
            }

            if (isset($vitals['respiratory_rate']) && ($vitals['respiratory_rate'] < 10 || $vitals['respiratory_rate'] > 40)) {
                $vitalsScore -= 20;
            }

            if (isset($vitals['temperature']) && ($vitals['temperature'] < 37.5 || $vitals['temperature'] > 39.5)) {
                $vitalsScore -= 20;
            }

            // Check if weight is changing rapidly
            $weightHistory = $this->getPetWeightHistory($petId, 3);
            if (count($weightHistory) >= 2) {
                $latestWeight = $weightHistory[count($weightHistory) - 1]['weight'];
                $previousWeight = $weightHistory[count($weightHistory) - 2]['weight'];

                $weightChange = abs(($latestWeight - $previousWeight) / $previousWeight);
                if ($weightChange > 0.1) { // More than 10% change
                    $vitalsScore -= 20;
                }
            }

            $scoreComponents['vitals']['score'] = max(0, $vitalsScore);
        } else {
            $scoreComponents['vitals']['score'] = 50;
            $scoreComponents['vitals']['description'] .= ' (No recent vitals)';
        }

        // 3. Medication Adherence Component
        $medications = $this->getPetMedications($petId);
        if (!empty($medications)) {
            $adherenceScores = [];

            foreach ($medications as $medication) {
                $logs = $this->getMedicationLogs($medication['id']);
                $totalDoses = 0;
                $takenDoses = 0;

                if (!empty($logs)) {
                    $totalDoses = count($logs);
                    foreach ($logs as $log) {
                        if ($log['given'] == 1) {
                            $takenDoses++;
                        }
                    }
                }

                $adherenceScores[] = $totalDoses > 0 ? ($takenDoses / $totalDoses) * 100 : 100;
            }

            $scoreComponents['medication_adherence']['score'] = !empty($adherenceScores) ?
                array_sum($adherenceScores) / count($adherenceScores) : 100;
        } else {
            $scoreComponents['medication_adherence']['score'] = 100;
            $scoreComponents['medication_adherence']['description'] .= ' (No medications)';
        }

        // 4. Preventive Care Component
        $treatments = $this->getPetTreatments($petId);
        $preventiveScore = 100;

        if (!empty($treatments)) {
            $vaccinations = array_filter($treatments, function($treatment) {
                return $treatment['treatment_type'] == 'vaccination';
            });

            $dewormings = array_filter($treatments, function($treatment) {
                return $treatment['treatment_type'] == 'deworming';
            });

            // Check for overdue vaccinations
            foreach ($vaccinations as $vaccination) {
                if (isset($vaccination['next_due_date']) &&
                    strtotime($vaccination['next_due_date']) < time()) {
                    $preventiveScore -= 20;
                    break;
                }
            }

            // Check for overdue deworming
            foreach ($dewormings as $deworming) {
                if (isset($deworming['next_due_date']) &&
                    strtotime($deworming['next_due_date']) < time()) {
                    $preventiveScore -= 20;
                    break;
                }
            }

            // Check for recent vet visits
            $recentVetVisit = $this->getPetVetVisits($petId, 1);
            if (empty($recentVetVisit) ||
                strtotime($recentVetVisit[0]['visit_date']) < strtotime('-1 year')) {
                $preventiveScore -= 30;
            }

            $scoreComponents['preventive_care']['score'] = max(0, $preventiveScore);
        } else {
            $scoreComponents['preventive_care']['score'] = 50;
            $scoreComponents['preventive_care']['description'] .= ' (No preventive care records)';
        }

        // 5. Activity Component
        $recentActivities = $this->getPetActivities($petId, 10);
        if (!empty($recentActivities)) {
            // Calculate average activity frequency (days between activities)
            $activityDates = array_map(function($activity) {
                return strtotime($activity['activity_date']);
            }, $recentActivities);

            sort($activityDates);

            if (count($activityDates) >= 2) {
                $daysBetweenActivities = [];
                for ($i = 1; $i < count($activityDates); $i++) {
                    $daysBetweenActivities[] = ($activityDates[$i] - $activityDates[$i-1]) / 86400; // Convert seconds to days
                }

                $avgDaysBetween = array_sum($daysBetweenActivities) / count($daysBetweenActivities);

                // Score based on frequency (more frequent is better)
                if ($avgDaysBetween <= 1) {
                    $activityScore = 100; // Daily activity
                } else if ($avgDaysBetween <= 2) {
                    $activityScore = 90; // Every other day
                } else if ($avgDaysBetween <= 3) {
                    $activityScore = 80;
                } else if ($avgDaysBetween <= 5) {
                    $activityScore = 70;
                } else if ($avgDaysBetween <= 7) {
                    $activityScore = 60; // Weekly
                } else {
                    $activityScore = 50; // Less than weekly
                }

                $scoreComponents['activity']['score'] = $activityScore;
            } else {
                $scoreComponents['activity']['score'] = 70; // Only one activity recorded
            }
        } else {
            $scoreComponents['activity']['score'] = 50;
            $scoreComponents['activity']['description'] .= ' (No activity records)';
        }

        // 6. Nutrition Component
        $activeDietPlan = $this->getActiveDietPlan($petId);
        $recentMeals = $this->getPetMeals($petId, 10);

        if (!empty($activeDietPlan)) {
            $nutritionScore = 80; // Having a diet plan is good

            // Check if following the diet plan
            if (!empty($recentMeals)) {
                $mealsFollowingPlan = array_filter($recentMeals, function($meal) use ($activeDietPlan) {
                    return isset($meal['diet_plan_id']) && $meal['diet_plan_id'] == $activeDietPlan['id'];
                });

                $planAdherence = count($mealsFollowingPlan) / count($recentMeals);
                $nutritionScore += $planAdherence * 20; // Up to 20 more points for following the plan
            }

            $scoreComponents['nutrition']['score'] = $nutritionScore;
        } else if (!empty($recentMeals)) {
            // No diet plan, but regular feeding
            $scoreComponents['nutrition']['score'] = 70;
        } else {
            $scoreComponents['nutrition']['score'] = 50;
            $scoreComponents['nutrition']['description'] .= ' (No nutrition records)';
        }

        // Calculate weighted total score
        $totalScore = 0;
        $totalWeight = 0;

        foreach ($scoreComponents as $component) {
            $totalScore += $component['score'] * $component['weight'];
            $totalWeight += $component['weight'];
        }

        $finalScore = $totalWeight > 0 ? round($totalScore / $totalWeight) : 50;

        // Determine score class for styling
        $scoreClass = '';
        if ($finalScore >= 90) {
            $scoreClass = 'text-green-600 dark:text-green-400';
        } else if ($finalScore >= 75) {
            $scoreClass = 'text-blue-600 dark:text-blue-400';
        } else if ($finalScore >= 60) {
            $scoreClass = 'text-yellow-600 dark:text-yellow-400';
        } else if ($finalScore >= 40) {
            $scoreClass = 'text-orange-600 dark:text-orange-400';
        } else {
            $scoreClass = 'text-red-600 dark:text-red-400';
        }

        return [
            'value' => $finalScore,
            'class' => $scoreClass,
            'components' => $scoreComponents
        ];
    }
}
