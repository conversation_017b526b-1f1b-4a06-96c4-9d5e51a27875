<?php
/**
 * Trigger Identification Model
 *
 * Handles ADHD trigger identification data
 */

require_once __DIR__ . '/BaseModel.php';

class TriggerIdentification extends BaseModel {
    protected $table = 'adhd_triggers';
    protected $occurrencesTable = 'trigger_occurrences';
    protected $strategiesTable = 'trigger_coping_strategies';

    /**
     * Get all triggers for a user
     */
    public function getUserTriggers($userId) {
        $sql = "SELECT t.*,
                (SELECT COUNT(*) FROM {$this->occurrencesTable} o WHERE o.trigger_id = t.id) as occurrence_count,
                (SELECT AVG(impact_rating) FROM {$this->occurrencesTable} o WHERE o.trigger_id = t.id) as avg_impact
                FROM {$this->table} t
                WHERE t.user_id = ?
                ORDER BY avg_impact DESC, occurrence_count DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }

    /**
     * Get triggers by category
     */
    public function getTriggersByCategory($userId, $category) {
        $sql = "SELECT t.*,
                (SELECT COUNT(*) FROM {$this->occurrencesTable} o WHERE o.trigger_id = t.id) as occurrence_count,
                (SELECT AVG(impact_rating) FROM {$this->occurrencesTable} o WHERE o.trigger_id = t.id) as avg_impact
                FROM {$this->table} t
                WHERE t.user_id = ? AND t.category = ?
                ORDER BY avg_impact DESC, occurrence_count DESC";

        return $this->db->fetchAll($sql, [$userId, $category]);
    }

    /**
     * Get trigger by ID
     */
    public function getTrigger($id) {
        return $this->find($id);
    }

    /**
     * Create a new trigger
     */
    public function createTrigger($data) {
        return $this->create($data);
    }

    /**
     * Update a trigger
     */
    public function updateTrigger($id, $data) {
        return $this->update($id, $data);
    }

    /**
     * Delete a trigger
     */
    public function deleteTrigger($id) {
        // First delete all occurrences and strategies
        $this->db->delete($this->occurrencesTable, "trigger_id = ?", [$id]);
        $this->db->delete($this->strategiesTable, "trigger_id = ?", [$id]);

        // Then delete the trigger
        return $this->delete($id);
    }

    /**
     * Get trigger occurrences
     */
    public function getTriggerOccurrences($triggerId, $limit = null) {
        $sql = "SELECT * FROM {$this->occurrencesTable}
                WHERE trigger_id = ?
                ORDER BY occurrence_date DESC, occurrence_time DESC";

        if ($limit) {
            $sql .= " LIMIT ?";
            return $this->db->fetchAll($sql, [$triggerId, $limit]);
        }

        return $this->db->fetchAll($sql, [$triggerId]);
    }

    /**
     * Get a single occurrence by ID
     */
    public function getOccurrence($id) {
        $sql = "SELECT * FROM {$this->occurrencesTable} WHERE id = ?";
        return $this->db->fetchOne($sql, [$id]);
    }

    /**
     * Get recent trigger occurrences for a user
     */
    public function getRecentOccurrences($userId, $days = 30, $limit = null) {
        $sql = "SELECT o.*, t.name as trigger_name, t.category
                FROM {$this->occurrencesTable} o
                JOIN {$this->table} t ON o.trigger_id = t.id
                WHERE t.user_id = ?
                AND o.occurrence_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY o.occurrence_date DESC, o.occurrence_time DESC";

        if ($limit) {
            $sql .= " LIMIT ?";
            return $this->db->fetchAll($sql, [$userId, $days, $limit]);
        }

        return $this->db->fetchAll($sql, [$userId, $days]);
    }

    /**
     * Log a trigger occurrence
     */
    public function logOccurrence($data) {
        return $this->db->insert($this->occurrencesTable, $data);
    }

    /**
     * Create a trigger occurrence
     */
    public function createOccurrence($data) {
        return $this->db->insert($this->occurrencesTable, $data);
    }

    /**
     * Update a trigger occurrence
     */
    public function updateOccurrence($id, $data) {
        return $this->db->update($this->occurrencesTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a trigger occurrence
     */
    public function deleteOccurrence($id) {
        return $this->db->delete($this->occurrencesTable, "id = ?", [$id]);
    }

    /**
     * Get coping strategies for a trigger
     */
    public function getCopingStrategies($triggerId) {
        $sql = "SELECT * FROM {$this->strategiesTable}
                WHERE trigger_id = ?
                ORDER BY effectiveness_rating DESC";

        return $this->db->fetchAll($sql, [$triggerId]);
    }

    /**
     * Get trigger strategies
     */
    public function getTriggerStrategies($triggerId) {
        return $this->getCopingStrategies($triggerId);
    }

    /**
     * Add a coping strategy
     */
    public function addCopingStrategy($data) {
        return $this->db->insert($this->strategiesTable, $data);
    }

    /**
     * Create a coping strategy
     */
    public function createCopingStrategy($data) {
        return $this->db->insert($this->strategiesTable, $data);
    }

    /**
     * Update a coping strategy
     */
    public function updateCopingStrategy($id, $data) {
        return $this->db->update($this->strategiesTable, $data, "id = ?", [$id]);
    }

    /**
     * Delete a coping strategy
     */
    public function deleteCopingStrategy($id) {
        return $this->db->delete($this->strategiesTable, "id = ?", [$id]);
    }

    /**
     * Get high-impact triggers for a user
     */
    public function getHighImpactTriggers($userId, $threshold = 7) {
        $sql = "SELECT t.*, AVG(o.impact_rating) as avg_impact, COUNT(o.id) as occurrence_count
                FROM {$this->table} t
                JOIN {$this->occurrencesTable} o ON t.id = o.trigger_id
                WHERE t.user_id = ?
                GROUP BY t.id
                HAVING avg_impact >= ?
                ORDER BY avg_impact DESC";

        return $this->db->fetchAll($sql, [$userId, $threshold]);
    }

    /**
     * Get trigger impact trends
     */
    public function getTriggerImpactTrends($triggerId, $days = 90) {
        $sql = "SELECT
                DATE_FORMAT(occurrence_date, '%Y-%m-%d') as date,
                AVG(impact_rating) as avg_impact,
                COUNT(*) as occurrence_count
                FROM {$this->occurrencesTable}
                WHERE trigger_id = ?
                AND occurrence_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                GROUP BY DATE_FORMAT(occurrence_date, '%Y-%m-%d')
                ORDER BY date";

        return $this->db->fetchAll($sql, [$triggerId, $days]);
    }

    /**
     * Get most effective coping strategies for a user
     */
    public function getMostEffectiveStrategies($userId, $limit = 5) {
        $sql = "SELECT s.*, t.name as trigger_name, t.category
                FROM {$this->strategiesTable} s
                JOIN {$this->table} t ON s.trigger_id = t.id
                WHERE t.user_id = ?
                ORDER BY s.effectiveness_rating DESC
                LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $limit]);
    }

    /**
     * Get trigger categories with counts
     */
    public function getTriggerCategories($userId) {
        $sql = "SELECT category, COUNT(*) as count
                FROM {$this->table}
                WHERE user_id = ?
                GROUP BY category
                ORDER BY count DESC";

        return $this->db->fetchAll($sql, [$userId]);
    }
}
