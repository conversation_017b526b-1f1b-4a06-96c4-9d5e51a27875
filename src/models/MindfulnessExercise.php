<?php
/**
 * Mindfulness Exercise Model
 *
 * Handles mindfulness exercises and practice logs
 */

require_once __DIR__ . '/BaseModel.php';

class MindfulnessExercise extends BaseModel {
    protected $table = 'mindfulness_exercises';
    protected $logsTable = 'user_mindfulness_logs';

    /**
     * Get all mindfulness exercises
     */
    public function getAllExercises() {
        $sql = "SELECT * FROM {$this->table} ORDER BY category, name";
        $result = $this->db->fetchAll($sql);
        return $result ? $result : [];
    }

    /**
     * Get exercises by category
     */
    public function getExercisesByCategory($category) {
        $sql = "SELECT * FROM {$this->table} WHERE category = ? ORDER BY name";
        $result = $this->db->fetchAll($sql, [$category]);
        return $result ? $result : [];
    }

    /**
     * Get exercise by ID
     */
    public function getExercise($id) {
        return $this->find($id);
    }

    /**
     * Get exercises by difficulty
     */
    public function getExercisesByDifficulty($difficulty) {
        $sql = "SELECT * FROM {$this->table} WHERE difficulty = ? ORDER BY category, name";
        $result = $this->db->fetchAll($sql, [$difficulty]);
        return $result ? $result : [];
    }

    /**
     * Get exercises by duration (less than or equal to specified minutes)
     */
    public function getExercisesByMaxDuration($minutes) {
        $sql = "SELECT * FROM {$this->table} WHERE duration_minutes <= ? ORDER BY duration_minutes";
        $result = $this->db->fetchAll($sql, [$minutes]);
        return $result ? $result : [];
    }

    /**
     * Get recommended exercises based on user's emotional regulation score
     */
    public function getRecommendedExercises($userId, $limit = 3) {
        // Get user's latest emotional regulation score
        $sql = "SELECT emotional_regulation_score
                FROM adhd_symptom_logs
                WHERE user_id = ?
                ORDER BY log_date DESC
                LIMIT 1";

        $result = $this->db->fetchOne($sql, [$userId]);

        if (!$result) {
            // If no logs yet, return beginner exercises
            $sql = "SELECT * FROM {$this->table}
                    WHERE difficulty = 'beginner'
                    ORDER BY RAND()
                    LIMIT ?";

            return $this->db->fetchAll($sql, [$limit]);
        }

        $emotionalScore = $result['emotional_regulation_score'];

        // Recommend based on emotional regulation score
        if ($emotionalScore <= 3) {
            // Low score - recommend grounding and breathing exercises
            $sql = "SELECT * FROM {$this->table}
                    WHERE category IN ('grounding', 'breathing')
                    ORDER BY RAND()
                    LIMIT ?";
        } elseif ($emotionalScore <= 6) {
            // Medium score - recommend mixed exercises
            $sql = "SELECT * FROM {$this->table}
                    ORDER BY RAND()
                    LIMIT ?";
        } else {
            // High score - recommend more advanced exercises
            $sql = "SELECT * FROM {$this->table}
                    WHERE difficulty IN ('intermediate', 'advanced')
                    ORDER BY RAND()
                    LIMIT ?";
        }

        return $this->db->fetchAll($sql, [$limit]);
    }

    /**
     * Log a mindfulness practice session
     */
    public function logPractice($data) {
        return $this->db->insert($this->logsTable, $data);
    }

    /**
     * Get user's practice logs
     */
    public function getUserLogs($userId, $limit = null) {
        $sql = "SELECT l.*, e.name, e.category
                FROM {$this->logsTable} l
                JOIN {$this->table} e ON l.exercise_id = e.id
                WHERE l.user_id = ?
                ORDER BY l.practice_date DESC";

        if ($limit) {
            $sql .= " LIMIT ?";
            $result = $this->db->fetchAll($sql, [$userId, $limit]);
            return $result ? $result : [];
        }

        $result = $this->db->fetchAll($sql, [$userId]);
        return $result ? $result : [];
    }

    /**
     * Get user's recent practice logs
     */
    public function getRecentLogs($userId, $days = 30) {
        $sql = "SELECT l.*, e.name, e.category
                FROM {$this->logsTable} l
                JOIN {$this->table} e ON l.exercise_id = e.id
                WHERE l.user_id = ?
                AND l.practice_date >= DATE_SUB(CURDATE(), INTERVAL ? DAY)
                ORDER BY l.practice_date DESC";

        $result = $this->db->fetchAll($sql, [$userId, $days]);
        return $result ? $result : [];
    }

    /**
     * Get user's practice streak (consecutive days)
     */
    public function getPracticeStreak($userId) {
        $sql = "SELECT COUNT(*) as streak
                FROM (
                    SELECT DATE(practice_date) as practice_day
                    FROM {$this->logsTable}
                    WHERE user_id = ?
                    GROUP BY practice_day
                    ORDER BY practice_day DESC
                ) as daily_logs
                JOIN (
                    SELECT @row := 0, @date := NULL
                ) as vars
                WHERE (
                    @date IS NULL AND (@date := practice_day)
                    OR
                    @date = DATE_ADD(practice_day, INTERVAL @row:=@row+1 DAY)
                )";

        $result = $this->db->fetchOne($sql, [$userId]);
        return $result ? $result['streak'] : 0;
    }

    /**
     * Get user's total practice minutes
     */
    public function getTotalPracticeMinutes($userId) {
        $sql = "SELECT SUM(duration_minutes) as total_minutes
                FROM {$this->logsTable}
                WHERE user_id = ?";

        $result = $this->db->fetchOne($sql, [$userId]);
        return $result ? $result['total_minutes'] : 0;
    }

    /**
     * Get user's practice statistics
     */
    public function getPracticeStats($userId) {
        $stats = [
            'total_sessions' => 0,
            'total_minutes' => 0,
            'current_streak' => 0,
            'favorite_category' => null,
            'mood_improvement' => 0
        ];

        // Total sessions
        $sql = "SELECT COUNT(*) as count FROM {$this->logsTable} WHERE user_id = ?";
        $result = $this->db->fetchOne($sql, [$userId]);
        $stats['total_sessions'] = $result ? $result['count'] : 0;

        // Total minutes
        $stats['total_minutes'] = $this->getTotalPracticeMinutes($userId);

        // Current streak
        $stats['current_streak'] = $this->getPracticeStreak($userId);

        // Favorite category
        $sql = "SELECT e.category, COUNT(*) as count
                FROM {$this->logsTable} l
                JOIN {$this->table} e ON l.exercise_id = e.id
                WHERE l.user_id = ?
                GROUP BY e.category
                ORDER BY count DESC
                LIMIT 1";

        $result = $this->db->fetchOne($sql, [$userId]);
        $stats['favorite_category'] = $result ? $result['category'] : null;

        // Average mood improvement
        $sql = "SELECT AVG(
                    CASE
                        WHEN mood_before = 'very_poor' THEN 1
                        WHEN mood_before = 'poor' THEN 2
                        WHEN mood_before = 'neutral' THEN 3
                        WHEN mood_before = 'good' THEN 4
                        WHEN mood_before = 'very_good' THEN 5
                    END -
                    CASE
                        WHEN mood_after = 'very_poor' THEN 1
                        WHEN mood_after = 'poor' THEN 2
                        WHEN mood_after = 'neutral' THEN 3
                        WHEN mood_after = 'good' THEN 4
                        WHEN mood_after = 'very_good' THEN 5
                    END
                ) as avg_improvement
                FROM {$this->logsTable}
                WHERE user_id = ?";

        $result = $this->db->fetchOne($sql, [$userId]);
        $stats['mood_improvement'] = $result ? $result['avg_improvement'] : 0;

        return $stats;
    }
}
