<?php
/**
 * Business Venture Model
 *
 * This model handles operations related to online business ventures.
 */

class BusinessVenture
{
    private $db;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->db = Database::getInstance();
    }

    /**
     * Get all business ventures for a user with pagination
     *
     * @param int $userId The user ID
     * @param array $filters Optional filters
     * @param int $page Page number (1-based)
     * @param int $perPage Items per page
     * @return array The business ventures and pagination info
     */
    public function getUserVentures($userId, $filters = [], $page = 1, $perPage = 10)
    {
        // Base query for counting total records
        $countSql = "SELECT COUNT(*) as total FROM business_ventures WHERE user_id = ?";
        $countParams = [$userId];

        // Base query for fetching data
        $sql = "SELECT * FROM business_ventures WHERE user_id = ?";
        $params = [$userId];

        // Apply filters if provided
        if (!empty($filters['status'])) {
            $sql .= " AND status = ?";
            $countSql .= " AND status = ?";
            $params[] = $filters['status'];
            $countParams[] = $filters['status'];
        }

        if (!empty($filters['search'])) {
            $sql .= " AND (name LIKE ? OR description LIKE ?)";
            $countSql .= " AND (name LIKE ? OR description LIKE ?)";
            $searchTerm = '%' . $filters['search'] . '%';
            $params[] = $searchTerm;
            $params[] = $searchTerm;
            $countParams[] = $searchTerm;
            $countParams[] = $searchTerm;
        }

        // Get total count for pagination
        $totalResult = $this->db->fetchOne($countSql, $countParams);
        $total = $totalResult ? (int)$totalResult['total'] : 0;

        // Calculate pagination values
        $page = max(1, $page); // Ensure page is at least 1
        $totalPages = ceil($total / $perPage);
        $offset = ($page - 1) * $perPage;

        // Add ordering and pagination to the main query
        $sql .= " ORDER BY name ASC LIMIT ? OFFSET ?";
        $params[] = $perPage;
        $params[] = $offset;

        // Get the ventures for this page
        $ventures = $this->db->fetchAll($sql, $params);

        // Return both the ventures and pagination info
        return [
            'ventures' => $ventures,
            'pagination' => [
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page,
                'total_pages' => $totalPages,
                'has_more' => $page < $totalPages
            ]
        ];
    }

    /**
     * Get a business venture by ID
     *
     * @param int $id The venture ID
     * @param int $userId The user ID (for security)
     * @return array|false The business venture or false if not found
     */
    public function getVenture($id, $userId)
    {
        $sql = "SELECT * FROM business_ventures WHERE id = ? AND user_id = ?";
        return $this->db->fetchOne($sql, [$id, $userId]);
    }

    /**
     * Create a new business venture
     *
     * @param array $data The venture data
     * @return int|false The new venture ID or false on failure
     */
    public function create($data)
    {
        $now = date('Y-m-d H:i:s');

        $sql = "INSERT INTO business_ventures (
                    user_id, name, description, business_type, website,
                    start_date, status, notes, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        $params = [
            $data['user_id'],
            $data['name'],
            $data['description'] ?? null,
            $data['business_type'],
            $data['website'] ?? null,
            $data['start_date'] ?? null,
            $data['status'] ?? 'planning',
            $data['notes'] ?? null,
            $now,
            $now
        ];

        $stmt = $this->db->query($sql, $params);
        return $stmt ? $this->db->getConnection()->lastInsertId() : false;
    }

    /**
     * Update a business venture
     *
     * @param int $id The venture ID
     * @param array $data The updated data
     * @return bool Success or failure
     */
    public function update($id, $data)
    {
        $now = date('Y-m-d H:i:s');

        $sql = "UPDATE business_ventures SET
                    name = ?,
                    description = ?,
                    business_type = ?,
                    website = ?,
                    start_date = ?,
                    status = ?,
                    notes = ?,
                    updated_at = ?
                WHERE id = ? AND user_id = ?";

        $params = [
            $data['name'],
            $data['description'] ?? null,
            $data['business_type'],
            $data['website'] ?? null,
            $data['start_date'] ?? null,
            $data['status'] ?? 'planning',
            $data['notes'] ?? null,
            $now,
            $id,
            $data['user_id']
        ];

        $stmt = $this->db->query($sql, $params);
        return $stmt ? $stmt->rowCount() > 0 : false;
    }

    /**
     * Delete a business venture
     *
     * @param int $id The venture ID
     * @param int $userId The user ID (for security)
     * @return bool Success or failure
     */
    public function delete($id, $userId)
    {
        $sql = "DELETE FROM business_ventures WHERE id = ? AND user_id = ?";
        $stmt = $this->db->query($sql, [$id, $userId]);
        return $stmt ? $stmt->rowCount() > 0 : false;
    }

    /**
     * Get business venture summary for a user
     *
     * @param int $userId The user ID
     * @return array The summary data
     */
    public function getVentureSummary($userId)
    {
        // Initialize summary structure
        $summary = [
            'total' => 0,
            'planning' => 0,
            'startup' => 0,
            'operational' => 0,
            'growing' => 0,
            'declining' => 0,
            'inactive' => 0,
            'monthly_revenue' => 0,
            'monthly_expenses' => 0,
            'monthly_profit' => 0
        ];

        // Combine both queries into one for better performance
        $sql = "SELECT
                    (SELECT COUNT(*) FROM business_ventures WHERE user_id = ?) as total,
                    (SELECT COUNT(*) FROM business_ventures WHERE user_id = ? AND status = 'planning') as planning,
                    (SELECT COUNT(*) FROM business_ventures WHERE user_id = ? AND status = 'startup') as startup,
                    (SELECT COUNT(*) FROM business_ventures WHERE user_id = ? AND status = 'operational') as operational,
                    (SELECT COUNT(*) FROM business_ventures WHERE user_id = ? AND status = 'growing') as growing,
                    (SELECT COUNT(*) FROM business_ventures WHERE user_id = ? AND status = 'declining') as declining,
                    (SELECT COUNT(*) FROM business_ventures WHERE user_id = ? AND status = 'inactive') as inactive,
                    COALESCE((SELECT SUM(bm.revenue) FROM business_ventures bv
                        LEFT JOIN business_metrics bm ON bv.id = bm.venture_id
                        WHERE bv.user_id = ? AND bm.metric_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)), 0) as monthly_revenue,
                    COALESCE((SELECT SUM(bm.expenses) FROM business_ventures bv
                        LEFT JOIN business_metrics bm ON bv.id = bm.venture_id
                        WHERE bv.user_id = ? AND bm.metric_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)), 0) as monthly_expenses,
                    COALESCE((SELECT SUM(bm.profit) FROM business_ventures bv
                        LEFT JOIN business_metrics bm ON bv.id = bm.venture_id
                        WHERE bv.user_id = ? AND bm.metric_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)), 0) as monthly_profit";

        $params = array_fill(0, 10, $userId); // Fill array with userId 10 times for all placeholders

        $result = $this->db->fetchOne($sql, $params);

        if ($result) {
            // Update summary with actual values
            foreach ($result as $key => $value) {
                if (isset($summary[$key])) {
                    $summary[$key] = is_numeric($value) ? (float)$value : 0;
                }
            }
        }

        return $summary;
    }

    /**
     * Get active business ventures for dashboard display
     *
     * @param int $userId The user ID
     * @param int $limit Maximum number of ventures to return
     * @return array The active ventures
     */
    public function getActiveVentures($userId, $limit = 5)
    {
        $sql = "SELECT * FROM business_ventures
                WHERE user_id = ? AND status IN ('operational', 'growing')
                ORDER BY name ASC LIMIT ?";

        return $this->db->fetchAll($sql, [$userId, $limit]);
    }
}
