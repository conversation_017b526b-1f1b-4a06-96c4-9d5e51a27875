<?php
/**
 * Base Agent Class
 *
 * This is the foundation class for all AI agents in the system.
 * It provides common functionality and interfaces that all agents will share.
 */

class BaseAgent {
    protected $id;
    protected $name;
    protected $description;
    protected $capabilities;
    protected $userId;
    protected $db;
    protected $agentModel;
    protected $interactionModel;

    /**
     * Constructor
     *
     * @param int $agentId The ID of the agent in the database
     * @param int $userId The ID of the user who owns this agent
     */
    public function __construct($agentId = null, $userId = null) {
        require_once __DIR__ . '/../AIAgent.php';
        require_once __DIR__ . '/../AIAgentInteraction.php';
        require_once __DIR__ . '/../../utils/Database.php';

        $this->db = Database::getInstance();
        $this->agentModel = new AIAgent();
        $this->interactionModel = new AIAgentInteraction();
        
        if ($agentId) {
            $this->loadAgent($agentId, $userId);
        }
    }

    /**
     * Load agent data from the database
     *
     * @param int $agentId The ID of the agent in the database
     * @param int $userId The ID of the user who owns this agent
     * @return bool True if agent was loaded successfully, false otherwise
     */
    public function loadAgent($agentId, $userId = null) {
        $agent = $this->agentModel->getAgent($agentId, $userId);
        
        if (!$agent) {
            return false;
        }
        
        $this->id = $agent['id'];
        $this->name = $agent['name'];
        $this->description = $agent['description'];
        $this->capabilities = $agent['capabilities'];
        $this->userId = $agent['user_id'];
        
        return true;
    }

    /**
     * Create a new agent in the database
     *
     * @param array $data Agent data
     * @return int|bool The ID of the new agent or false on failure
     */
    public function createAgent($data) {
        return $this->agentModel->createAgent($data);
    }

    /**
     * Record an interaction with this agent
     *
     * @param string $content The content of the interaction
     * @param string $response The agent's response
     * @param string $interactionType The type of interaction (command, query, feedback, training, system)
     * @param bool $success Whether the interaction was successful
     * @return int|bool The ID of the new interaction or false on failure
     */
    public function recordInteraction($content, $response, $interactionType = 'command', $success = true) {
        if (!$this->id || !$this->userId) {
            return false;
        }
        
        $data = [
            'agent_id' => $this->id,
            'user_id' => $this->userId,
            'interaction_type' => $interactionType,
            'content' => $content,
            'response' => $response,
            'success' => $success,
            'created_at' => date('Y-m-d H:i:s')
        ];
        
        return $this->interactionModel->createInteraction($data);
    }

    /**
     * Process a request to the agent
     * This method should be overridden by child classes
     *
     * @param string $request The request to process
     * @return string The agent's response
     */
    public function processRequest($request) {
        // Base implementation - should be overridden
        $response = "I am {$this->name}, but I don't know how to process your request yet.";
        $this->recordInteraction($request, $response);
        return $response;
    }

    /**
     * Get agent ID
     *
     * @return int The agent ID
     */
    public function getId() {
        return $this->id;
    }

    /**
     * Get agent name
     *
     * @return string The agent name
     */
    public function getName() {
        return $this->name;
    }

    /**
     * Get agent description
     *
     * @return string The agent description
     */
    public function getDescription() {
        return $this->description;
    }

    /**
     * Get agent capabilities
     *
     * @return string The agent capabilities
     */
    public function getCapabilities() {
        return $this->capabilities;
    }

    /**
     * Get user ID
     *
     * @return int The user ID
     */
    public function getUserId() {
        return $this->userId;
    }
}
