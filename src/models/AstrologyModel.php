<?php

require_once __DIR__ . '/BaseModel.php';

class AstrologyModel extends BaseModel {

    /**
     * <PERSON><PERSON>a timings for each day of the week (Sri Lanka standard)
     */
    private $rahuKalayaTimings = [
        'Monday' => ['start' => '07:30', 'end' => '09:00'],
        'Tuesday' => ['start' => '15:00', 'end' => '16:30'],
        'Wednesday' => ['start' => '12:00', 'end' => '13:30'],
        'Thursday' => ['start' => '13:30', 'end' => '15:00'],
        'Friday' => ['start' => '10:30', 'end' => '12:00'],
        'Saturday' => ['start' => '09:00', 'end' => '10:30'],
        'Sunday' => ['start' => '16:30', 'end' => '18:00']
    ];

    /**
     * Nighttime Rahu Kalaya timings (<PERSON><PERSON>)
     */
    private $nightRahuKalayaTimings = [
        'Monday' => ['start' => '22:30', 'end' => '24:00'],
        'Tuesday' => ['start' => '21:00', 'end' => '22:30'],
        'Wednesday' => ['start' => '19:30', 'end' => '21:00'],
        'Thursday' => ['start' => '18:00', 'end' => '19:30'],
        'Friday' => ['start' => '01:30', 'end' => '03:00'],
        'Saturday' => ['start' => '00:00', 'end' => '01:30'],
        'Sunday' => ['start' => '03:00', 'end' => '04:30']
    ];

    /**
     * Get Rahu Kalaya timing for a specific day
     */
    public function getRahuKalayaForDay($day) {
        if (isset($this->rahuKalayaTimings[$day])) {
            $timing = $this->rahuKalayaTimings[$day];
            return [
                'day' => $day,
                'start_time' => $timing['start'],
                'end_time' => $timing['end'],
                'formatted_time' => $this->formatTime($timing['start']) . ' – ' . $this->formatTime($timing['end']),
                'type' => 'day'
            ];
        }
        return null;
    }

    /**
     * Get Night Rahu Kalaya timing for a specific day
     */
    public function getNightRahuKalayaForDay($day) {
        if (isset($this->nightRahuKalayaTimings[$day])) {
            $timing = $this->nightRahuKalayaTimings[$day];
            return [
                'day' => $day,
                'start_time' => $timing['start'],
                'end_time' => $timing['end'],
                'formatted_time' => $this->formatTime($timing['start']) . ' – ' . $this->formatTime($timing['end']),
                'type' => 'night'
            ];
        }
        return null;
    }

    /**
     * Get all weekly Rahu Kalaya timings (day only)
     */
    public function getAllRahuKalayaTimings() {
        $timings = [];
        foreach ($this->rahuKalayaTimings as $day => $timing) {
            $timings[] = [
                'day' => $day,
                'start_time' => $timing['start'],
                'end_time' => $timing['end'],
                'formatted_time' => $this->formatTime($timing['start']) . ' – ' . $this->formatTime($timing['end']),
                'type' => 'day'
            ];
        }
        return $timings;
    }

    /**
     * Get all weekly Night Rahu Kalaya timings
     */
    public function getAllNightRahuKalayaTimings() {
        $timings = [];
        foreach ($this->nightRahuKalayaTimings as $day => $timing) {
            $timings[] = [
                'day' => $day,
                'start_time' => $timing['start'],
                'end_time' => $timing['end'],
                'formatted_time' => $this->formatTime($timing['start']) . ' – ' . $this->formatTime($timing['end']),
                'type' => 'night'
            ];
        }
        return $timings;
    }

    /**
     * Get both day and night Rahu Kalaya timings for all days
     */
    public function getAllRahuKalayaTimingsBoth() {
        $timings = [];
        foreach ($this->rahuKalayaTimings as $day => $dayTiming) {
            $nightTiming = $this->nightRahuKalayaTimings[$day];
            $timings[] = [
                'day' => $day,
                'day_timing' => [
                    'start_time' => $dayTiming['start'],
                    'end_time' => $dayTiming['end'],
                    'formatted_time' => $this->formatTime($dayTiming['start']) . ' – ' . $this->formatTime($dayTiming['end'])
                ],
                'night_timing' => [
                    'start_time' => $nightTiming['start'],
                    'end_time' => $nightTiming['end'],
                    'formatted_time' => $this->formatTime($nightTiming['start']) . ' – ' . $this->formatTime($nightTiming['end'])
                ]
            ];
        }
        return $timings;
    }

    /**
     * Check if current time is within Rahu Kalaya period (day or night)
     */
    public function isCurrentTimeRahuKalaya() {
        $currentDay = date('l');
        $currentTime = date('H:i');

        // Check day Rahu Kalaya
        if (isset($this->rahuKalayaTimings[$currentDay])) {
            $dayTiming = $this->rahuKalayaTimings[$currentDay];
            if ($currentTime >= $dayTiming['start'] && $currentTime <= $dayTiming['end']) {
                return ['active' => true, 'type' => 'day', 'timing' => $dayTiming];
            }
        }

        // Check night Rahu Kalaya
        if (isset($this->nightRahuKalayaTimings[$currentDay])) {
            $nightTiming = $this->nightRahuKalayaTimings[$currentDay];

            // Handle midnight crossing (e.g., 22:30 - 24:00 or 00:00 - 01:30)
            if ($nightTiming['end'] === '24:00') {
                // Period goes until midnight
                if ($currentTime >= $nightTiming['start']) {
                    return ['active' => true, 'type' => 'night', 'timing' => $nightTiming];
                }
            } elseif ($nightTiming['start'] === '00:00') {
                // Period starts at midnight
                if ($currentTime <= $nightTiming['end']) {
                    return ['active' => true, 'type' => 'night', 'timing' => $nightTiming];
                }
            } else {
                // Normal night period
                if ($currentTime >= $nightTiming['start'] && $currentTime <= $nightTiming['end']) {
                    return ['active' => true, 'type' => 'night', 'timing' => $nightTiming];
                }
            }
        }

        return ['active' => false, 'type' => null, 'timing' => null];
    }

    /**
     * Check if current time is within day Rahu Kalaya period only
     */
    public function isCurrentTimeDayRahuKalaya() {
        $currentDay = date('l');
        $currentTime = date('H:i');

        if (isset($this->rahuKalayaTimings[$currentDay])) {
            $timing = $this->rahuKalayaTimings[$currentDay];
            return ($currentTime >= $timing['start'] && $currentTime <= $timing['end']);
        }

        return false;
    }

    /**
     * Check if current time is within night Rahu Kalaya period only
     */
    public function isCurrentTimeNightRahuKalaya() {
        $currentDay = date('l');
        $currentTime = date('H:i');

        if (isset($this->nightRahuKalayaTimings[$currentDay])) {
            $timing = $this->nightRahuKalayaTimings[$currentDay];

            // Handle midnight crossing
            if ($timing['end'] === '24:00') {
                return ($currentTime >= $timing['start']);
            } elseif ($timing['start'] === '00:00') {
                return ($currentTime <= $timing['end']);
            } else {
                return ($currentTime >= $timing['start'] && $currentTime <= $timing['end']);
            }
        }

        return false;
    }

    /**
     * Get user's astrology preferences
     */
    public function getUserPreferences($userId) {
        $sql = "SELECT * FROM user_astrology_preferences WHERE user_id = ?";
        return $this->db->fetchOne($sql, [$userId]);
    }

    /**
     * Save user's astrology preferences
     */
    public function saveUserPreferences($userId, $preferences) {
        try {
            // Check if preferences already exist
            $existing = $this->getUserPreferences($userId);

            if ($existing) {
                // Update existing preferences
                $sql = "UPDATE user_astrology_preferences
                        SET notifications = ?, location = ?, timezone = ?, updated_at = NOW()
                        WHERE user_id = ?";
                $stmt = $this->db->query($sql, [
                    $preferences['notifications'],
                    $preferences['location'],
                    $preferences['timezone'],
                    $userId
                ]);
                return $stmt !== false;
            } else {
                // Insert new preferences
                $data = [
                    'user_id' => $userId,
                    'notifications' => $preferences['notifications'],
                    'location' => $preferences['location'],
                    'timezone' => $preferences['timezone']
                ];
                return $this->db->insert('user_astrology_preferences', $data) !== false;
            }
        } catch (Exception $e) {
            error_log("Error saving astrology preferences: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Format time from 24-hour to 12-hour format
     */
    private function formatTime($time) {
        return date('g:i A', strtotime($time));
    }

    /**
     * Get next Rahu Kalaya timing
     */
    public function getNextRahuKalaya() {
        $currentDay = date('l');
        $currentTime = date('H:i');

        // Check if today's Rahu Kalaya is still upcoming
        if (isset($this->rahuKalayaTimings[$currentDay])) {
            $todayTiming = $this->rahuKalayaTimings[$currentDay];
            if ($currentTime < $todayTiming['start']) {
                return $this->getRahuKalayaForDay($currentDay);
            }
        }

        // Find next day's Rahu Kalaya
        $days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
        $currentDayIndex = array_search($currentDay, $days);
        $nextDayIndex = ($currentDayIndex + 1) % 7;
        $nextDay = $days[$nextDayIndex];

        return $this->getRahuKalayaForDay($nextDay);
    }

    /**
     * Create the astrology preferences table if it doesn't exist
     */
    public function createAstrologyTables() {
        try {
            $sql = "CREATE TABLE IF NOT EXISTS user_astrology_preferences (
                        id INT AUTO_INCREMENT PRIMARY KEY,
                        user_id INT NOT NULL,
                        notifications TINYINT(1) DEFAULT 0,
                        location VARCHAR(100) DEFAULT 'Sri Lanka',
                        timezone VARCHAR(50) DEFAULT 'Asia/Colombo',
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
                        UNIQUE KEY unique_user_preferences (user_id)
                    )";

            $stmt = $this->db->query($sql);
            return $stmt !== false;
        } catch (Exception $e) {
            error_log("Error creating astrology tables: " . $e->getMessage());
            return false;
        }
    }
}
