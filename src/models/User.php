<?php
/**
 * User Model
 *
 * Handles user-related database operations.
 */

require_once __DIR__ . '/BaseModel.php';

class User extends BaseModel {
    protected $table = 'users';

    /**
     * Create a new user with hashed password
     */
    public function register($data) {
        // Hash the password
        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);

        // Set default values
        $data['created_at'] = date('Y-m-d H:i:s');
        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->create($data);
    }

    /**
     * Authenticate a user
     */
    public function authenticate($email, $password) {
        $user = $this->findOneBy('email', $email);

        if ($user && password_verify($password, $user['password'])) {
            // Update last login time
            $this->update($user['id'], [
                'last_login' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            return $user;
        }

        return false;
    }

    /**
     * Update user profile
     */
    public function updateProfile($id, $data) {
        // Don't allow direct password update through this method
        if (isset($data['password'])) {
            unset($data['password']);
        }

        $data['updated_at'] = date('Y-m-d H:i:s');

        return $this->update($id, $data);
    }

    /**
     * Change user password
     */
    public function changePassword($id, $newPassword) {
        return $this->update($id, [
            'password' => password_hash($newPassword, PASSWORD_DEFAULT),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
    }

    /**
     * Set current focus task
     */
    public function setCurrentFocusTask($userId, $taskId) {
        // Log the parameters for debugging
        error_log("Setting focus task - User ID: $userId, Task ID: $taskId");

        // Verify the user exists
        $user = $this->find($userId);
        if (!$user) {
            error_log("User not found with ID: $userId");
            return false;
        }

        // Perform the update with detailed error logging
        try {
            $result = $this->update($userId, [
                'current_focus_task_id' => $taskId,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            error_log("Update result: " . ($result ? "Success" : "Failed"));
            return $result;
        } catch (Exception $e) {
            error_log("Exception in setCurrentFocusTask: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Clear current focus task
     */
    public function clearCurrentFocusTask($userId) {
        // Log the parameters for debugging
        error_log("Clearing focus task - User ID: $userId");

        // Verify the user exists
        $user = $this->find($userId);
        if (!$user) {
            error_log("User not found with ID: $userId");
            return false;
        }

        // Check if the column exists
        $db = $this->db;
        $checkColumnSql = "SELECT COUNT(*) as column_exists
                          FROM information_schema.COLUMNS
                          WHERE TABLE_SCHEMA = DATABASE()
                          AND TABLE_NAME = 'users'
                          AND COLUMN_NAME = 'current_focus_task_id'";
        $columnExists = $db->fetchOne($checkColumnSql);

        if (!$columnExists || $columnExists['column_exists'] == 0) {
            error_log("Column 'current_focus_task_id' doesn't exist in clearCurrentFocusTask");
            return false;
        }

        // Perform the update with detailed error logging
        try {
            $result = $this->update($userId, [
                'current_focus_task_id' => null,
                'updated_at' => date('Y-m-d H:i:s')
            ]);

            error_log("Clear focus task result: " . ($result ? "Success" : "Failed"));
            return $result;
        } catch (Exception $e) {
            error_log("Exception in clearCurrentFocusTask: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Find user by email
     */
    public function findByEmail($email) {
        return $this->findOneBy('email', $email);
    }
}
