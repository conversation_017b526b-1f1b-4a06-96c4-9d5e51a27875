<?php
/**
 * Manual Table Creation Script
 * 
 * Creates AI Assistant tables one by one with better error handling
 */

// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
if (!defined('BASE_PATH')) {
    define('BASE_PATH', __DIR__);
}

// Load database configuration
require_once BASE_PATH . '/src/utils/Database.php';

echo "Creating AI Assistant Tables Manually...\n\n";

try {
    $db = Database::getInstance();
    
    // Table 1: ai_prompt_categories
    echo "1. Creating ai_prompt_categories table...\n";
    $sql1 = "CREATE TABLE IF NOT EXISTS ai_prompt_categories (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(100) NOT NULL,
        description TEXT,
        color VARCHAR(20) DEFAULT '#6366F1',
        icon VARCHAR(50) DEFAULT 'fa-brain',
        display_order INT DEFAULT 0,
        is_system BOOLEAN DEFAULT FALSE,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $result = $db->query($sql1);
    if ($result !== false) {
        echo "  ✓ ai_prompt_categories created successfully\n";
    } else {
        echo "  ✗ Failed to create ai_prompt_categories\n";
    }
    
    // Table 2: ai_prompts
    echo "2. Creating ai_prompts table...\n";
    $sql2 = "CREATE TABLE IF NOT EXISTS ai_prompts (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        category_id INT,
        title VARCHAR(255) NOT NULL,
        description TEXT,
        prompt_text TEXT NOT NULL,
        variables JSON,
        tags VARCHAR(500),
        is_template BOOLEAN DEFAULT FALSE,
        is_favorite BOOLEAN DEFAULT FALSE,
        is_public BOOLEAN DEFAULT FALSE,
        usage_count INT DEFAULT 0,
        effectiveness_rating DECIMAL(3,1) DEFAULT 0.0,
        version INT DEFAULT 1,
        parent_prompt_id INT,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (category_id) REFERENCES ai_prompt_categories(id) ON DELETE SET NULL,
        FOREIGN KEY (parent_prompt_id) REFERENCES ai_prompts(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $result = $db->query($sql2);
    if ($result !== false) {
        echo "  ✓ ai_prompts created successfully\n";
    } else {
        echo "  ✗ Failed to create ai_prompts\n";
    }
    
    // Table 3: ai_prompt_history
    echo "3. Creating ai_prompt_history table...\n";
    $sql3 = "CREATE TABLE IF NOT EXISTS ai_prompt_history (
        id INT AUTO_INCREMENT PRIMARY KEY,
        prompt_id INT NOT NULL,
        user_id INT NOT NULL,
        executed_prompt TEXT NOT NULL,
        ai_provider ENUM('openai', 'anthropic', 'google', 'local', 'other') NOT NULL,
        model_name VARCHAR(100),
        response_text LONGTEXT,
        response_metadata JSON,
        execution_time_ms INT,
        success BOOLEAN DEFAULT TRUE,
        error_message TEXT,
        user_rating INT,
        user_feedback TEXT,
        created_at DATETIME NOT NULL,
        FOREIGN KEY (prompt_id) REFERENCES ai_prompts(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $result = $db->query($sql3);
    if ($result !== false) {
        echo "  ✓ ai_prompt_history created successfully\n";
    } else {
        echo "  ✗ Failed to create ai_prompt_history\n";
    }
    
    // Table 4: ai_prompt_workflows
    echo "4. Creating ai_prompt_workflows table...\n";
    $sql4 = "CREATE TABLE IF NOT EXISTS ai_prompt_workflows (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        name VARCHAR(255) NOT NULL,
        description TEXT,
        workflow_steps JSON NOT NULL,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $result = $db->query($sql4);
    if ($result !== false) {
        echo "  ✓ ai_prompt_workflows created successfully\n";
    } else {
        echo "  ✗ Failed to create ai_prompt_workflows\n";
    }
    
    // Table 5: quick_captures
    echo "5. Creating quick_captures table...\n";
    $sql5 = "CREATE TABLE IF NOT EXISTS quick_captures (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        type ENUM('screenshot', 'note', 'voice', 'mixed') NOT NULL,
        title VARCHAR(255),
        content TEXT,
        file_path VARCHAR(500),
        file_type VARCHAR(50),
        file_size INT,
        thumbnail_path VARCHAR(500),
        ocr_text LONGTEXT,
        tags VARCHAR(500),
        category VARCHAR(100),
        is_pinned BOOLEAN DEFAULT FALSE,
        linked_prompt_id INT,
        linked_task_id INT,
        linked_project_id INT,
        metadata JSON,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        FOREIGN KEY (linked_prompt_id) REFERENCES ai_prompts(id) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $result = $db->query($sql5);
    if ($result !== false) {
        echo "  ✓ quick_captures created successfully\n";
    } else {
        echo "  ✗ Failed to create quick_captures\n";
    }
    
    // Table 6: capture_annotations
    echo "6. Creating capture_annotations table...\n";
    $sql6 = "CREATE TABLE IF NOT EXISTS capture_annotations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        capture_id INT NOT NULL,
        user_id INT NOT NULL,
        annotation_type ENUM('text', 'arrow', 'rectangle', 'circle', 'highlight', 'blur') NOT NULL,
        position_data JSON NOT NULL,
        content TEXT,
        style_data JSON,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (capture_id) REFERENCES quick_captures(id) ON DELETE CASCADE,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $result = $db->query($sql6);
    if ($result !== false) {
        echo "  ✓ capture_annotations created successfully\n";
    } else {
        echo "  ✗ Failed to create capture_annotations\n";
    }
    
    // Table 7: ai_api_configurations
    echo "7. Creating ai_api_configurations table...\n";
    $sql7 = "CREATE TABLE IF NOT EXISTS ai_api_configurations (
        id INT AUTO_INCREMENT PRIMARY KEY,
        user_id INT NOT NULL,
        provider ENUM('openai', 'anthropic', 'google', 'local', 'other') NOT NULL,
        api_key_encrypted TEXT,
        model_preferences JSON,
        rate_limits JSON,
        cost_tracking JSON,
        is_active BOOLEAN DEFAULT TRUE,
        created_at DATETIME NOT NULL,
        updated_at DATETIME NOT NULL,
        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
        UNIQUE KEY user_provider_unique (user_id, provider)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
    
    $result = $db->query($sql7);
    if ($result !== false) {
        echo "  ✓ ai_api_configurations created successfully\n";
    } else {
        echo "  ✗ Failed to create ai_api_configurations\n";
    }
    
    // Insert default categories
    echo "8. Inserting default categories...\n";
    
    // Get the first user ID
    $users = $db->fetchAll("SELECT id FROM users LIMIT 1");
    if (!empty($users)) {
        $userId = $users[0]['id'];
        
        $categories = [
            ['Writing & Content', 'Prompts for writing assistance, content creation, and editing', '#10B981', 'fa-pen-fancy', 1],
            ['Analysis & Research', 'Prompts for data analysis, research, and critical thinking', '#3B82F6', 'fa-chart-line', 2],
            ['Brainstorming & Ideas', 'Creative prompts for ideation and problem-solving', '#F59E0B', 'fa-lightbulb', 3],
            ['Code & Development', 'Programming and technical development prompts', '#8B5CF6', 'fa-code', 4],
            ['Learning & Education', 'Prompts for learning new concepts and skills', '#EF4444', 'fa-graduation-cap', 5],
            ['Business & Strategy', 'Business planning, strategy, and decision-making prompts', '#059669', 'fa-briefcase', 6],
            ['Personal & ADHD', 'Personal productivity and ADHD management prompts', '#EC4899', 'fa-brain', 7]
        ];
        
        foreach ($categories as $cat) {
            $existing = $db->fetchOne(
                "SELECT id FROM ai_prompt_categories WHERE name = ? AND user_id = ?",
                [$cat[0], $userId]
            );
            
            if (!$existing) {
                $result = $db->query(
                    "INSERT INTO ai_prompt_categories (user_id, name, description, color, icon, display_order, is_system, created_at, updated_at) VALUES (?, ?, ?, ?, ?, ?, TRUE, NOW(), NOW())",
                    [$userId, $cat[0], $cat[1], $cat[2], $cat[3], $cat[4]]
                );
                
                if ($result) {
                    echo "  ✓ Created category: {$cat[0]}\n";
                } else {
                    echo "  ✗ Failed to create category: {$cat[0]}\n";
                }
            } else {
                echo "  ✓ Category already exists: {$cat[0]}\n";
            }
        }
    } else {
        echo "  ⚠ No users found. Please create a user account first.\n";
    }
    
    echo "\n✅ Manual table creation completed!\n";
    echo "Now try accessing the AI Assistant features.\n\n";
    
} catch (Exception $e) {
    echo "\n❌ Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
?>
