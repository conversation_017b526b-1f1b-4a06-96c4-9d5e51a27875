# WindSurf VibeCoding Quick Start Guide

This quick start guide will help you get up and running with the WindSurf VibeCoding directory structure in minutes.

## Prerequisites

- Basic knowledge of HTML, CSS, and JavaScript
- A code editor (VS Code, Sublime Text, etc.)
- A local development server (Live Server, http-server, etc.)

## Step 1: Clone or Copy the Directory Structure

Start by copying the directory structure to your project:

```
website/
├── assets/
│   ├── images/
│   ├── fonts/
│   └── icons/
├── components/
│   ├── common/
│   ├── layout/
│   └── specific/
├── styles/
│   ├── base/
│   ├── components/
│   └── utilities/
├── scripts/
│   ├── modules/
│   ├── utils/
│   └── vendors/
├── pages/
├── data/
└── docs/
```

## Step 2: Create Your First Page

1. Create an `index.html` file in the `pages` directory:

```html
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>My WindSurf Project</title>
  <link rel="stylesheet" href="../styles/base/reset.css">
  <link rel="stylesheet" href="../styles/components/buttons.css">
  <link rel="stylesheet" href="../styles/components/layout.css">
</head>
<body>
  <div id="app"></div>
  
  <script type="module" src="../scripts/main.js"></script>
</body>
</html>
```

2. Create a `main.js` file in the `scripts` directory:

```javascript
import Header from '../components/layout/Header.js';
import Footer from '../components/layout/Footer.js';
import Button from '../components/common/Button.js';

// Get the app container
const app = document.getElementById('app');

// Create and render the header
new Header({
  title: 'My WindSurf Project',
  navItems: [
    { text: 'Home', url: '#', active: true },
    { text: 'About', url: '#about' },
    { text: 'Contact', url: '#contact' }
  ]
}).render(app);

// Create main content
const main = document.createElement('main');
main.classList.add('container');

const heading = document.createElement('h1');
heading.textContent = 'Welcome to My WindSurf Project';
main.appendChild(heading);

const paragraph = document.createElement('p');
paragraph.textContent = 'This is a simple example of using the WindSurf VibeCoding directory structure.';
main.appendChild(paragraph);

// Add a button
new Button('Click Me', 'primary', 'large')
  .onClick(() => {
    alert('Button clicked!');
  })
  .render(main);

app.appendChild(main);

// Create and render the footer
new Footer({
  copyright: `© ${new Date().getFullYear()} My WindSurf Project`,
  links: [
    { text: 'Privacy Policy', url: '#privacy' },
    { text: 'Terms of Service', url: '#terms' }
  ]
}).render(app);
```

## Step 3: Start Your Development Server

If you're using VS Code with Live Server:
1. Right-click on `index.html`
2. Select "Open with Live Server"

If you're using http-server:
1. Navigate to your project directory in the terminal
2. Run `npx http-server`

## Step 4: Build Your Project

Now you can start building your project by:

1. Creating reusable components in the `components` directory
2. Adding styles in the `styles` directory
3. Creating utility functions in the `scripts/utils` directory
4. Adding pages to the `pages` directory

## Best Practices

- Keep your components small and focused
- Use consistent naming conventions
- Document your code
- Follow the directory structure guidelines

## Next Steps

For more detailed information, check out:
- [Implementation Guide](./implementation-guide.md)
- [Component Documentation](./components.md)

Happy coding with WindSurf VibeCoding!
