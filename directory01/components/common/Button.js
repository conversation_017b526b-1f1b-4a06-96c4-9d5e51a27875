/**
 * <PERSON>ton Component
 * A reusable button component with various styles and states
 */

class Button {
  constructor(text, type = 'primary', size = 'medium', disabled = false) {
    this.text = text;
    this.type = type;
    this.size = size;
    this.disabled = disabled;
    this.element = this.createButton();
  }

  createButton() {
    const button = document.createElement('button');
    button.textContent = this.text;
    button.classList.add('btn', `btn-${this.type}`, `btn-${this.size}`);
    
    if (this.disabled) {
      button.disabled = true;
      button.classList.add('btn-disabled');
    }
    
    return button;
  }

  onClick(callback) {
    if (!this.disabled) {
      this.element.addEventListener('click', callback);
    }
    return this;
  }

  render(container) {
    container.appendChild(this.element);
    return this;
  }
}

export default Button;
