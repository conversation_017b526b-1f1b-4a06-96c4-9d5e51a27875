<?php
/**
 * Mockup Routes
 *
 * This file contains routes for the mockup pages.
 */

// Include the necessary files
require_once '../src/utils/Database.php';
require_once '../src/models/MockupTask.php';

// Start the session
session_start();

// Set up a simple user session if not already set
if (!isset($_SESSION['user'])) {
    $_SESSION['user'] = [
        'id' => 1,
        'name' => 'Demo User',
        'email' => '<EMAIL>'
    ];
}

// Set dark mode preference
$_SESSION['dark_mode'] = $_SESSION['dark_mode'] ?? false;

// Get the requested path
$path = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '/';

// Route the request
switch ($path) {
    case '/dashboard':
        // Get mockup data
        $userId = $_SESSION['user']['id'] ?? 1;
        $currentFocusTask = \App\Models\MockupTask::getCurrentFocusTask($userId);
        $adhdTasks = \App\Models\MockupTask::getADHDTasks();
        $productivityTasks = \App\Models\MockupTask::getProductivityTasks();

        // Include the mockup view
        include '../src/views/dashboard/mockup.php';
        break;

    case '/html':
        // Redirect to the HTML mockup
        header('Location: /momentum/public/dashboard-mockup.html');
        exit;

    case '/toggle-theme':
        // Toggle dark mode
        $_SESSION['dark_mode'] = !$_SESSION['dark_mode'];

        // Redirect back to the dashboard
        header('Location: /momentum/public/mockup-routes.php/dashboard');
        exit;

    default:
        // Redirect to the dashboard mockup by default
        header('Location: /momentum/public/mockup-routes.php/dashboard');
        exit;
}
