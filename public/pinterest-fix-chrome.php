<?php
/**
 * Pinterest Chrome Profile Fix
 *
 * This script helps fix Chrome profile issues for Pinterest integration.
 */

// Include necessary files
require_once __DIR__ . '/../src/utils/View.php';
require_once __DIR__ . '/../src/utils/AssetManager.php';
require_once __DIR__ . '/../src/utils/Environment.php';

// Start output buffering
ob_start();

// Load environment variables
Environment::load();

// Get Chrome profile path
$chromeProfile = Environment::get('CHROME_PROFILE_PATH', '');

// Check if Chrome profile is set
if (empty($chromeProfile)) {
    $chromeProfile = 'C:/Users/<USER>/AppData/Local/Google/Chrome/User Data/Profile 39';
}

// We can't update the environment variable directly, but we'll use this value in our code

// Check if Chrome profile exists
$profileExists = file_exists($chromeProfile);

// Check if Cookies file exists
$cookiesExist = file_exists($chromeProfile . '/Cookies');

// Check if Chrome is running
$chromeRunning = false;
exec('tasklist /FI "IMAGENAME eq chrome.exe" 2>NUL', $output);
foreach ($output as $line) {
    if (strpos($line, 'chrome.exe') !== false) {
        $chromeRunning = true;
        break;
    }
}

// Generate Chrome command
$chromeCommand = '"C:\\Program Files\\Google\\Chrome\\Application\\chrome.exe" --profile-directory="' . basename($chromeProfile) . '"';

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pinterest Chrome Profile Fix</title>

    <!-- Tailwind CSS -->
    <link rel="stylesheet" href="/momentum/css/tailwind.css">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <style>
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .section {
            margin-bottom: 2rem;
            padding: 1.5rem;
            border-radius: 0.5rem;
            background-color: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }
        .step {
            margin-bottom: 1rem;
            padding: 1rem;
            border-radius: 0.5rem;
        }
        .step-success {
            background-color: #d1fae5;
            border-left: 4px solid #10b981;
        }
        .step-error {
            background-color: #fee2e2;
            border-left: 4px solid #ef4444;
        }
        .step-warning {
            background-color: #fef3c7;
            border-left: 4px solid #f59e0b;
        }
        .step-info {
            background-color: #e0f2fe;
            border-left: 4px solid #0ea5e9;
        }
        .code {
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 0.5rem;
            border-radius: 0.25rem;
            margin-top: 0.5rem;
            overflow-x: auto;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container">
        <h1 class="text-3xl font-bold mb-6">Pinterest Chrome Profile Fix</h1>

        <div class="section">
            <h2 class="text-xl font-semibold mb-4">Chrome Profile Status</h2>

            <div class="step <?= $profileExists ? 'step-success' : 'step-error' ?>">
                <strong>Chrome Profile Directory:</strong> <?= htmlspecialchars($chromeProfile) ?>
                <?php if ($profileExists): ?>
                    <p class="text-green-600"><i class="fas fa-check-circle mr-1"></i> Profile directory exists</p>
                <?php else: ?>
                    <p class="text-red-600"><i class="fas fa-times-circle mr-1"></i> Profile directory does not exist</p>
                <?php endif; ?>
            </div>

            <div class="step <?= $cookiesExist ? 'step-success' : 'step-error' ?>">
                <strong>Cookies File:</strong> <?= htmlspecialchars($chromeProfile . '/Cookies') ?>
                <?php if ($cookiesExist): ?>
                    <p class="text-green-600"><i class="fas fa-check-circle mr-1"></i> Cookies file exists</p>
                <?php else: ?>
                    <p class="text-red-600"><i class="fas fa-times-circle mr-1"></i> Cookies file does not exist</p>
                <?php endif; ?>
            </div>

            <div class="step <?= $chromeRunning ? 'step-warning' : 'step-success' ?>">
                <strong>Chrome Status:</strong>
                <?php if ($chromeRunning): ?>
                    <p class="text-yellow-600"><i class="fas fa-exclamation-triangle mr-1"></i> Chrome is currently running</p>
                    <p class="mt-2">Please close all Chrome windows before proceeding.</p>
                <?php else: ?>
                    <p class="text-green-600"><i class="fas fa-check-circle mr-1"></i> Chrome is not running</p>
                <?php endif; ?>
            </div>
        </div>

        <div class="section">
            <h2 class="text-xl font-semibold mb-4">Fix Instructions</h2>

            <ol class="list-decimal list-inside space-y-4 mb-6">
                <li>
                    <strong>Close all Chrome windows</strong>
                    <p class="text-gray-600 ml-6">Make sure all Chrome windows and processes are completely closed.</p>
                </li>
                <li>
                    <strong>Open Chrome with your specific profile</strong>
                    <div class="code"><?= htmlspecialchars($chromeCommand) ?></div>
                    <p class="text-gray-600 ml-6">Copy and paste this command into a Command Prompt or Run dialog (Win+R).</p>
                </li>
                <li>
                    <strong>Log in to Pinterest</strong>
                    <p class="text-gray-600 ml-6">Navigate to <a href="https://www.pinterest.com/login/" class="text-blue-600 hover:underline" target="_blank">https://www.pinterest.com/login/</a> and log in with your credentials:</p>
                    <ul class="list-disc list-inside ml-6">
                        <li>Email: <?= htmlspecialchars(Environment::get('PINTEREST_EMAIL', '')) ?></li>
                        <li>Username: <?= htmlspecialchars(Environment::get('PINTEREST_USERNAME', '')) ?></li>
                        <li>Password: ********</li>
                    </ul>
                </li>
                <li>
                    <strong>Browse around Pinterest</strong>
                    <p class="text-gray-600 ml-6">Browse a few pages to ensure cookies are properly saved.</p>
                </li>
                <li>
                    <strong>Close Chrome completely</strong>
                    <p class="text-gray-600 ml-6">Make sure to close all Chrome windows.</p>
                </li>
            </ol>

            <div class="bg-blue-100 p-4 rounded-md">
                <p class="text-blue-800"><i class="fas fa-info-circle mr-2"></i> After completing these steps, try the Pinterest API Test again.</p>
            </div>
        </div>

        <div class="section">
            <h2 class="text-xl font-semibold mb-4">Alternative: Disable API and Use Fallback Data</h2>

            <p class="mb-4">If you're unable to fix the Chrome profile issues, you can disable the Pinterest API and use fallback data instead.</p>

            <div class="code">
                # Set to 'true' to disable the Pinterest API and use fallback data
                DISABLE_PINTEREST_API=true
            </div>

            <p class="mt-2">Add this line to your .env file to disable the Pinterest API.</p>
        </div>

        <div class="mt-8 flex space-x-4">
            <a href="/momentum/pinterest-api-test.php" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 transition-colors duration-200">
                <i class="fas fa-vial mr-2"></i> API Test
            </a>
            <a href="/momentum/fix-pinterest-api.php" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors duration-200">
                <i class="fas fa-wrench mr-2"></i> Fix API
            </a>
            <a href="/momentum/clone/pinterest" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <i class="fas fa-arrow-left mr-2"></i> Back to Pinterest Dashboard
            </a>
        </div>
    </div>
</body>
</html>
<?php
// End output buffering and display the page
echo ob_get_clean();
?>
