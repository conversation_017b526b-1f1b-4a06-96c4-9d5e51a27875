/**
 * Simple Dropdown Fix
 * 
 * This script provides a very simple, direct implementation for dropdown menus
 * without any dependencies or complex interactions.
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Simple Dropdown Fix loaded');
    
    // Get all dropdown buttons
    const dropdownButtons = [
        document.getElementById('adhd-dropdown-button'),
        document.getElementById('productivity-dropdown-button'),
        document.getElementById('tools-dropdown-button'),
        document.getElementById('user-menu-button')
    ];
    
    // Get all dropdown menus
    const dropdownMenus = [
        document.getElementById('adhd-dropdown-menu'),
        document.getElementById('productivity-dropdown-menu'),
        document.getElementById('tools-dropdown-menu'),
        document.getElementById('user-dropdown-menu')
    ];
    
    // Function to hide all menus
    function hideAllMenus() {
        dropdownMenus.forEach(menu => {
            if (menu) {
                menu.style.display = 'none';
            }
        });
        
        // Reset aria-expanded attributes
        dropdownButtons.forEach(button => {
            if (button) {
                button.setAttribute('aria-expanded', 'false');
            }
        });
    }
    
    // Add click handlers to each button
    dropdownButtons.forEach((button, index) => {
        if (button && dropdownMenus[index]) {
            // Remove any existing onclick attribute to prevent conflicts
            button.removeAttribute('onclick');
            
            // Add clean event listener
            button.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                console.log(button.id + ' clicked');
                
                const menu = dropdownMenus[index];
                
                // Toggle menu visibility
                if (menu.style.display === 'block') {
                    menu.style.display = 'none';
                    button.setAttribute('aria-expanded', 'false');
                } else {
                    // Hide all other menus first
                    hideAllMenus();
                    
                    // Show this menu
                    menu.style.display = 'block';
                    button.setAttribute('aria-expanded', 'true');
                    
                    // Position the menu
                    const buttonRect = button.getBoundingClientRect();
                    menu.style.position = 'fixed';
                    menu.style.top = (buttonRect.bottom + 5) + 'px';
                    
                    // Handle right alignment for user menu
                    if (button.id === 'user-menu-button') {
                        menu.style.left = 'auto';
                        menu.style.right = (window.innerWidth - buttonRect.right) + 'px';
                    } else {
                        menu.style.left = buttonRect.left + 'px';
                    }
                    
                    // Ensure menu doesn't go off-screen
                    const menuRect = menu.getBoundingClientRect();
                    if (menuRect.right > window.innerWidth) {
                        menu.style.left = (window.innerWidth - menuRect.width - 10) + 'px';
                    }
                }
            });
        }
    });
    
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        let clickedOnDropdown = false;
        
        // Check if click was on a dropdown button or menu
        dropdownButtons.forEach((button, index) => {
            if (button && button.contains(e.target)) {
                clickedOnDropdown = true;
            }
        });
        
        dropdownMenus.forEach(menu => {
            if (menu && menu.contains(e.target)) {
                clickedOnDropdown = true;
            }
        });
        
        // If click was outside, hide all menus
        if (!clickedOnDropdown) {
            hideAllMenus();
        }
    });
    
    // Close dropdowns when pressing Escape
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            hideAllMenus();
        }
    });
});
