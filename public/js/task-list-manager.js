/**
 * Task List Manager
 *
 * This script manages task lists in the dashboard, including:
 * - Detecting overflow in task lists
 * - Showing/hiding expand buttons
 * - Expanding/collapsing task lists
 * - Handling ADHD-friendly animations and transitions
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('Task List Manager loaded');

    // Get task widgets and lists
    const todayTasksWidget = document.querySelector('[data-widget="today-tasks"]');
    const overdueTasksWidget = document.querySelector('[data-widget="overdue-tasks"]');
    const todayTasksList = document.getElementById('today-tasks-list');
    const overdueTasksList = document.getElementById('overdue-tasks-list');
    const todayTasksContainer = document.getElementById('today-tasks-container');
    const overdueTasksContainer = document.getElementById('overdue-tasks-container');

    // Check if elements exist
    if (!todayTasksWidget || !overdueTasksWidget || !todayTasksList || !overdueTasksList || !todayTasksContainer || !overdueTasksContainer) {
        console.warn('Some task list elements not found');
        return;
    }

    // Function to check if a list has overflow
    function checkOverflow(list, container) {
        if (!list || !container) return false;

        // Check if the list's scroll height is greater than its client height
        const hasOverflow = list.scrollHeight > list.clientHeight;

        // Add or remove the has-overflow class
        if (hasOverflow) {
            container.classList.add('has-overflow');
        } else {
            container.classList.remove('has-overflow');
        }

        return hasOverflow;
    }





    // Check for overflow on initial load
    checkOverflow(todayTasksList, todayTasksContainer);
    checkOverflow(overdueTasksList, overdueTasksContainer);

    // Check for overflow when window is resized
    window.addEventListener('resize', function() {
        checkOverflow(todayTasksList, todayTasksContainer);
        checkOverflow(overdueTasksList, overdueTasksContainer);
    });

    // Add visual cues for scrollable content
    function addScrollHints() {
        // Today's tasks scroll hint
        if (todayTasksList && todayTasksList.scrollHeight > todayTasksList.clientHeight) {
            // Add subtle animation to indicate scrollability
            todayTasksContainer.classList.add('has-overflow');

            // Add scroll event listener to show/hide gradient
            todayTasksList.addEventListener('scroll', function() {
                const isAtBottom = this.scrollHeight - this.scrollTop - this.clientHeight < 10;

                if (isAtBottom) {
                    todayTasksContainer.classList.remove('has-more-content');
                } else {
                    todayTasksContainer.classList.add('has-more-content');
                }
            });

            // Trigger scroll event to initialize state
            todayTasksList.dispatchEvent(new Event('scroll'));
        }

        // Overdue tasks scroll hint
        if (overdueTasksList && overdueTasksList.scrollHeight > overdueTasksList.clientHeight) {
            // Add subtle animation to indicate scrollability
            overdueTasksContainer.classList.add('has-overflow');

            // Add scroll event listener to show/hide gradient
            overdueTasksList.addEventListener('scroll', function() {
                const isAtBottom = this.scrollHeight - this.scrollTop - this.clientHeight < 10;

                if (isAtBottom) {
                    overdueTasksContainer.classList.remove('has-more-content');
                } else {
                    overdueTasksContainer.classList.add('has-more-content');
                }
            });

            // Trigger scroll event to initialize state
            overdueTasksList.dispatchEvent(new Event('scroll'));
        }
    }

    // Call addScrollHints after a short delay to ensure content is fully loaded
    setTimeout(addScrollHints, 500);

    // ADHD-friendly focus highlighting for task items
    document.querySelectorAll('.task-item').forEach(item => {
        // Remove any existing blue highlight classes
        item.classList.remove('bg-primary-50', 'dark:bg-primary-900', 'border-l-4', 'border-primary-500');

        // Add hover effect
        item.addEventListener('mouseenter', function() {
            this.style.backgroundColor = 'rgba(243, 244, 246, 0.2)';
        });

        item.addEventListener('mouseleave', function() {
            this.style.backgroundColor = '';
        });

        // Add focus effect for keyboard navigation
        item.addEventListener('focus', function() {
            this.style.outline = '2px solid rgba(59, 130, 246, 0.5)';
            this.style.backgroundColor = 'rgba(243, 244, 246, 0.2)';
        });

        item.addEventListener('blur', function() {
            this.style.outline = '';
            this.style.backgroundColor = '';
        });
    });

    // Add keyboard navigation for task lists
    function setupKeyboardNavigation(list) {
        if (!list) return;

        list.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                e.preventDefault();

                const items = Array.from(list.querySelectorAll('.task-item'));
                const currentIndex = items.findIndex(item => item === document.activeElement);

                if (currentIndex >= 0) {
                    let nextIndex;

                    if (e.key === 'ArrowDown') {
                        nextIndex = Math.min(currentIndex + 1, items.length - 1);
                    } else {
                        nextIndex = Math.max(currentIndex - 1, 0);
                    }

                    items[nextIndex].focus();

                    // Ensure the focused item is visible
                    items[nextIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            }
        });
    }

    // Setup keyboard navigation
    setupKeyboardNavigation(todayTasksList);
    setupKeyboardNavigation(overdueTasksList);
});
