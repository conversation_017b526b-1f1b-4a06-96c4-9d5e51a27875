/**
 * AI Agent Management
 *
 * JavaScript functionality for managing AI agents
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('AI Agent Management loaded');

    // Initialize form validation
    initFormValidation();

    // Initialize skill selection
    initSkillSelection();

    // Initialize rating sliders
    initRatingSliders();

    // Initialize agent status toggle
    initStatusToggle();

    // Initialize agent deletion confirmation
    initDeleteConfirmation();

    // Initialize agent interaction handling
    initAgentInteractions();
});

/**
 * Initialize form validation
 */
function initFormValidation() {
    const agentForms = document.querySelectorAll('.agent-form');
    
    agentForms.forEach(form => {
        form.addEventListener('submit', function(event) {
            let isValid = true;
            
            // Validate required fields
            const requiredFields = form.querySelectorAll('[required]');
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    isValid = false;
                    field.classList.add('border-red-500', 'dark:border-red-500');
                    
                    // Add error message if it doesn't exist
                    let errorMessage = field.parentNode.querySelector('.error-message');
                    if (!errorMessage) {
                        errorMessage = document.createElement('p');
                        errorMessage.className = 'text-red-500 text-xs mt-1 error-message';
                        errorMessage.textContent = 'This field is required';
                        field.parentNode.appendChild(errorMessage);
                    }
                } else {
                    field.classList.remove('border-red-500', 'dark:border-red-500');
                    
                    // Remove error message if it exists
                    const errorMessage = field.parentNode.querySelector('.error-message');
                    if (errorMessage) {
                        errorMessage.remove();
                    }
                }
            });
            
            if (!isValid) {
                event.preventDefault();
                
                // Scroll to the first error
                const firstError = form.querySelector('.border-red-500');
                if (firstError) {
                    firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstError.focus();
                }
            }
        });
    });
}

/**
 * Initialize skill selection
 */
function initSkillSelection() {
    const skillSelectors = document.querySelectorAll('.skill-selector');
    
    skillSelectors.forEach(selector => {
        selector.addEventListener('change', function() {
            const skillId = this.value;
            const skillsContainer = document.getElementById('selected-skills');
            
            if (skillId && skillsContainer) {
                // Check if skill is already selected
                const existingSkill = document.querySelector(`[data-skill-id="${skillId}"]`);
                if (existingSkill) {
                    // Highlight existing skill
                    existingSkill.classList.add('bg-yellow-100', 'dark:bg-yellow-900');
                    setTimeout(() => {
                        existingSkill.classList.remove('bg-yellow-100', 'dark:bg-yellow-900');
                    }, 1000);
                    return;
                }
                
                // Get skill details
                const skillName = this.options[this.selectedIndex].text;
                const skillType = this.options[this.selectedIndex].getAttribute('data-skill-type');
                
                // Create skill item
                const skillItem = document.createElement('div');
                skillItem.className = 'flex items-center justify-between p-2 mb-2 bg-gray-50 dark:bg-gray-750 rounded-lg skill-item';
                skillItem.setAttribute('data-skill-id', skillId);
                
                skillItem.innerHTML = `
                    <div class="flex items-center">
                        <span class="skill-badge ${skillType}">${skillName}</span>
                    </div>
                    <div class="flex items-center">
                        <label class="text-xs text-gray-500 dark:text-gray-400 mr-2">Proficiency:</label>
                        <input type="range" name="skills[${skillId}]" min="1" max="10" value="5" class="w-24 h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer agent-rating-slider">
                        <span class="text-xs ml-2 proficiency-value">5</span>
                        <button type="button" class="ml-3 text-red-500 hover:text-red-700 dark:hover:text-red-300 remove-skill">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                `;
                
                skillsContainer.appendChild(skillItem);
                
                // Initialize proficiency slider
                const slider = skillItem.querySelector('input[type="range"]');
                const proficiencyValue = skillItem.querySelector('.proficiency-value');
                
                slider.addEventListener('input', function() {
                    proficiencyValue.textContent = this.value;
                });
                
                // Initialize remove button
                const removeButton = skillItem.querySelector('.remove-skill');
                removeButton.addEventListener('click', function() {
                    skillItem.remove();
                });
                
                // Reset selector
                this.value = '';
            }
        });
    });
    
    // Handle existing skills
    const existingSkills = document.querySelectorAll('.existing-skill');
    existingSkills.forEach(skill => {
        const slider = skill.querySelector('input[type="range"]');
        const proficiencyValue = skill.querySelector('.proficiency-value');
        
        if (slider && proficiencyValue) {
            slider.addEventListener('input', function() {
                proficiencyValue.textContent = this.value;
            });
        }
        
        const removeButton = skill.querySelector('.remove-skill');
        if (removeButton) {
            removeButton.addEventListener('click', function() {
                skill.remove();
            });
        }
    });
}

/**
 * Initialize rating sliders
 */
function initRatingSliders() {
    const ratingSliders = document.querySelectorAll('.agent-rating-slider');
    
    ratingSliders.forEach(slider => {
        const valueDisplay = slider.nextElementSibling;
        
        if (valueDisplay) {
            valueDisplay.textContent = slider.value;
            
            slider.addEventListener('input', function() {
                valueDisplay.textContent = this.value;
            });
        }
    });
}

/**
 * Initialize agent status toggle
 */
function initStatusToggle() {
    const statusToggles = document.querySelectorAll('.status-toggle');
    
    statusToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const statusValue = document.getElementById('status-value');
            if (statusValue) {
                statusValue.value = this.value;
            }
            
            // Update status indicator
            const statusIndicator = document.querySelector('.status-indicator');
            if (statusIndicator) {
                statusIndicator.className = 'status-indicator';
                statusIndicator.classList.add(`status-${this.value}`);
            }
            
            // Update status text
            const statusText = document.querySelector('.status-text');
            if (statusText) {
                statusText.textContent = this.value.charAt(0).toUpperCase() + this.value.slice(1);
                
                // Update status text color
                statusText.className = 'status-text';
                switch (this.value) {
                    case 'active':
                        statusText.classList.add('text-green-500', 'dark:text-green-400');
                        break;
                    case 'inactive':
                        statusText.classList.add('text-gray-500', 'dark:text-gray-400');
                        break;
                    case 'training':
                        statusText.classList.add('text-amber-500', 'dark:text-amber-400');
                        break;
                    case 'error':
                        statusText.classList.add('text-red-500', 'dark:text-red-400');
                        break;
                }
            }
        });
    });
}

/**
 * Initialize agent deletion confirmation
 */
function initDeleteConfirmation() {
    const deleteButtons = document.querySelectorAll('.delete-agent-btn');
    
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(event) {
            event.preventDefault();
            
            const agentName = this.getAttribute('data-agent-name');
            const confirmMessage = `Are you sure you want to delete the agent "${agentName}"? This action cannot be undone.`;
            
            if (confirm(confirmMessage)) {
                window.location.href = this.getAttribute('href');
            }
        });
    });
}

/**
 * Initialize agent interaction handling
 */
function initAgentInteractions() {
    const interactionForm = document.getElementById('agent-interaction-form');
    
    if (interactionForm) {
        interactionForm.addEventListener('submit', function(event) {
            event.preventDefault();
            
            const agentId = this.getAttribute('data-agent-id');
            const interactionType = document.getElementById('interaction-type').value;
            const interactionContent = document.getElementById('interaction-content').value;
            
            if (!interactionContent.trim()) {
                alert('Please enter interaction content');
                return;
            }
            
            // Send interaction to server
            fetch('/momentum/ai-agents/interact', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                body: JSON.stringify({
                    agent_id: agentId,
                    interaction_type: interactionType,
                    content: interactionContent
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Add interaction to the list
                    const interactionsList = document.getElementById('agent-interactions');
                    
                    if (interactionsList) {
                        const interactionItem = document.createElement('li');
                        interactionItem.className = 'py-3 border-b border-gray-200 dark:border-gray-700 last:border-0';
                        
                        interactionItem.innerHTML = `
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-indigo-100 dark:bg-indigo-900 text-indigo-500 dark:text-indigo-400">
                                        <i class="fas fa-user"></i>
                                    </span>
                                </div>
                                <div class="ml-3 flex-1">
                                    <div class="flex justify-between items-center">
                                        <p class="text-sm font-medium text-gray-900 dark:text-white">
                                            You
                                        </p>
                                        <p class="text-xs text-gray-500 dark:text-gray-400">
                                            Just now
                                        </p>
                                    </div>
                                    <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300">
                                            ${interactionType.charAt(0).toUpperCase() + interactionType.slice(1)}
                                        </span>
                                        ${interactionContent}
                                    </p>
                                </div>
                            </div>
                        `;
                        
                        interactionsList.prepend(interactionItem);
                        
                        // Clear form
                        document.getElementById('interaction-content').value = '';
                        
                        // Add agent response if provided
                        if (data.response) {
                            const responseItem = document.createElement('li');
                            responseItem.className = 'py-3 border-b border-gray-200 dark:border-gray-700 last:border-0';
                            
                            responseItem.innerHTML = `
                                <div class="flex items-start">
                                    <div class="flex-shrink-0">
                                        <span class="inline-flex items-center justify-center h-8 w-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-500 dark:text-blue-400">
                                            <i class="fas fa-robot"></i>
                                        </span>
                                    </div>
                                    <div class="ml-3 flex-1">
                                        <div class="flex justify-between items-center">
                                            <p class="text-sm font-medium text-gray-900 dark:text-white">
                                                Agent
                                            </p>
                                            <p class="text-xs text-gray-500 dark:text-gray-400">
                                                Just now
                                            </p>
                                        </div>
                                        <p class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                            ${data.response}
                                        </p>
                                    </div>
                                </div>
                            `;
                            
                            interactionsList.prepend(responseItem);
                        }
                    }
                } else {
                    alert('Error: ' + (data.message || 'Failed to send interaction'));
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while sending the interaction');
            });
        });
    }
}
