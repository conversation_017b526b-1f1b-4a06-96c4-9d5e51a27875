<?php
// Include necessary files
require_once '../src/utils/Database.php';
require_once '../src/models/IncomeSource.php';

// Initialize the model
$incomeSourceModel = new IncomeSource();

// Use user ID 1 (the one we updated the income sources to)
$userId = 1;

// Get all income sources for this user without filtering
$incomeSources = $incomeSourceModel->getUserIncomeSources($userId, true);

// Get income sources with total income
$startDate = date('Y-m-01'); // First day of current month
$endDate = date('Y-m-t'); // Last day of current month

$sourcesWithIncome = $incomeSourceModel->getSourcesWithTotalIncome($userId, $startDate, $endDate);

// Calculate total income
$totalIncome = 0;
foreach ($sourcesWithIncome as $source) {
    $totalIncome += (float)($source['total_income'] ?? 0);
}

// Get all income sources in the database
$db = Database::getInstance();
$allSources = $db->fetchAll("SELECT * FROM income_sources");
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Income Sources Test</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">Income Sources Test</h1>
        
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">Basic Information</h2>
            <p><strong>User ID:</strong> <?= $userId ?></p>
            <p><strong>Date Range:</strong> <?= $startDate ?> to <?= $endDate ?></p>
            <p><strong>Total Income:</strong> <?= number_format($totalIncome, 2) ?></p>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">Income Sources from getUserIncomeSources()</h2>
            <p class="mb-4">Found <?= count($incomeSources) ?> income sources</p>
            
            <?php if (count($incomeSources) > 0): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b">ID</th>
                                <th class="py-2 px-4 border-b">Name</th>
                                <th class="py-2 px-4 border-b">Active</th>
                                <th class="py-2 px-4 border-b">Recurring</th>
                                <th class="py-2 px-4 border-b">Category</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($incomeSources as $source): ?>
                                <tr>
                                    <td class="py-2 px-4 border-b"><?= $source['id'] ?></td>
                                    <td class="py-2 px-4 border-b"><?= htmlspecialchars($source['name']) ?></td>
                                    <td class="py-2 px-4 border-b"><?= $source['is_active'] ? 'Yes' : 'No' ?></td>
                                    <td class="py-2 px-4 border-b"><?= $source['is_recurring'] ? 'Yes' : 'No' ?></td>
                                    <td class="py-2 px-4 border-b"><?= htmlspecialchars($source['category'] ?? 'N/A') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-red-500">No income sources found</p>
            <?php endif; ?>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-md mb-6">
            <h2 class="text-xl font-semibold mb-4">Income Sources from getSourcesWithTotalIncome()</h2>
            <p class="mb-4">Found <?= count($sourcesWithIncome) ?> income sources</p>
            
            <?php if (count($sourcesWithIncome) > 0): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b">ID</th>
                                <th class="py-2 px-4 border-b">Name</th>
                                <th class="py-2 px-4 border-b">Active</th>
                                <th class="py-2 px-4 border-b">Recurring</th>
                                <th class="py-2 px-4 border-b">Category</th>
                                <th class="py-2 px-4 border-b">Transactions</th>
                                <th class="py-2 px-4 border-b">Total Income</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($sourcesWithIncome as $source): ?>
                                <tr>
                                    <td class="py-2 px-4 border-b"><?= $source['id'] ?></td>
                                    <td class="py-2 px-4 border-b"><?= htmlspecialchars($source['name']) ?></td>
                                    <td class="py-2 px-4 border-b"><?= $source['is_active'] ? 'Yes' : 'No' ?></td>
                                    <td class="py-2 px-4 border-b"><?= $source['is_recurring'] ? 'Yes' : 'No' ?></td>
                                    <td class="py-2 px-4 border-b"><?= htmlspecialchars($source['category'] ?? 'N/A') ?></td>
                                    <td class="py-2 px-4 border-b"><?= $source['transaction_count'] ?? 0 ?></td>
                                    <td class="py-2 px-4 border-b"><?= number_format($source['total_income'] ?? 0, 2) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-red-500">No income sources found</p>
            <?php endif; ?>
        </div>
        
        <div class="bg-white p-6 rounded-lg shadow-md">
            <h2 class="text-xl font-semibold mb-4">All Income Sources in Database</h2>
            <p class="mb-4">Found <?= count($allSources) ?> income sources</p>
            
            <?php if (count($allSources) > 0): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full bg-white">
                        <thead>
                            <tr>
                                <th class="py-2 px-4 border-b">ID</th>
                                <th class="py-2 px-4 border-b">User ID</th>
                                <th class="py-2 px-4 border-b">Name</th>
                                <th class="py-2 px-4 border-b">Active</th>
                                <th class="py-2 px-4 border-b">Recurring</th>
                                <th class="py-2 px-4 border-b">Category</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($allSources as $source): ?>
                                <tr>
                                    <td class="py-2 px-4 border-b"><?= $source['id'] ?></td>
                                    <td class="py-2 px-4 border-b"><?= $source['user_id'] ?></td>
                                    <td class="py-2 px-4 border-b"><?= htmlspecialchars($source['name']) ?></td>
                                    <td class="py-2 px-4 border-b"><?= $source['is_active'] ? 'Yes' : 'No' ?></td>
                                    <td class="py-2 px-4 border-b"><?= $source['is_recurring'] ? 'Yes' : 'No' ?></td>
                                    <td class="py-2 px-4 border-b"><?= htmlspecialchars($source['category'] ?? 'N/A') ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <p class="text-red-500">No income sources found in database</p>
            <?php endif; ?>
        </div>
    </div>
</body>
</html>
