/**
 * Layout Visual Styles
 * 
 * This stylesheet provides dramatic visual differences between different dashboard layouts.
 */

/* ===== Layout Labels ===== */
#layout-label {
    position: absolute;
    top: -10px;
    left: 20px;
    padding: 2px 10px;
    border-radius: 4px;
    font-size: 0.75rem;
    font-weight: 600;
    z-index: 10;
    color: white;
}

/* ===== Body Layout Classes ===== */

/* ADHD Optimized Layout */
body.layout-adhd-optimized #dashboard-widgets {
    background-color: rgba(14, 165, 233, 0.08) !important;
    padding: 1.5rem !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 0 0 2px rgba(14, 165, 233, 0.2) !important;
}

body.layout-adhd-optimized #current-focus-widget {
    grid-column: 1 / -1 !important;
    border-left: 6px solid rgba(14, 165, 233, 0.8) !important;
    transform: scale(1.02) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

body.layout-adhd-optimized [data-widget="today-tasks"] {
    border-left: 4px solid rgba(59, 130, 246, 0.8) !important;
}

body.layout-adhd-optimized [data-widget="overdue-tasks"] {
    border-left: 4px solid rgba(239, 68, 68, 0.8) !important;
}

body.layout-adhd-optimized [data-widget="keyboard-shortcuts"] {
    border-left: 4px solid rgba(139, 92, 246, 0.8) !important;
}

body.layout-adhd-optimized [data-widget="adhd-guide"] {
    border-left: 4px solid rgba(16, 185, 129, 0.8) !important;
}

body.layout-adhd-optimized [data-widget="help-center"] {
    border-left: 4px solid rgba(245, 158, 11, 0.8) !important;
}

/* Focus Layout */
body.layout-focus #dashboard-widgets {
    background-color: rgba(0, 0, 0, 0.05) !important;
    padding: 1.5rem !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2) !important;
    display: flex !important;
    flex-direction: column !important;
    gap: 1.5rem !important;
}

body.layout-focus [data-widget]:not(#current-focus-widget):not([data-widget="today-tasks"]):not([data-widget="keyboard-shortcuts"]) {
    display: none !important;
}

body.layout-focus #current-focus-widget {
    grid-column: 1 / -1 !important;
    order: -1 !important;
    border-left: 8px solid rgba(99, 102, 241, 0.8) !important;
    transform: scale(1.03) !important;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    background-color: rgba(255, 255, 255, 0.8) !important;
}

body.layout-focus.dark #current-focus-widget {
    background-color: rgba(31, 41, 55, 0.8) !important;
}

body.layout-focus [data-widget="today-tasks"] {
    grid-column: 1 / -1 !important;
    order: 0 !important;
    border-left: 5px solid rgba(59, 130, 246, 0.8) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

body.layout-focus [data-widget="keyboard-shortcuts"] {
    grid-column: 1 / -1 !important;
    order: 1 !important;
    border-left: 5px solid rgba(139, 92, 246, 0.8) !important;
    max-height: 200px !important;
    overflow-y: auto !important;
}

/* Standard Layout */
body.layout-standard #dashboard-widgets {
    background-color: rgba(0, 0, 0, 0.02) !important;
    padding: 1.25rem !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2) !important;
}

body.layout-standard #current-focus-widget {
    grid-column: 1 / -1 !important;
    border-left: 4px solid rgba(16, 185, 129, 0.8) !important;
}

body.layout-standard [data-widget]:not(#current-focus-widget) {
    min-height: 200px !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    border-radius: 0.5rem !important;
}

body.layout-standard.dark [data-widget]:not(#current-focus-widget) {
    border: 1px solid rgba(255, 255, 255, 0.05) !important;
}

/* Custom Layout */
body.layout-custom #dashboard-widgets {
    background-color: rgba(139, 92, 246, 0.05) !important;
    padding: 1.5rem !important;
    border-radius: 0.75rem !important;
    border: 2px dashed rgba(139, 92, 246, 0.3) !important;
    box-shadow: none !important;
}

body.layout-custom [data-widget] {
    border: 2px dotted rgba(139, 92, 246, 0.3) !important;
    position: relative !important;
    cursor: move !important;
}

body.layout-custom #widget-controls {
    display: block !important;
    margin-bottom: 1.5rem !important;
    padding: 1rem !important;
    background-color: rgba(139, 92, 246, 0.05) !important;
    border-radius: 0.5rem !important;
    border: 2px dashed rgba(139, 92, 246, 0.3) !important;
}

/* ===== Focus Mode Enhancements ===== */

/* Reduce opacity of non-essential elements in focus mode */
body.focus-mode [data-widget]:not(#current-focus-widget):not([data-widget="today-tasks"]):not([data-widget="keyboard-shortcuts"]) {
    opacity: 0.5 !important;
    filter: grayscale(30%) !important;
}

/* ===== Widget Hover Effects ===== */

/* ADHD Optimized hover effects */
body.layout-adhd-optimized [data-widget]:hover {
    transform: translateY(-5px) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

/* Standard hover effects */
body.layout-standard [data-widget]:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Custom hover effects */
body.layout-custom [data-widget]:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
    border-color: rgba(139, 92, 246, 0.6) !important;
}

/* ===== Transition Effects ===== */

/* Smooth transitions when changing layouts */
#dashboard-widgets,
#dashboard-widgets > div {
    transition: all 0.3s ease-in-out !important;
}
