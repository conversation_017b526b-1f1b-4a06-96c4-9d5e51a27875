#!/usr/bin/env python
"""
Pinterest Board Listing Script (Dummy Version)

This script creates a dummy board for testing purposes.
"""

import sys
import json

# Get arguments from command line
if len(sys.argv) < 4:
    print(json.dumps({
        "success": False,
        "message": "Missing required arguments. Usage: list_boards_dummy.py email password username [chrome_profile]"
    }))
    sys.exit(1)

email = sys.argv[1]
password = sys.argv[2]
username = sys.argv[3]

# Create a dummy board for testing
dummy_board = {
    "id": "dummy_board_id",
    "name": "Dummy Board (For Testing)",
    "url": f"https://www.pinterest.com/{username}/boards/",
    "pin_count": 0,
    "description": "This is a dummy board created for testing purposes.",
    "privacy": "public",
    "created_at": ""
}

# Return the dummy board
print(json.dumps({
    "success": True,
    "boards": [dummy_board],
    "message": "Created a dummy board for testing."
}))
