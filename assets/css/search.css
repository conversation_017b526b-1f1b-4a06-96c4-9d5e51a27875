/* Search Page Specific Styles */

/* Search Header */
.search-header {
    background-color: var(--light-color);
    padding: 100px 0 30px;
    border-bottom: 1px solid var(--gray-200);
}

.search-form-container {
    max-width: 800px;
    margin: 0 auto;
}

.search-form-inline {
    background-color: var(--white);
    border-radius: 12px;
    padding: 8px;
    box-shadow: var(--box-shadow);
}

.search-form-inline .search-input-group {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 8px;
    align-items: center;
}

/* Main Layout */
.search-main {
    padding: 40px 0 80px;
}

.search-layout {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 40px;
}

/* Filters Sidebar */
.filters-sidebar {
    background-color: var(--white);
    border-radius: 12px;
    padding: 24px;
    box-shadow: var(--box-shadow);
    height: fit-content;
    position: sticky;
    top: 120px;
}

.filters-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--gray-200);
}

.filters-header h3 {
    margin: 0;
    color: var(--gray-900);
}

.btn-clear {
    background: none;
    border: none;
    color: var(--primary-color);
    font-size: 0.9rem;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: var(--transition);
}

.btn-clear:hover {
    background-color: var(--gray-100);
}

.filter-group {
    margin-bottom: 24px;
}

.filter-group h4 {
    margin-bottom: 12px;
    color: var(--gray-800);
    font-size: 1rem;
}

/* Rating Filters */
.rating-filters {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.rating-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--transition);
}

.rating-option:hover {
    background-color: var(--gray-100);
}

.rating-option input[type="radio"] {
    margin-right: 8px;
}

.rating-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
}

.rating-label .stars {
    color: var(--secondary-color);
}

/* Price Filter */
.price-filter {
    margin-top: 8px;
}

.price-filter input[type="range"] {
    width: 100%;
    height: 6px;
    border-radius: 3px;
    background: var(--gray-200);
    outline: none;
    -webkit-appearance: none;
}

.price-filter input[type="range"]::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
}

.price-filter input[type="range"]::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: var(--primary-color);
    cursor: pointer;
    border: none;
}

.price-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 0.85rem;
    color: var(--gray-600);
}

/* Select Filters */
.filter-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    background-color: var(--white);
    font-size: 0.9rem;
    color: var(--gray-700);
    cursor: pointer;
}

.filter-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

/* Checkbox Options */
.checkbox-option {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 8px;
    border-radius: 6px;
    transition: var(--transition);
}

.checkbox-option:hover {
    background-color: var(--gray-100);
}

.checkbox-option input[type="checkbox"] {
    margin-right: 12px;
    width: 18px;
    height: 18px;
}

.checkbox-label {
    font-size: 0.9rem;
    color: var(--gray-700);
}

/* Results Content */
.results-content {
    min-height: 600px;
}

.results-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid var(--gray-200);
}

.results-info h2 {
    margin-bottom: 4px;
    color: var(--gray-900);
}

.results-subtitle {
    color: var(--gray-600);
    margin: 0;
    font-size: 0.9rem;
}

.sort-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.sort-controls label {
    font-size: 0.9rem;
    color: var(--gray-600);
}

.sort-select {
    padding: 8px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 6px;
    background-color: var(--white);
    font-size: 0.9rem;
    cursor: pointer;
}

/* Search Results */
.search-results {
    display: grid;
    grid-template-columns: 1fr;
    gap: 24px;
}

.plumber-card {
    display: grid;
    grid-template-columns: 200px 1fr;
    gap: 20px;
    background-color: var(--white);
    border-radius: 12px;
    padding: 20px;
    box-shadow: var(--box-shadow);
    border: 1px solid var(--gray-200);
    transition: var(--transition);
}

.plumber-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.plumber-image {
    position: relative;
    border-radius: 8px;
    overflow: hidden;
    height: 150px;
}

.plumber-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.verified-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: var(--success-color);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.plumber-info {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.plumber-info h3 {
    margin-bottom: 8px;
    color: var(--gray-900);
}

.rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
}

.specialties {
    color: var(--gray-600);
    margin-bottom: 12px;
    font-size: 0.9rem;
}

.plumber-details {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 12px;
    font-size: 0.85rem;
    color: var(--gray-500);
}

.plumber-details span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.plumber-meta {
    display: flex;
    gap: 16px;
    margin-bottom: 12px;
    font-size: 0.9rem;
}

.pricing {
    font-weight: 600;
    color: var(--primary-color);
}

.response-time {
    color: var(--success-color);
    font-weight: 500;
}

.availability {
    color: var(--gray-600);
}

.certifications {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    margin-bottom: 16px;
}

.cert-badge {
    background-color: var(--gray-100);
    color: var(--gray-700);
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.plumber-actions {
    display: flex;
    gap: 12px;
}

.plumber-actions .btn {
    flex: 1;
    padding: 10px 16px;
    font-size: 0.9rem;
}

/* Loading State */
.loading-spinner {
    text-align: center;
    padding: 60px 20px;
    color: var(--gray-500);
}

.loading-spinner i {
    font-size: 2rem;
    margin-bottom: 16px;
    color: var(--primary-color);
}

/* No Results */
.no-results {
    text-align: center;
    padding: 60px 20px;
}

.no-results-icon {
    font-size: 3rem;
    color: var(--gray-400);
    margin-bottom: 16px;
}

.no-results h3 {
    color: var(--gray-700);
    margin-bottom: 8px;
}

.no-results p {
    color: var(--gray-500);
    margin-bottom: 24px;
}

/* Pagination */
.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 8px;
    margin-top: 40px;
}

.page-btn {
    padding: 10px 16px;
    border: 1px solid var(--gray-300);
    background-color: var(--white);
    color: var(--gray-700);
    border-radius: 6px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.page-btn:hover {
    background-color: var(--gray-100);
    border-color: var(--gray-400);
}

.page-btn.active {
    background-color: var(--primary-color);
    color: var(--white);
    border-color: var(--primary-color);
}

.page-ellipsis {
    padding: 10px 8px;
    color: var(--gray-400);
}

/* Trust Section */
.trust-section {
    background-color: var(--light-color);
    padding: 60px 0;
    margin-top: 40px;
}

.trust-section .trust-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 2rem;
}

.trust-section .trust-item {
    text-align: center;
    padding: 1.5rem;
}

.trust-section .trust-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
}

.trust-section .trust-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.trust-section h4 {
    color: var(--gray-900);
    margin-bottom: 0.5rem;
    font-size: 1rem;
}

.trust-section p {
    color: var(--gray-600);
    font-size: 0.9rem;
    margin: 0;
}

/* Responsive Design for Search Page */
@media (max-width: 1024px) {
    .search-layout {
        grid-template-columns: 250px 1fr;
        gap: 30px;
    }
    
    .plumber-card {
        grid-template-columns: 150px 1fr;
        gap: 16px;
    }
    
    .plumber-image {
        height: 120px;
    }
}

@media (max-width: 768px) {
    .search-header {
        padding: 90px 0 20px;
    }
    
    .search-form-inline .search-input-group {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .search-layout {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .filters-sidebar {
        position: static;
        order: 2;
    }
    
    .results-content {
        order: 1;
    }
    
    .results-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }
    
    .plumber-card {
        grid-template-columns: 1fr;
        gap: 16px;
    }
    
    .plumber-image {
        height: 200px;
    }
    
    .plumber-details {
        flex-direction: column;
        gap: 8px;
    }
    
    .plumber-actions {
        flex-direction: column;
    }
    
    .trust-section .trust-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 480px) {
    .search-main {
        padding: 20px 0 60px;
    }
    
    .filters-sidebar {
        padding: 16px;
    }
    
    .plumber-card {
        padding: 16px;
    }
    
    .plumber-meta {
        flex-direction: column;
        gap: 8px;
    }
    
    .trust-section .trust-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
    }
    
    .pagination {
        flex-wrap: wrap;
        gap: 4px;
    }
    
    .page-btn {
        padding: 8px 12px;
        font-size: 0.85rem;
    }
}
