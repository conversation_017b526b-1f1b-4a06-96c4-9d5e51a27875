/* Responsive Design */

/* Large screens (1200px and up) */
@media (min-width: 1200px) {
    .container {
        max-width: 1400px;
    }
    
    .hero-text h1 {
        font-size: 3.5rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .plumbers-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Medium screens (768px to 1199px) */
@media (max-width: 1199px) {
    .hero-content {
        gap: 2rem;
    }
    
    .hero-text h1 {
        font-size: 2.5rem;
    }
    
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .plumbers-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        grid-template-columns: 2fr 1fr 1fr;
        gap: 2rem;
    }
}

/* Tablet screens (768px to 991px) */
@media (max-width: 991px) {
    .nav-menu {
        position: fixed;
        top: 100%;
        left: 0;
        right: 0;
        background-color: var(--white);
        flex-direction: column;
        padding: 2rem;
        box-shadow: var(--box-shadow);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: var(--transition);
    }
    
    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }
    
    .nav-list {
        flex-direction: column;
        gap: 1rem;
        margin-bottom: 2rem;
    }
    
    .nav-buttons {
        flex-direction: column;
        width: 100%;
    }
    
    .nav-toggle {
        display: flex;
    }
    
    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }
    
    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }
    
    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
    
    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .hero-text {
        order: 2;
    }
    
    .hero-image {
        order: 1;
    }
    
    .search-input-group {
        grid-template-columns: 1fr;
        gap: 12px;
    }
    
    .btn-search {
        width: 100%;
    }
    
    .steps-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .trust-grid {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .footer-content {
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
    }
}

/* Mobile screens (576px to 767px) */
@media (max-width: 767px) {
    .container {
        padding: 0 16px;
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.25rem; }
    
    .hero {
        padding: 100px 0 60px;
    }
    
    .hero-text h1 {
        font-size: 2rem;
        margin-bottom: 1rem;
    }
    
    .hero-text p {
        font-size: 1rem;
    }
    
    .search-container {
        padding: 6px;
    }
    
    .input-wrapper input {
        padding: 14px 14px 14px 44px;
        font-size: 0.9rem;
    }
    
    .btn-search {
        padding: 14px 24px;
        font-size: 1rem;
    }
    
    .emergency-banner {
        padding: 10px 12px;
        font-size: 0.9rem;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .service-card {
        padding: 1.5rem;
    }
    
    .plumbers-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .plumber-details {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .trust-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
    
    .trust-item {
        padding: 1.5rem;
    }
    
    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }
    
    .footer-bottom {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }
    
    .footer-links {
        justify-content: center;
    }
}

/* Small mobile screens (up to 575px) */
@media (max-width: 575px) {
    .container {
        padding: 0 12px;
    }
    
    .navbar {
        padding: 0.75rem 0;
    }
    
    .logo {
        font-size: 1.25rem;
    }
    
    .logo i {
        font-size: 1.5rem;
    }
    
    .hero {
        padding: 90px 0 50px;
    }
    
    .hero-text h1 {
        font-size: 1.75rem;
        line-height: 1.2;
    }
    
    .popular-services,
    .how-it-works,
    .featured-plumbers,
    .trust-indicators {
        padding: 60px 0;
    }
    
    .service-card,
    .plumber-card {
        margin: 0 -4px;
    }
    
    .service-card {
        padding: 1.25rem;
    }
    
    .service-icon {
        width: 50px;
        height: 50px;
    }
    
    .service-icon i {
        font-size: 1.25rem;
    }
    
    .step-icon {
        width: 60px;
        height: 60px;
    }
    
    .step-icon i {
        font-size: 1.5rem;
    }
    
    .step-number {
        width: 30px;
        height: 30px;
        font-size: 1rem;
    }
    
    .plumber-info {
        padding: 1.25rem;
    }
    
    .plumber-image {
        height: 180px;
    }
    
    .trust-icon {
        width: 60px;
        height: 60px;
    }
    
    .trust-icon i {
        font-size: 1.5rem;
    }
    
    .footer {
        padding: 40px 0 20px;
    }
    
    .social-links a {
        width: 35px;
        height: 35px;
    }
}

/* Extra small screens (up to 400px) */
@media (max-width: 400px) {
    .hero-text h1 {
        font-size: 1.5rem;
    }
    
    .search-container {
        padding: 4px;
    }
    
    .input-wrapper input {
        padding: 12px 12px 12px 40px;
        font-size: 0.85rem;
    }
    
    .input-wrapper i {
        left: 12px;
        font-size: 0.9rem;
    }
    
    .btn-search {
        padding: 12px 20px;
        font-size: 0.9rem;
    }
    
    .service-card,
    .plumber-info,
    .trust-item {
        padding: 1rem;
    }
    
    .emergency-banner {
        padding: 8px 10px;
        font-size: 0.85rem;
    }
}

/* Landscape orientation adjustments */
@media (max-height: 500px) and (orientation: landscape) {
    .hero {
        padding: 80px 0 40px;
    }
    
    .hero-text h1 {
        font-size: 1.75rem;
        margin-bottom: 0.75rem;
    }
    
    .hero-text p {
        margin-bottom: 1rem;
    }
    
    .search-form {
        margin-bottom: 1rem;
    }
    
    .emergency-banner {
        padding: 8px 12px;
    }
}

/* Print styles */
@media print {
    .header,
    .nav-toggle,
    .emergency-banner,
    .btn,
    .footer {
        display: none !important;
    }
    
    .hero {
        padding: 20px 0;
    }
    
    .hero-content {
        grid-template-columns: 1fr;
    }
    
    .hero-image {
        display: none;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
    
    .service-card,
    .plumber-card,
    .trust-item {
        page-break-inside: avoid;
        border: 1px solid #ccc;
        margin-bottom: 10pt;
    }
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000ff;
        --gray-600: #000000;
        --gray-500: #333333;
        --border-radius: 4px;
    }
    
    .btn-outline {
        border-width: 3px;
    }
    
    .service-card,
    .plumber-card {
        border-width: 2px;
        border-color: #000000;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .service-card:hover,
    .plumber-card:hover,
    .btn-primary:hover {
        transform: none;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --white: #1a1a1a;
        --light-color: #2a2a2a;
        --gray-100: #333333;
        --gray-200: #404040;
        --gray-300: #525252;
        --gray-800: #e5e5e5;
        --gray-900: #ffffff;
    }
    
    body {
        background-color: var(--white);
        color: var(--gray-800);
    }
    
    .header {
        background-color: var(--light-color);
        border-bottom: 1px solid var(--gray-200);
    }
    
    .service-card,
    .plumber-card {
        background-color: var(--light-color);
        border-color: var(--gray-200);
    }
    
    .search-container {
        background-color: var(--light-color);
    }
    
    .input-wrapper input {
        background-color: var(--white);
        border-color: var(--gray-200);
        color: var(--gray-800);
    }
}
