/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #2563eb;
    --primary-dark: #1d4ed8;
    --secondary-color: #f59e0b;
    --success-color: #10b981;
    --danger-color: #ef4444;
    --warning-color: #f59e0b;
    --dark-color: #1f2937;
    --light-color: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    --white: #ffffff;
    --border-radius: 8px;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

body {
    font-family: var(--font-family);
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--white);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: 1rem;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: var(--gray-600);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition);
    gap: 8px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
    transform: translateY(-1px);
}

.btn-outline {
    background-color: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.btn-search {
    padding: 16px 32px;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Header */
.header {
    background-color: var(--white);
    box-shadow: var(--box-shadow);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.navbar {
    padding: 1rem 0;
}

.navbar .container {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--primary-color);
    text-decoration: none;
}

.logo i {
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-list {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-link {
    color: var(--gray-700);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
}

.nav-link:hover {
    color: var(--primary-color);
}

.nav-buttons {
    display: flex;
    gap: 1rem;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background-color: var(--gray-700);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    padding: 120px 0 80px;
    background: linear-gradient(135deg, var(--light-color) 0%, var(--gray-100) 100%);
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.hero-text h1 {
    font-size: 3rem;
    color: var(--gray-900);
    margin-bottom: 1.5rem;
}

.hero-text p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
}

.search-form {
    margin-bottom: 2rem;
}

.search-container {
    background-color: var(--white);
    border-radius: 12px;
    padding: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.search-input-group {
    display: grid;
    grid-template-columns: 1fr 1fr auto;
    gap: 8px;
    align-items: center;
}

.input-wrapper {
    position: relative;
}

.input-wrapper i {
    position: absolute;
    left: 16px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

.input-wrapper input {
    width: 100%;
    padding: 16px 16px 16px 48px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
}

.input-wrapper input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.emergency-banner {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background-color: var(--danger-color);
    color: var(--white);
    border-radius: var(--border-radius);
    font-weight: 500;
}

.emergency-banner a {
    color: var(--white);
    text-decoration: underline;
}

.hero-image img {
    width: 100%;
    height: auto;
    border-radius: 12px;
    box-shadow: var(--box-shadow);
}

/* Popular Services */
.popular-services {
    padding: 80px 0;
}

.popular-services h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--gray-900);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background-color: var(--white);
    border-radius: 12px;
    padding: 2rem;
    text-decoration: none;
    color: inherit;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid var(--gray-200);
}

.service-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 60px;
    height: 60px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
}

.service-icon i {
    font-size: 1.5rem;
    color: var(--white);
}

.service-card h3 {
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.service-card p {
    margin-bottom: 1rem;
}

.service-price {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
    display: block;
    margin-bottom: 1rem;
}

.service-rating {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stars {
    color: var(--secondary-color);
    font-size: 1.1rem;
}

.rating-text {
    color: var(--gray-500);
    font-size: 0.9rem;
}

/* How It Works */
.how-it-works {
    padding: 80px 0;
    background-color: var(--light-color);
}

.how-it-works h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--gray-900);
}

.steps-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 3rem;
}

.step {
    text-align: center;
    position: relative;
}

.step-number {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 40px;
    background-color: var(--primary-color);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.2rem;
}

.step-icon {
    width: 80px;
    height: 80px;
    background-color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: var(--box-shadow);
}

.step-icon i {
    font-size: 2rem;
    color: var(--primary-color);
}

.step h3 {
    color: var(--gray-900);
    margin-bottom: 1rem;
}

/* Featured Plumbers */
.featured-plumbers {
    padding: 80px 0;
}

.featured-plumbers h2 {
    text-align: center;
    margin-bottom: 3rem;
    color: var(--gray-900);
}

.plumbers-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    margin-bottom: 3rem;
}

.plumber-card {
    background-color: var(--white);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border: 1px solid var(--gray-200);
}

.plumber-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.plumber-image {
    position: relative;
    height: 200px;
    overflow: hidden;
}

.plumber-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.verified-badge {
    position: absolute;
    top: 12px;
    right: 12px;
    background-color: var(--success-color);
    color: var(--white);
    padding: 4px 8px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
}

.plumber-info {
    padding: 1.5rem;
}

.plumber-info h3 {
    color: var(--gray-900);
    margin-bottom: 0.5rem;
}

.rating {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 1rem;
}

.specialties {
    color: var(--gray-600);
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.plumber-details {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 0.9rem;
    color: var(--gray-500);
}

.plumber-details span {
    display: flex;
    align-items: center;
    gap: 4px;
}

.pricing {
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.btn-contact {
    width: 100%;
}

.view-all-container {
    text-align: center;
}

/* Trust Indicators */
.trust-indicators {
    padding: 80px 0;
    background-color: var(--light-color);
}

.trust-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.trust-item {
    text-align: center;
    padding: 2rem;
}

.trust-icon {
    width: 80px;
    height: 80px;
    background-color: var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
}

.trust-icon i {
    font-size: 2rem;
    color: var(--white);
}

.trust-item h3 {
    color: var(--gray-900);
    margin-bottom: 1rem;
}

/* Notifications */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 10000;
    max-width: 400px;
    background-color: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
    border-left: 4px solid var(--primary-color);
    animation: slideIn 0.3s ease;
}

.notification-info {
    border-left-color: var(--primary-color);
}

.notification-success {
    border-left-color: var(--success-color);
}

.notification-error {
    border-left-color: var(--danger-color);
}

.notification-warning {
    border-left-color: var(--warning-color);
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
}

.notification-message {
    color: var(--gray-800);
    font-weight: 500;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0;
    margin-left: 16px;
}

.notification-close:hover {
    color: var(--gray-600);
}

.notification-exit {
    animation: slideOut 0.3s ease forwards;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOut {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Modal */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    padding: 20px;
}

.modal-content {
    background-color: var(--white);
    border-radius: 12px;
    max-width: 500px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24px 24px 0;
    border-bottom: 1px solid var(--gray-200);
    margin-bottom: 24px;
}

.modal-header h3 {
    margin: 0;
    color: var(--gray-900);
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: var(--gray-400);
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: var(--transition);
}

.modal-close:hover {
    background-color: var(--gray-100);
    color: var(--gray-600);
}

.modal-body {
    padding: 0 24px 24px;
}

.modal-open {
    overflow: hidden;
}

/* Form Styles */
.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--gray-700);
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid var(--gray-200);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    background-color: var(--white);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

/* Suggestions */
.service-suggestions,
.location-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: var(--white);
    border: 1px solid var(--gray-200);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--box-shadow);
    z-index: 1000;
    display: none;
}

.suggestion-item {
    padding: 12px 16px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
}

.suggestion-item:hover,
.suggestion-item.active {
    background-color: var(--gray-100);
}

.suggestion-item i {
    color: var(--gray-400);
    font-size: 0.9rem;
}

/* Geolocation Button */
.geo-button {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--gray-400);
    cursor: pointer;
    padding: 8px;
    border-radius: 4px;
    transition: var(--transition);
}

.geo-button:hover {
    color: var(--primary-color);
    background-color: var(--gray-100);
}

.geo-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* Animation Classes */
.animate-in {
    animation: fadeInUp 0.6s ease forwards;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Footer */
.footer {
    background-color: var(--gray-900);
    color: var(--gray-300);
    padding: 60px 0 20px;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-brand .logo {
    color: var(--white);
    margin-bottom: 1rem;
}

.footer-brand p {
    margin-bottom: 1.5rem;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background-color: var(--gray-800);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-300);
    transition: var(--transition);
}

.social-links a:hover {
    background-color: var(--primary-color);
    color: var(--white);
}

.footer-section h4 {
    color: var(--white);
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: var(--gray-300);
    text-decoration: none;
    transition: var(--transition);
}

.footer-section ul li a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    border-top: 1px solid var(--gray-800);
    padding-top: 2rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: var(--gray-400);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: var(--primary-color);
}
