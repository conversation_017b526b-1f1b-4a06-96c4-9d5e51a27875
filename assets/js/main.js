// Main JavaScript functionality for PlumberPro

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all components
    initNavigation();
    initSearchForm();
    initContactButtons();
    initScrollEffects();
    initLazyLoading();
    initAnalytics();
});

// Navigation functionality
function initNavigation() {
    const navToggle = document.getElementById('nav-toggle');
    const navMenu = document.getElementById('nav-menu');
    
    if (navToggle && navMenu) {
        navToggle.addEventListener('click', function() {
            navToggle.classList.toggle('active');
            navMenu.classList.toggle('active');
            document.body.classList.toggle('nav-open');
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
                navToggle.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.classList.remove('nav-open');
            }
        });
        
        // Close menu when clicking on nav links
        const navLinks = navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', function() {
                navToggle.classList.remove('active');
                navMenu.classList.remove('active');
                document.body.classList.remove('nav-open');
            });
        });
    }
    
    // Navbar scroll effect
    window.addEventListener('scroll', function() {
        const header = document.querySelector('.header');
        if (window.scrollY > 100) {
            header.classList.add('scrolled');
        } else {
            header.classList.remove('scrolled');
        }
    });
}

// Search form functionality
function initSearchForm() {
    const searchForm = document.getElementById('hero-search');
    const serviceInput = document.getElementById('service-input');
    const locationInput = document.getElementById('location-input');
    
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            handleSearch();
        });
    }
    
    // Service input autocomplete
    if (serviceInput) {
        const services = [
            'Emergency Plumbing',
            'Drain Cleaning',
            'Water Heater Repair',
            'Pipe Repair',
            'Toilet Repair',
            'Faucet Repair',
            'Leak Detection',
            'Sewer Line Repair',
            'Garbage Disposal Repair',
            'Bathroom Plumbing',
            'Kitchen Plumbing',
            'Water Line Repair',
            'Hydro Jetting',
            'Pipe Installation',
            'Plumbing Inspection'
        ];
        
        setupAutocomplete(serviceInput, services, 'service-suggestions');
    }
    
    // Location input with geolocation
    if (locationInput) {
        setupLocationInput(locationInput);
    }
}

// Autocomplete functionality
function setupAutocomplete(input, suggestions, containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;
    
    input.addEventListener('input', function() {
        const value = this.value.toLowerCase();
        container.innerHTML = '';
        
        if (value.length < 2) {
            container.style.display = 'none';
            return;
        }
        
        const filtered = suggestions.filter(item => 
            item.toLowerCase().includes(value)
        ).slice(0, 5);
        
        if (filtered.length > 0) {
            container.style.display = 'block';
            filtered.forEach(item => {
                const div = document.createElement('div');
                div.className = 'suggestion-item';
                div.textContent = item;
                div.addEventListener('click', function() {
                    input.value = item;
                    container.style.display = 'none';
                });
                container.appendChild(div);
            });
        } else {
            container.style.display = 'none';
        }
    });
    
    // Hide suggestions when clicking outside
    document.addEventListener('click', function(e) {
        if (!input.contains(e.target) && !container.contains(e.target)) {
            container.style.display = 'none';
        }
    });
}

// Location input with geolocation
function setupLocationInput(input) {
    // Add geolocation button
    const wrapper = input.parentElement;
    const geoButton = document.createElement('button');
    geoButton.type = 'button';
    geoButton.className = 'geo-button';
    geoButton.innerHTML = '<i class="fas fa-location-arrow"></i>';
    geoButton.title = 'Use my location';
    wrapper.appendChild(geoButton);
    
    geoButton.addEventListener('click', function() {
        if (navigator.geolocation) {
            geoButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
            navigator.geolocation.getCurrentPosition(
                function(position) {
                    reverseGeocode(position.coords.latitude, position.coords.longitude)
                        .then(location => {
                            input.value = location;
                            geoButton.innerHTML = '<i class="fas fa-location-arrow"></i>';
                        })
                        .catch(() => {
                            geoButton.innerHTML = '<i class="fas fa-location-arrow"></i>';
                            showNotification('Unable to get your location', 'error');
                        });
                },
                function() {
                    geoButton.innerHTML = '<i class="fas fa-location-arrow"></i>';
                    showNotification('Location access denied', 'error');
                }
            );
        } else {
            showNotification('Geolocation not supported', 'error');
        }
    });
}

// Reverse geocoding (mock implementation)
async function reverseGeocode(lat, lng) {
    // In production, use a real geocoding service like Google Maps API
    return new Promise((resolve) => {
        setTimeout(() => {
            resolve('Your Location'); // Mock response
        }, 1000);
    });
}

// Handle search submission
function handleSearch() {
    const serviceInput = document.getElementById('service-input');
    const locationInput = document.getElementById('location-input');
    
    const service = serviceInput?.value.trim();
    const location = locationInput?.value.trim();
    
    if (!service || !location) {
        showNotification('Please enter both service and location', 'error');
        return;
    }
    
    // Track search event
    trackEvent('search', {
        service: service,
        location: location
    });
    
    // Redirect to search results
    const params = new URLSearchParams({
        service: service,
        location: location
    });
    
    window.location.href = `/search?${params.toString()}`;
}

// Contact button functionality
function initContactButtons() {
    const contactButtons = document.querySelectorAll('.btn-contact');
    
    contactButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const plumberCard = this.closest('.plumber-card');
            const plumberName = plumberCard?.querySelector('h3')?.textContent || 'Plumber';
            
            // Track contact event
            trackEvent('contact_plumber', {
                plumber_name: plumberName
            });
            
            // Show contact modal or redirect
            showContactModal(plumberName);
        });
    });
}

// Contact modal
function showContactModal(plumberName) {
    const modal = document.createElement('div');
    modal.className = 'modal-overlay';
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>Contact ${plumberName}</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="contact-form">
                    <div class="form-group">
                        <label for="contact-name">Your Name</label>
                        <input type="text" id="contact-name" required>
                    </div>
                    <div class="form-group">
                        <label for="contact-phone">Phone Number</label>
                        <input type="tel" id="contact-phone" required>
                    </div>
                    <div class="form-group">
                        <label for="contact-email">Email</label>
                        <input type="email" id="contact-email" required>
                    </div>
                    <div class="form-group">
                        <label for="contact-message">Describe your plumbing issue</label>
                        <textarea id="contact-message" rows="4" required></textarea>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" required>
                            I agree to be contacted by ${plumberName}
                        </label>
                    </div>
                    <button type="submit" class="btn btn-primary">Send Message</button>
                </form>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    document.body.classList.add('modal-open');
    
    // Close modal functionality
    const closeBtn = modal.querySelector('.modal-close');
    closeBtn.addEventListener('click', closeModal);
    
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
    
    // Form submission
    const form = modal.querySelector('#contact-form');
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        handleContactSubmission(plumberName);
    });
    
    function closeModal() {
        document.body.removeChild(modal);
        document.body.classList.remove('modal-open');
    }
}

// Handle contact form submission
function handleContactSubmission(plumberName) {
    const form = document.getElementById('contact-form');
    const formData = new FormData(form);
    
    // Show loading state
    const submitBtn = form.querySelector('button[type="submit"]');
    const originalText = submitBtn.textContent;
    submitBtn.textContent = 'Sending...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Track conversion
        trackEvent('lead_generated', {
            plumber_name: plumberName
        });
        
        showNotification('Your message has been sent! The plumber will contact you soon.', 'success');
        
        // Close modal
        const modal = document.querySelector('.modal-overlay');
        if (modal) {
            document.body.removeChild(modal);
            document.body.classList.remove('modal-open');
        }
    }, 2000);
}

// Scroll effects
function initScrollEffects() {
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);
    
    // Observe elements for animation
    document.querySelectorAll('.service-card, .plumber-card, .step, .trust-item').forEach(el => {
        observer.observe(el);
    });
}

// Lazy loading for images
function initLazyLoading() {
    const images = document.querySelectorAll('img[loading="lazy"]');
    
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src || img.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    }
}

// Analytics and tracking
function initAnalytics() {
    // Track page view
    trackEvent('page_view', {
        page: window.location.pathname,
        title: document.title
    });
    
    // Track scroll depth
    let maxScroll = 0;
    window.addEventListener('scroll', throttle(function() {
        const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);
        if (scrollPercent > maxScroll) {
            maxScroll = scrollPercent;
            if (maxScroll % 25 === 0) { // Track at 25%, 50%, 75%, 100%
                trackEvent('scroll_depth', { percent: maxScroll });
            }
        }
    }, 1000));
}

// Event tracking function
function trackEvent(eventName, properties = {}) {
    // In production, integrate with Google Analytics, Mixpanel, etc.
    console.log('Event tracked:', eventName, properties);
    
    // Example Google Analytics 4 tracking
    if (typeof gtag !== 'undefined') {
        gtag('event', eventName, properties);
    }
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-message">${message}</span>
            <button class="notification-close">&times;</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.classList.add('notification-exit');
            setTimeout(() => {
                if (notification.parentElement) {
                    document.body.removeChild(notification);
                }
            }, 300);
        }
    }, 5000);
    
    // Close button functionality
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', function() {
        notification.classList.add('notification-exit');
        setTimeout(() => {
            if (notification.parentElement) {
                document.body.removeChild(notification);
            }
        }, 300);
    });
}

// Utility functions
function throttle(func, limit) {
    let inThrottle;
    return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
            func.apply(context, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
