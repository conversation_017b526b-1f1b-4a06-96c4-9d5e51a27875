// Geolocation and location services for PlumberPro

class LocationService {
    constructor() {
        this.currentLocation = null;
        this.locationCache = new Map();
        this.init();
    }
    
    init() {
        this.setupLocationInputs();
        this.loadSavedLocation();
    }
    
    // Setup location inputs with autocomplete and geolocation
    setupLocationInputs() {
        const locationInputs = document.querySelectorAll('#location-input, #location-search');
        
        locationInputs.forEach(input => {
            this.setupLocationAutocomplete(input);
            this.addGeolocationButton(input);
        });
    }
    
    // Add geolocation button to location inputs
    addGeolocationButton(input) {
        const wrapper = input.parentElement;
        if (wrapper.querySelector('.geo-button')) return; // Already exists
        
        const geoButton = document.createElement('button');
        geoButton.type = 'button';
        geoButton.className = 'geo-button';
        geoButton.innerHTML = '<i class="fas fa-location-arrow"></i>';
        geoButton.title = 'Use my current location';
        geoButton.setAttribute('aria-label', 'Use my current location');
        
        wrapper.appendChild(geoButton);
        
        geoButton.addEventListener('click', () => {
            this.getCurrentLocation(input);
        });
    }
    
    // Get current location using browser geolocation
    getCurrentLocation(input) {
        const geoButton = input.parentElement.querySelector('.geo-button');
        
        if (!navigator.geolocation) {
            this.showLocationError('Geolocation is not supported by this browser');
            return;
        }
        
        // Show loading state
        geoButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        geoButton.disabled = true;
        
        const options = {
            enableHighAccuracy: true,
            timeout: 10000,
            maximumAge: 300000 // 5 minutes
        };
        
        navigator.geolocation.getCurrentPosition(
            (position) => this.onLocationSuccess(position, input, geoButton),
            (error) => this.onLocationError(error, geoButton),
            options
        );
    }
    
    // Handle successful location retrieval
    async onLocationSuccess(position, input, geoButton) {
        try {
            const { latitude, longitude } = position.coords;
            this.currentLocation = { lat: latitude, lng: longitude };
            
            // Reverse geocode to get address
            const address = await this.reverseGeocode(latitude, longitude);
            
            if (address) {
                input.value = address;
                this.saveLocation(address, latitude, longitude);
                
                // Trigger input event to update suggestions
                input.dispatchEvent(new Event('input', { bubbles: true }));
                
                // Track location usage
                this.trackLocationUsage('geolocation_success');
            } else {
                throw new Error('Unable to determine address');
            }
        } catch (error) {
            console.error('Location processing error:', error);
            this.showLocationError('Unable to determine your location');
        } finally {
            // Reset button state
            geoButton.innerHTML = '<i class="fas fa-location-arrow"></i>';
            geoButton.disabled = false;
        }
    }
    
    // Handle location errors
    onLocationError(error, geoButton) {
        let message = 'Unable to get your location';
        
        switch (error.code) {
            case error.PERMISSION_DENIED:
                message = 'Location access denied. Please enable location services.';
                break;
            case error.POSITION_UNAVAILABLE:
                message = 'Location information unavailable.';
                break;
            case error.TIMEOUT:
                message = 'Location request timed out.';
                break;
        }
        
        this.showLocationError(message);
        this.trackLocationUsage('geolocation_error', { error: error.code });
        
        // Reset button state
        geoButton.innerHTML = '<i class="fas fa-location-arrow"></i>';
        geoButton.disabled = false;
    }
    
    // Reverse geocode coordinates to address
    async reverseGeocode(lat, lng) {
        const cacheKey = `${lat.toFixed(4)},${lng.toFixed(4)}`;
        
        // Check cache first
        if (this.locationCache.has(cacheKey)) {
            return this.locationCache.get(cacheKey);
        }
        
        try {
            // In production, use a real geocoding service
            // This is a mock implementation
            const address = await this.mockReverseGeocode(lat, lng);
            
            // Cache the result
            this.locationCache.set(cacheKey, address);
            
            return address;
        } catch (error) {
            console.error('Reverse geocoding error:', error);
            return null;
        }
    }
    
    // Mock reverse geocoding (replace with real service in production)
    async mockReverseGeocode(lat, lng) {
        // Simulate API delay
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // Mock city data based on coordinates
        const cities = [
            { name: 'Dallas, TX', lat: 32.7767, lng: -96.7970 },
            { name: 'Houston, TX', lat: 29.7604, lng: -95.3698 },
            { name: 'Austin, TX', lat: 30.2672, lng: -97.7431 },
            { name: 'Phoenix, AZ', lat: 33.4484, lng: -112.0740 },
            { name: 'San Antonio, TX', lat: 29.4241, lng: -98.4936 }
        ];
        
        // Find closest city
        let closestCity = cities[0];
        let minDistance = this.calculateDistance(lat, lng, closestCity.lat, closestCity.lng);
        
        for (const city of cities) {
            const distance = this.calculateDistance(lat, lng, city.lat, city.lng);
            if (distance < minDistance) {
                minDistance = distance;
                closestCity = city;
            }
        }
        
        return closestCity.name;
    }
    
    // Calculate distance between two coordinates
    calculateDistance(lat1, lng1, lat2, lng2) {
        const R = 6371; // Earth's radius in kilometers
        const dLat = this.toRadians(lat2 - lat1);
        const dLng = this.toRadians(lng2 - lng1);
        
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                  Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                  Math.sin(dLng / 2) * Math.sin(dLng / 2);
        
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
    
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
    
    // Setup location autocomplete
    setupLocationAutocomplete(input) {
        const suggestions = [
            'Dallas, TX',
            'Houston, TX',
            'Austin, TX',
            'San Antonio, TX',
            'Fort Worth, TX',
            'Phoenix, AZ',
            'Tucson, AZ',
            'Mesa, AZ',
            'Chandler, AZ',
            'Scottsdale, AZ',
            'Atlanta, GA',
            'Denver, CO',
            'Las Vegas, NV',
            'Miami, FL',
            'Orlando, FL'
        ];
        
        let suggestionsContainer = input.parentElement.querySelector('.location-suggestions');
        
        if (!suggestionsContainer) {
            suggestionsContainer = document.createElement('div');
            suggestionsContainer.className = 'location-suggestions';
            input.parentElement.appendChild(suggestionsContainer);
        }
        
        input.addEventListener('input', (e) => {
            const value = e.target.value.toLowerCase().trim();
            suggestionsContainer.innerHTML = '';
            
            if (value.length < 2) {
                suggestionsContainer.style.display = 'none';
                return;
            }
            
            const filtered = suggestions.filter(location =>
                location.toLowerCase().includes(value)
            ).slice(0, 5);
            
            if (filtered.length > 0) {
                suggestionsContainer.style.display = 'block';
                
                filtered.forEach(location => {
                    const div = document.createElement('div');
                    div.className = 'suggestion-item';
                    div.innerHTML = `
                        <i class="fas fa-map-marker-alt"></i>
                        <span>${location}</span>
                    `;
                    
                    div.addEventListener('click', () => {
                        input.value = location;
                        suggestionsContainer.style.display = 'none';
                        this.saveRecentLocation(location);
                        this.trackLocationUsage('autocomplete_selected');
                    });
                    
                    suggestionsContainer.appendChild(div);
                });
            } else {
                suggestionsContainer.style.display = 'none';
            }
        });
        
        // Hide suggestions when clicking outside
        document.addEventListener('click', (e) => {
            if (!input.contains(e.target) && !suggestionsContainer.contains(e.target)) {
                suggestionsContainer.style.display = 'none';
            }
        });
        
        // Handle keyboard navigation
        input.addEventListener('keydown', (e) => {
            const items = suggestionsContainer.querySelectorAll('.suggestion-item');
            let activeIndex = Array.from(items).findIndex(item => item.classList.contains('active'));
            
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    activeIndex = Math.min(activeIndex + 1, items.length - 1);
                    this.updateActiveSuggestion(items, activeIndex);
                    break;
                case 'ArrowUp':
                    e.preventDefault();
                    activeIndex = Math.max(activeIndex - 1, 0);
                    this.updateActiveSuggestion(items, activeIndex);
                    break;
                case 'Enter':
                    if (activeIndex >= 0 && items[activeIndex]) {
                        e.preventDefault();
                        items[activeIndex].click();
                    }
                    break;
                case 'Escape':
                    suggestionsContainer.style.display = 'none';
                    break;
            }
        });
    }
    
    updateActiveSuggestion(items, activeIndex) {
        items.forEach((item, index) => {
            item.classList.toggle('active', index === activeIndex);
        });
    }
    
    // Save location to localStorage
    saveLocation(address, lat, lng) {
        const locationData = {
            address,
            coordinates: { lat, lng },
            timestamp: Date.now()
        };
        
        localStorage.setItem('plumberpro_location', JSON.stringify(locationData));
    }
    
    // Save recent location searches
    saveRecentLocation(location) {
        let recent = JSON.parse(localStorage.getItem('plumberpro_recent_locations') || '[]');
        
        // Remove if already exists
        recent = recent.filter(loc => loc !== location);
        
        // Add to beginning
        recent.unshift(location);
        
        // Keep only last 5
        recent = recent.slice(0, 5);
        
        localStorage.setItem('plumberpro_recent_locations', JSON.stringify(recent));
    }
    
    // Load saved location
    loadSavedLocation() {
        const saved = localStorage.getItem('plumberpro_location');
        if (saved) {
            try {
                const locationData = JSON.parse(saved);
                
                // Check if location is less than 24 hours old
                if (Date.now() - locationData.timestamp < 24 * 60 * 60 * 1000) {
                    this.currentLocation = locationData.coordinates;
                    
                    // Optionally pre-fill location inputs
                    const locationInputs = document.querySelectorAll('#location-input, #location-search');
                    locationInputs.forEach(input => {
                        if (!input.value) {
                            input.value = locationData.address;
                        }
                    });
                }
            } catch (error) {
                console.error('Error loading saved location:', error);
            }
        }
    }
    
    // Show location error message
    showLocationError(message) {
        // Use the notification system from main.js if available
        if (typeof showNotification === 'function') {
            showNotification(message, 'error');
        } else {
            alert(message);
        }
    }
    
    // Track location usage for analytics
    trackLocationUsage(action, properties = {}) {
        if (typeof trackEvent === 'function') {
            trackEvent('location_service', {
                action,
                ...properties
            });
        }
    }
    
    // Get current location coordinates
    getCurrentCoordinates() {
        return this.currentLocation;
    }
    
    // Check if location services are available
    isLocationAvailable() {
        return 'geolocation' in navigator;
    }
    
    // Clear saved location data
    clearLocationData() {
        localStorage.removeItem('plumberpro_location');
        localStorage.removeItem('plumberpro_recent_locations');
        this.currentLocation = null;
        this.locationCache.clear();
    }
}

// Initialize location service when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.locationService = new LocationService();
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = LocationService;
}
