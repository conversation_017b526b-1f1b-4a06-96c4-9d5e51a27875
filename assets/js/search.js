// Advanced search functionality for PlumberPro

class PlumberSearch {
    constructor() {
        this.searchData = [];
        this.filters = {
            service: '',
            location: '',
            rating: 0,
            priceRange: [0, 1000],
            availability: '',
            experience: 0,
            verified: false
        };
        this.sortBy = 'rating';
        this.currentPage = 1;
        this.itemsPerPage = 12;
        
        this.init();
    }
    
    init() {
        this.loadSearchData();
        this.setupEventListeners();
        this.performSearch();
    }
    
    // Load plumber data (in production, this would come from an API)
    loadSearchData() {
        this.searchData = [
            {
                id: 1,
                name: "Mike's Plumbing Services",
                rating: 4.9,
                reviews: 127,
                hourlyRate: 120,
                location: "Dallas, TX",
                distance: 2.3,
                services: ["Emergency Plumbing", "Drain Cleaning", "Water Heater Repair"],
                experience: 15,
                verified: true,
                availability: "24/7",
                image: "assets/images/plumber-1.jpg",
                phone: "(*************",
                description: "Professional plumbing services with 15+ years of experience. Specializing in emergency repairs and water heater installations.",
                certifications: ["Licensed", "Insured", "Bonded"],
                responseTime: "< 1 hour"
            },
            {
                id: 2,
                name: "Rodriguez Plumbing Co.",
                rating: 4.8,
                reviews: 89,
                hourlyRate: 110,
                location: "Houston, TX",
                distance: 5.7,
                services: ["Pipe Repair", "Bathroom Remodeling", "Leak Detection"],
                experience: 12,
                verified: true,
                availability: "Mon-Sat 7AM-8PM",
                image: "assets/images/plumber-2.jpg",
                phone: "(*************",
                description: "Family-owned plumbing business serving Houston area. Expert in residential and commercial plumbing solutions.",
                certifications: ["Licensed", "Insured"],
                responseTime: "< 2 hours"
            },
            {
                id: 3,
                name: "Elite Drain Solutions",
                rating: 4.7,
                reviews: 156,
                hourlyRate: 100,
                location: "Phoenix, AZ",
                distance: 3.1,
                services: ["Drain Cleaning", "Sewer Repair", "Hydro Jetting"],
                experience: 8,
                verified: true,
                availability: "24/7",
                image: "assets/images/plumber-3.jpg",
                phone: "(*************",
                description: "Specialized drain and sewer experts. Advanced equipment for tough clogs and pipe issues.",
                certifications: ["Licensed", "Insured", "EPA Certified"],
                responseTime: "< 30 minutes"
            },
            {
                id: 4,
                name: "Quick Fix Plumbing",
                rating: 4.6,
                reviews: 203,
                hourlyRate: 95,
                location: "Dallas, TX",
                distance: 1.8,
                services: ["Emergency Plumbing", "Toilet Repair", "Faucet Repair"],
                experience: 10,
                verified: true,
                availability: "24/7",
                image: "assets/images/plumber-4.jpg",
                phone: "(*************",
                description: "Fast, reliable plumbing repairs. Same-day service guaranteed for most issues.",
                certifications: ["Licensed", "Insured"],
                responseTime: "< 45 minutes"
            },
            {
                id: 5,
                name: "Pro Water Solutions",
                rating: 4.5,
                reviews: 78,
                hourlyRate: 130,
                location: "Austin, TX",
                distance: 8.2,
                services: ["Water Heater Repair", "Pipe Installation", "Water Line Repair"],
                experience: 20,
                verified: true,
                availability: "Mon-Fri 8AM-6PM",
                image: "assets/images/plumber-5.jpg",
                phone: "(*************",
                description: "Water system specialists with two decades of experience. Premium service and warranties.",
                certifications: ["Licensed", "Insured", "Bonded", "Master Plumber"],
                responseTime: "< 3 hours"
            }
        ];
    }
    
    setupEventListeners() {
        // Search form
        const searchForm = document.getElementById('search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSearch();
            });
        }
        
        // Filter controls
        this.setupFilterListeners();
        
        // Sort controls
        const sortSelect = document.getElementById('sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortBy = e.target.value;
                this.performSearch();
            });
        }
        
        // Pagination
        this.setupPaginationListeners();
    }
    
    setupFilterListeners() {
        // Rating filter
        const ratingInputs = document.querySelectorAll('input[name="rating"]');
        ratingInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.filters.rating = parseFloat(e.target.value);
                this.performSearch();
            });
        });
        
        // Price range filter
        const priceRange = document.getElementById('price-range');
        if (priceRange) {
            priceRange.addEventListener('input', (e) => {
                this.filters.priceRange = [0, parseInt(e.target.value)];
                this.updatePriceDisplay(e.target.value);
                this.performSearch();
            });
        }
        
        // Availability filter
        const availabilitySelect = document.getElementById('availability-filter');
        if (availabilitySelect) {
            availabilitySelect.addEventListener('change', (e) => {
                this.filters.availability = e.target.value;
                this.performSearch();
            });
        }
        
        // Experience filter
        const experienceSelect = document.getElementById('experience-filter');
        if (experienceSelect) {
            experienceSelect.addEventListener('change', (e) => {
                this.filters.experience = parseInt(e.target.value);
                this.performSearch();
            });
        }
        
        // Verified filter
        const verifiedCheckbox = document.getElementById('verified-only');
        if (verifiedCheckbox) {
            verifiedCheckbox.addEventListener('change', (e) => {
                this.filters.verified = e.target.checked;
                this.performSearch();
            });
        }
        
        // Clear filters
        const clearFiltersBtn = document.getElementById('clear-filters');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }
    }
    
    setupPaginationListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('page-btn')) {
                e.preventDefault();
                const page = parseInt(e.target.dataset.page);
                if (page !== this.currentPage) {
                    this.currentPage = page;
                    this.performSearch();
                    this.scrollToResults();
                }
            }
        });
    }
    
    handleSearch() {
        const serviceInput = document.getElementById('service-search');
        const locationInput = document.getElementById('location-search');
        
        if (serviceInput) this.filters.service = serviceInput.value.trim();
        if (locationInput) this.filters.location = locationInput.value.trim();
        
        this.currentPage = 1;
        this.performSearch();
    }
    
    performSearch() {
        let results = [...this.searchData];
        
        // Apply filters
        results = this.applyFilters(results);
        
        // Sort results
        results = this.sortResults(results);
        
        // Paginate results
        const paginatedResults = this.paginateResults(results);
        
        // Display results
        this.displayResults(paginatedResults, results.length);
        
        // Update URL
        this.updateURL();
        
        // Track search
        this.trackSearch(results.length);
    }
    
    applyFilters(results) {
        return results.filter(plumber => {
            // Service filter
            if (this.filters.service && !plumber.services.some(service => 
                service.toLowerCase().includes(this.filters.service.toLowerCase())
            )) {
                return false;
            }
            
            // Location filter
            if (this.filters.location && !plumber.location.toLowerCase().includes(
                this.filters.location.toLowerCase()
            )) {
                return false;
            }
            
            // Rating filter
            if (this.filters.rating > 0 && plumber.rating < this.filters.rating) {
                return false;
            }
            
            // Price range filter
            if (plumber.hourlyRate > this.filters.priceRange[1]) {
                return false;
            }
            
            // Availability filter
            if (this.filters.availability) {
                if (this.filters.availability === '24/7' && plumber.availability !== '24/7') {
                    return false;
                }
            }
            
            // Experience filter
            if (this.filters.experience > 0 && plumber.experience < this.filters.experience) {
                return false;
            }
            
            // Verified filter
            if (this.filters.verified && !plumber.verified) {
                return false;
            }
            
            return true;
        });
    }
    
    sortResults(results) {
        return results.sort((a, b) => {
            switch (this.sortBy) {
                case 'rating':
                    return b.rating - a.rating;
                case 'price-low':
                    return a.hourlyRate - b.hourlyRate;
                case 'price-high':
                    return b.hourlyRate - a.hourlyRate;
                case 'distance':
                    return a.distance - b.distance;
                case 'reviews':
                    return b.reviews - a.reviews;
                case 'experience':
                    return b.experience - a.experience;
                default:
                    return b.rating - a.rating;
            }
        });
    }
    
    paginateResults(results) {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        return results.slice(startIndex, endIndex);
    }
    
    displayResults(results, totalCount) {
        const container = document.getElementById('search-results');
        const countElement = document.getElementById('results-count');
        
        if (countElement) {
            countElement.textContent = `${totalCount} plumbers found`;
        }
        
        if (!container) return;
        
        if (results.length === 0) {
            container.innerHTML = this.getNoResultsHTML();
            return;
        }
        
        container.innerHTML = results.map(plumber => this.getPlumberCardHTML(plumber)).join('');
        
        // Update pagination
        this.updatePagination(totalCount);
        
        // Reinitialize contact buttons
        this.initContactButtons();
    }
    
    getPlumberCardHTML(plumber) {
        return `
            <div class="plumber-card" data-plumber-id="${plumber.id}">
                <div class="plumber-image">
                    <img src="${plumber.image}" alt="${plumber.name}" loading="lazy">
                    ${plumber.verified ? '<div class="verified-badge"><i class="fas fa-check-circle"></i> Verified</div>' : ''}
                </div>
                <div class="plumber-info">
                    <h3>${plumber.name}</h3>
                    <div class="rating">
                        <span class="stars">${this.getStarsHTML(plumber.rating)}</span>
                        <span class="rating-text">${plumber.rating} (${plumber.reviews} reviews)</span>
                    </div>
                    <p class="specialties">${plumber.services.join(', ')}</p>
                    <div class="plumber-details">
                        <span class="experience"><i class="fas fa-clock"></i> ${plumber.experience}+ years</span>
                        <span class="location"><i class="fas fa-map-marker-alt"></i> ${plumber.location}</span>
                        <span class="distance"><i class="fas fa-route"></i> ${plumber.distance} miles</span>
                    </div>
                    <div class="plumber-meta">
                        <div class="pricing">$${plumber.hourlyRate}/hour</div>
                        <div class="response-time">${plumber.responseTime}</div>
                        <div class="availability">${plumber.availability}</div>
                    </div>
                    <div class="certifications">
                        ${plumber.certifications.map(cert => `<span class="cert-badge">${cert}</span>`).join('')}
                    </div>
                    <div class="plumber-actions">
                        <button class="btn btn-outline btn-call" data-phone="${plumber.phone}">
                            <i class="fas fa-phone"></i> Call Now
                        </button>
                        <button class="btn btn-primary btn-contact" data-plumber-id="${plumber.id}">
                            Get Quote
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    getStarsHTML(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        let starsHTML = '';
        
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '★';
        }
        
        if (hasHalfStar) {
            starsHTML += '☆';
        }
        
        return starsHTML;
    }
    
    getNoResultsHTML() {
        return `
            <div class="no-results">
                <div class="no-results-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3>No plumbers found</h3>
                <p>Try adjusting your search criteria or location.</p>
                <button class="btn btn-primary" onclick="plumberSearch.clearFilters()">
                    Clear Filters
                </button>
            </div>
        `;
    }
    
    updatePagination(totalCount) {
        const totalPages = Math.ceil(totalCount / this.itemsPerPage);
        const paginationContainer = document.getElementById('pagination');
        
        if (!paginationContainer || totalPages <= 1) {
            if (paginationContainer) paginationContainer.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage - 1}">Previous</button>`;
        }
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="page-btn active" data-page="${i}">${i}</button>`;
            } else if (i === 1 || i === totalPages || Math.abs(i - this.currentPage) <= 2) {
                paginationHTML += `<button class="page-btn" data-page="${i}">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += `<span class="page-ellipsis">...</span>`;
            }
        }
        
        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage + 1}">Next</button>`;
        }
        
        paginationContainer.innerHTML = paginationHTML;
    }
    
    initContactButtons() {
        // Call buttons
        document.querySelectorAll('.btn-call').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const phone = e.target.closest('.btn-call').dataset.phone;
                window.location.href = `tel:${phone}`;
                this.trackEvent('phone_call', { phone });
            });
        });
        
        // Contact buttons
        document.querySelectorAll('.btn-contact').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const plumberId = e.target.closest('.btn-contact').dataset.plumberId;
                const plumber = this.searchData.find(p => p.id == plumberId);
                if (plumber) {
                    this.showContactModal(plumber);
                }
            });
        });
    }
    
    showContactModal(plumber) {
        // Implementation would be similar to main.js but with plumber-specific data
        console.log('Show contact modal for:', plumber.name);
    }
    
    clearFilters() {
        this.filters = {
            service: '',
            location: '',
            rating: 0,
            priceRange: [0, 1000],
            availability: '',
            experience: 0,
            verified: false
        };
        
        // Reset form elements
        const form = document.getElementById('filters-form');
        if (form) form.reset();
        
        this.currentPage = 1;
        this.performSearch();
    }
    
    updatePriceDisplay(value) {
        const display = document.getElementById('price-display');
        if (display) {
            display.textContent = `Up to $${value}/hour`;
        }
    }
    
    updateURL() {
        const params = new URLSearchParams();
        
        Object.keys(this.filters).forEach(key => {
            const value = this.filters[key];
            if (value && value !== '' && value !== 0 && value !== false) {
                if (Array.isArray(value)) {
                    params.set(key, value.join(','));
                } else {
                    params.set(key, value.toString());
                }
            }
        });
        
        if (this.sortBy !== 'rating') {
            params.set('sort', this.sortBy);
        }
        
        if (this.currentPage > 1) {
            params.set('page', this.currentPage.toString());
        }
        
        const newURL = `${window.location.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newURL);
    }
    
    scrollToResults() {
        const resultsContainer = document.getElementById('search-results');
        if (resultsContainer) {
            resultsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
    
    trackSearch(resultCount) {
        if (typeof trackEvent === 'function') {
            trackEvent('search_performed', {
                filters: this.filters,
                sort: this.sortBy,
                result_count: resultCount
            });
        }
    }
    
    trackEvent(eventName, properties) {
        if (typeof trackEvent === 'function') {
            trackEvent(eventName, properties);
        }
    }
}

// Initialize search functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('search-results')) {
        window.plumberSearch = new PlumberSearch();
    }
});
