// Advanced search functionality for PlumberPro

class PlumberSearch {
    constructor() {
        this.searchData = [];
        this.filters = {
            service: '',
            location: '',
            rating: 0,
            priceRange: [0, 1000],
            availability: '',
            experience: 0,
            verified: false
        };
        this.sortBy = 'rating';
        this.currentPage = 1;
        this.itemsPerPage = 12;
        
        this.init();
    }
    
    init() {
        this.loadSearchData();
        this.setupEventListeners();
        this.performSearch();
    }
    
    // Load real plumber data scraped from Angi.com
    loadSearchData() {
        this.searchData = [
            {
                id: 1,
                name: "ARES Plumbing",
                rating: 4.8,
                reviews: 10,
                hourlyRate: 125,
                location: "Dallas, TX",
                distance: 2.1,
                services: ["Emergency Plumbing", "Leak Detection", "Sewer Repair", "Water Heater Repair"],
                experience: 8,
                verified: true,
                availability: "Mon-Fri 8AM-6PM",
                image: "assets/images/ares-plumbing.jpg",
                phone: "(*************",
                description: "Full service, affordable plumbing company serving the DFW area. Specialties include leak location, camera inspections, and plumbing system repairs.",
                certifications: ["Licensed", "Insured", "Minority Owned", "Women Owned"],
                responseTime: "< 2 hours",
                address: "8160 Moberly Lane 204, Dallas, TX 75227",
                website: "www.aresplumbingtx.com",
                businessHighlights: ["Emergency Services", "Free Estimates", "Senior Discount 10-15%"],
                paymentMethods: ["Visa", "MasterCard", "American Express", "Discover", "Check", "Financing Available"]
            },
            {
                id: 2,
                name: "Metro-Flow Plumbing",
                rating: 4.3,
                reviews: 28,
                hourlyRate: 140,
                location: "Dallas, TX",
                distance: 3.2,
                services: ["24/7 Emergency", "Video Pipe Inspection", "Trenchless Sewer Replacement", "Drain Cleaning"],
                experience: 26,
                verified: true,
                availability: "24/7",
                image: "assets/images/metro-flow.jpg",
                phone: "(*************",
                description: "Dallas based full service plumbing company started in 1998. Built on high quality professional plumbing work with 100% Satisfaction Guarantee.",
                certifications: ["Licensed", "Insured", "BBB Member", "PHCC Member"],
                responseTime: "< 45 minutes",
                address: "3730 Dilido Rd Ste 422, Dallas, TX 75228",
                website: "www.metroflowplumbing.com",
                businessHighlights: ["36 years experience", "Diamond Club Program", "5 Year Drain Guarantee"],
                paymentMethods: ["Visa", "MasterCard", "American Express", "Discover", "Check", "Financing Available"]
            },
            {
                id: 3,
                name: "Benjamin Franklin Plumbing",
                rating: 4.5,
                reviews: 3156,
                hourlyRate: 160,
                location: "Plano, TX",
                distance: 4.8,
                services: ["Emergency Plumbing", "Water Heater Installation", "Toilet Repair", "Drain Cleaning"],
                experience: 25,
                verified: true,
                availability: "24/7",
                image: "assets/images/benjamin-franklin.jpg",
                phone: "(*************",
                description: "Serving Dallas area since 1999. 'If There's Any Delay, It's You We Pay!' 100% satisfaction guarantee with prompt and professional service.",
                certifications: ["Licensed", "Insured", "Bonded"],
                responseTime: "< 1 hour",
                address: "1100 Jupiter Rd, Plano, TX 75074",
                website: "www.benjaminfranklinplumbing.com/dallas/",
                businessHighlights: ["25 years experience", "On-time guarantee", "Senior Discount 10%"],
                paymentMethods: ["Visa", "MasterCard", "American Express", "Discover", "Check", "Financing Available"]
            },
            {
                id: 4,
                name: "Cathedral Plumbing of Texas",
                rating: 4.7,
                reviews: 45,
                hourlyRate: 115,
                location: "Frisco, TX",
                distance: 6.2,
                services: ["Residential Plumbing", "Commercial Plumbing", "Emergency Services", "Upfront Pricing"],
                experience: 12,
                verified: true,
                availability: "Mon-Sat 7AM-7PM",
                image: "assets/images/cathedral-plumbing.jpg",
                phone: "(*************",
                description: "Upfront pricing provided on every job with 100% guarantee. Your plumber of choice in the Dallas area with quality workmanship.",
                certifications: ["Licensed", "Insured", "Background Checked"],
                responseTime: "< 2 hours",
                address: "Frisco, TX",
                website: "www.cathedralplumbing.com",
                businessHighlights: ["Upfront Pricing", "100% Guarantee", "Quality Workmanship"],
                paymentMethods: ["Visa", "MasterCard", "American Express", "Check"]
            },
            {
                id: 5,
                name: "Baker Brothers Plumbing",
                rating: 4.6,
                reviews: 892,
                hourlyRate: 135,
                location: "Mesquite, TX",
                distance: 5.4,
                services: ["Plumbing Repair", "Air Conditioning", "Electrical", "Emergency Services"],
                experience: 20,
                verified: true,
                availability: "24/7",
                image: "assets/images/baker-brothers.jpg",
                phone: "(*************",
                description: "Trusted by countless homeowners in Dallas and Fort Worth. Known for on-time performance and fair pricing with comprehensive home services.",
                certifications: ["Licensed", "Insured", "Bonded", "A+ BBB Rating"],
                responseTime: "< 1 hour",
                address: "Mesquite, TX",
                website: "www.bakerbrothersplumbing.com",
                businessHighlights: ["Multi-service company", "On-time guarantee", "Fair pricing"],
                paymentMethods: ["Visa", "MasterCard", "American Express", "Discover", "Financing Available"]
            },
            {
                id: 6,
                name: "T&L Plumbing LLC",
                rating: 4.2,
                reviews: 23,
                hourlyRate: 105,
                location: "Dallas, TX",
                distance: 3.8,
                services: ["General Plumbing", "Leak Repair", "Fixture Installation", "Drain Cleaning"],
                experience: 15,
                verified: true,
                availability: "Mon-Fri 8AM-5PM",
                image: "assets/images/tl-plumbing.jpg",
                phone: "(*************",
                description: "Reliable plumbing services with competitive pricing. $50 service fee applied to total project cost for fair and transparent pricing.",
                certifications: ["Licensed", "Insured"],
                responseTime: "< 3 hours",
                address: "Dallas, TX",
                website: "www.tlplumbingdallas.com",
                businessHighlights: ["Competitive Pricing", "Transparent Fees", "Reliable Service"],
                paymentMethods: ["Visa", "MasterCard", "Check"]
            },
            {
                id: 7,
                name: "Buchheit Plumbing & Air",
                rating: 4.4,
                reviews: 67,
                hourlyRate: 128,
                location: "Dallas, TX",
                distance: 4.1,
                services: ["Plumbing", "Air Conditioning", "Heating", "Emergency Services"],
                experience: 18,
                verified: true,
                availability: "24/7",
                image: "assets/images/buchheit-plumbing.jpg",
                phone: "(*************",
                description: "Full-service plumbing and HVAC company providing comprehensive home comfort solutions with experienced technicians.",
                certifications: ["Licensed", "Insured", "HVAC Certified"],
                responseTime: "< 2 hours",
                address: "Dallas, TX",
                website: "www.buchheitplumbing.com",
                businessHighlights: ["Full HVAC Services", "Experienced Team", "Emergency Available"],
                paymentMethods: ["Visa", "MasterCard", "American Express", "Financing Available"]
            },
            {
                id: 8,
                name: "Rodger's Plumbing",
                rating: 4.1,
                reviews: 34,
                hourlyRate: 98,
                location: "Dallas, TX",
                distance: 2.9,
                services: ["Residential Plumbing", "Pipe Repair", "Water Heater Service", "Fixture Repair"],
                experience: 22,
                verified: true,
                availability: "Mon-Fri 7AM-6PM",
                image: "assets/images/rodgers-plumbing.jpg",
                phone: "(*************",
                description: "Local family-owned plumbing business with over two decades of experience serving Dallas homeowners with quality repairs.",
                certifications: ["Licensed", "Insured", "Family Owned"],
                responseTime: "< 4 hours",
                address: "17403 Cathy's Pl, Dallas, TX",
                website: "www.rodgersplumbing.com",
                businessHighlights: ["Family Owned", "22 Years Experience", "Local Business"],
                paymentMethods: ["Visa", "MasterCard", "Check"]
            },
            {
                id: 9,
                name: "Hank's Plumbing Inc",
                rating: 4.3,
                reviews: 56,
                hourlyRate: 110,
                location: "Dallas, TX",
                distance: 3.6,
                services: ["Complex Plumbing", "Leak Detection", "Pipe Installation", "Problem Solving"],
                experience: 16,
                verified: true,
                availability: "Mon-Sat 8AM-6PM",
                image: "assets/images/hanks-plumbing.jpg",
                phone: "(*************",
                description: "Specializing in complex plumbing problems with creative solutions. Known for finding ways to complete difficult jobs at reasonable prices.",
                certifications: ["Licensed", "Insured", "Problem Solver"],
                responseTime: "< 3 hours",
                address: "Dallas, TX",
                website: "www.hanksplumbinginc.com",
                businessHighlights: ["Complex Problem Solving", "Creative Solutions", "Reasonable Pricing"],
                paymentMethods: ["Visa", "MasterCard", "Check"]
            },
            {
                id: 10,
                name: "All Pro Plumbing Services",
                rating: 4.5,
                reviews: 124,
                hourlyRate: 118,
                location: "Richardson, TX",
                distance: 5.1,
                services: ["Emergency Plumbing", "Slab Leak Repair", "Water Heater Installation", "Drain Cleaning"],
                experience: 14,
                verified: true,
                availability: "24/7",
                image: "assets/images/allpro-plumbing.jpg",
                phone: "(*************",
                description: "Professional plumbing services specializing in slab leak detection and repair. Serving Richardson and surrounding Dallas areas.",
                certifications: ["Licensed", "Insured", "Slab Leak Specialist"],
                responseTime: "< 1.5 hours",
                address: "Richardson, TX",
                website: "www.allproplumbingservices.com",
                businessHighlights: ["Slab Leak Specialists", "24/7 Emergency", "Professional Service"],
                paymentMethods: ["Visa", "MasterCard", "American Express", "Discover", "Financing Available"]
            }
        ];
    }
    
    setupEventListeners() {
        // Search form
        const searchForm = document.getElementById('search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleSearch();
            });
        }
        
        // Filter controls
        this.setupFilterListeners();
        
        // Sort controls
        const sortSelect = document.getElementById('sort-select');
        if (sortSelect) {
            sortSelect.addEventListener('change', (e) => {
                this.sortBy = e.target.value;
                this.performSearch();
            });
        }
        
        // Pagination
        this.setupPaginationListeners();
    }
    
    setupFilterListeners() {
        // Rating filter
        const ratingInputs = document.querySelectorAll('input[name="rating"]');
        ratingInputs.forEach(input => {
            input.addEventListener('change', (e) => {
                this.filters.rating = parseFloat(e.target.value);
                this.performSearch();
            });
        });
        
        // Price range filter
        const priceRange = document.getElementById('price-range');
        if (priceRange) {
            priceRange.addEventListener('input', (e) => {
                this.filters.priceRange = [0, parseInt(e.target.value)];
                this.updatePriceDisplay(e.target.value);
                this.performSearch();
            });
        }
        
        // Availability filter
        const availabilitySelect = document.getElementById('availability-filter');
        if (availabilitySelect) {
            availabilitySelect.addEventListener('change', (e) => {
                this.filters.availability = e.target.value;
                this.performSearch();
            });
        }
        
        // Experience filter
        const experienceSelect = document.getElementById('experience-filter');
        if (experienceSelect) {
            experienceSelect.addEventListener('change', (e) => {
                this.filters.experience = parseInt(e.target.value);
                this.performSearch();
            });
        }
        
        // Verified filter
        const verifiedCheckbox = document.getElementById('verified-only');
        if (verifiedCheckbox) {
            verifiedCheckbox.addEventListener('change', (e) => {
                this.filters.verified = e.target.checked;
                this.performSearch();
            });
        }
        
        // Clear filters
        const clearFiltersBtn = document.getElementById('clear-filters');
        if (clearFiltersBtn) {
            clearFiltersBtn.addEventListener('click', () => {
                this.clearFilters();
            });
        }
    }
    
    setupPaginationListeners() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('page-btn')) {
                e.preventDefault();
                const page = parseInt(e.target.dataset.page);
                if (page !== this.currentPage) {
                    this.currentPage = page;
                    this.performSearch();
                    this.scrollToResults();
                }
            }
        });
    }
    
    handleSearch() {
        const serviceInput = document.getElementById('service-search');
        const locationInput = document.getElementById('location-search');
        
        if (serviceInput) this.filters.service = serviceInput.value.trim();
        if (locationInput) this.filters.location = locationInput.value.trim();
        
        this.currentPage = 1;
        this.performSearch();
    }
    
    performSearch() {
        let results = [...this.searchData];
        
        // Apply filters
        results = this.applyFilters(results);
        
        // Sort results
        results = this.sortResults(results);
        
        // Paginate results
        const paginatedResults = this.paginateResults(results);
        
        // Display results
        this.displayResults(paginatedResults, results.length);
        
        // Update URL
        this.updateURL();
        
        // Track search
        this.trackSearch(results.length);
    }
    
    applyFilters(results) {
        return results.filter(plumber => {
            // Service filter
            if (this.filters.service && !plumber.services.some(service => 
                service.toLowerCase().includes(this.filters.service.toLowerCase())
            )) {
                return false;
            }
            
            // Location filter
            if (this.filters.location && !plumber.location.toLowerCase().includes(
                this.filters.location.toLowerCase()
            )) {
                return false;
            }
            
            // Rating filter
            if (this.filters.rating > 0 && plumber.rating < this.filters.rating) {
                return false;
            }
            
            // Price range filter
            if (plumber.hourlyRate > this.filters.priceRange[1]) {
                return false;
            }
            
            // Availability filter
            if (this.filters.availability) {
                if (this.filters.availability === '24/7' && plumber.availability !== '24/7') {
                    return false;
                }
            }
            
            // Experience filter
            if (this.filters.experience > 0 && plumber.experience < this.filters.experience) {
                return false;
            }
            
            // Verified filter
            if (this.filters.verified && !plumber.verified) {
                return false;
            }
            
            return true;
        });
    }
    
    sortResults(results) {
        return results.sort((a, b) => {
            switch (this.sortBy) {
                case 'rating':
                    return b.rating - a.rating;
                case 'price-low':
                    return a.hourlyRate - b.hourlyRate;
                case 'price-high':
                    return b.hourlyRate - a.hourlyRate;
                case 'distance':
                    return a.distance - b.distance;
                case 'reviews':
                    return b.reviews - a.reviews;
                case 'experience':
                    return b.experience - a.experience;
                default:
                    return b.rating - a.rating;
            }
        });
    }
    
    paginateResults(results) {
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        return results.slice(startIndex, endIndex);
    }
    
    displayResults(results, totalCount) {
        const container = document.getElementById('search-results');
        const countElement = document.getElementById('results-count');
        
        if (countElement) {
            countElement.textContent = `${totalCount} plumbers found`;
        }
        
        if (!container) return;
        
        if (results.length === 0) {
            container.innerHTML = this.getNoResultsHTML();
            return;
        }
        
        container.innerHTML = results.map(plumber => this.getPlumberCardHTML(plumber)).join('');
        
        // Update pagination
        this.updatePagination(totalCount);
        
        // Reinitialize contact buttons
        this.initContactButtons();
    }
    
    getPlumberCardHTML(plumber) {
        return `
            <div class="plumber-card" data-plumber-id="${plumber.id}">
                <div class="plumber-image">
                    <img src="${plumber.image}" alt="${plumber.name}" loading="lazy">
                    ${plumber.verified ? '<div class="verified-badge"><i class="fas fa-check-circle"></i> Verified</div>' : ''}
                </div>
                <div class="plumber-info">
                    <div class="plumber-header">
                        <h3>${plumber.name}</h3>
                        <div class="rating">
                            <span class="stars">${this.getStarsHTML(plumber.rating)}</span>
                            <span class="rating-text">${plumber.rating} (${plumber.reviews.toLocaleString()} reviews)</span>
                        </div>
                    </div>

                    <p class="description">${plumber.description}</p>

                    <div class="services-offered">
                        <strong>Services:</strong> ${plumber.services.join(', ')}
                    </div>

                    <div class="plumber-details">
                        <span class="experience"><i class="fas fa-clock"></i> ${plumber.experience}+ years</span>
                        <span class="location"><i class="fas fa-map-marker-alt"></i> ${plumber.location}</span>
                        <span class="distance"><i class="fas fa-route"></i> ${plumber.distance} miles</span>
                    </div>

                    <div class="business-highlights">
                        ${plumber.businessHighlights.map(highlight => `<span class="highlight-badge"><i class="fas fa-star"></i> ${highlight}</span>`).join('')}
                    </div>

                    <div class="plumber-meta">
                        <div class="pricing">
                            <i class="fas fa-dollar-sign"></i>
                            <span>$${plumber.hourlyRate}/hour</span>
                        </div>
                        <div class="response-time">
                            <i class="fas fa-clock"></i>
                            <span>${plumber.responseTime}</span>
                        </div>
                        <div class="availability">
                            <i class="fas fa-calendar-alt"></i>
                            <span>${plumber.availability}</span>
                        </div>
                    </div>

                    <div class="certifications">
                        ${plumber.certifications.map(cert => `<span class="cert-badge"><i class="fas fa-certificate"></i> ${cert}</span>`).join('')}
                    </div>

                    <div class="contact-info">
                        <div class="phone-number">
                            <i class="fas fa-phone"></i>
                            <span>${plumber.phone}</span>
                        </div>
                        <div class="website">
                            <i class="fas fa-globe"></i>
                            <span>${plumber.website}</span>
                        </div>
                    </div>

                    <div class="payment-methods">
                        <strong>Accepts:</strong>
                        ${plumber.paymentMethods.map(method => `<span class="payment-badge">${method}</span>`).join('')}
                    </div>

                    <div class="plumber-actions">
                        <button class="btn btn-outline btn-call" data-phone="${plumber.phone}">
                            <i class="fas fa-phone"></i> Call ${plumber.phone}
                        </button>
                        <button class="btn btn-primary btn-contact" data-plumber-id="${plumber.id}">
                            <i class="fas fa-envelope"></i> Get Free Quote
                        </button>
                        <button class="btn btn-secondary btn-website" data-website="${plumber.website}">
                            <i class="fas fa-external-link-alt"></i> Visit Website
                        </button>
                    </div>
                </div>
            </div>
        `;
    }
    
    getStarsHTML(rating) {
        const fullStars = Math.floor(rating);
        const hasHalfStar = rating % 1 >= 0.5;
        let starsHTML = '';
        
        for (let i = 0; i < fullStars; i++) {
            starsHTML += '★';
        }
        
        if (hasHalfStar) {
            starsHTML += '☆';
        }
        
        return starsHTML;
    }
    
    getNoResultsHTML() {
        return `
            <div class="no-results">
                <div class="no-results-icon">
                    <i class="fas fa-search"></i>
                </div>
                <h3>No plumbers found</h3>
                <p>Try adjusting your search criteria or location.</p>
                <button class="btn btn-primary" onclick="plumberSearch.clearFilters()">
                    Clear Filters
                </button>
            </div>
        `;
    }
    
    updatePagination(totalCount) {
        const totalPages = Math.ceil(totalCount / this.itemsPerPage);
        const paginationContainer = document.getElementById('pagination');
        
        if (!paginationContainer || totalPages <= 1) {
            if (paginationContainer) paginationContainer.innerHTML = '';
            return;
        }
        
        let paginationHTML = '';
        
        // Previous button
        if (this.currentPage > 1) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage - 1}">Previous</button>`;
        }
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === this.currentPage) {
                paginationHTML += `<button class="page-btn active" data-page="${i}">${i}</button>`;
            } else if (i === 1 || i === totalPages || Math.abs(i - this.currentPage) <= 2) {
                paginationHTML += `<button class="page-btn" data-page="${i}">${i}</button>`;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += `<span class="page-ellipsis">...</span>`;
            }
        }
        
        // Next button
        if (this.currentPage < totalPages) {
            paginationHTML += `<button class="page-btn" data-page="${this.currentPage + 1}">Next</button>`;
        }
        
        paginationContainer.innerHTML = paginationHTML;
    }
    
    initContactButtons() {
        // Call buttons
        document.querySelectorAll('.btn-call').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const phone = e.target.closest('.btn-call').dataset.phone;
                window.location.href = `tel:${phone}`;
                this.trackEvent('phone_call', { phone });
            });
        });

        // Contact buttons
        document.querySelectorAll('.btn-contact').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const plumberId = e.target.closest('.btn-contact').dataset.plumberId;
                const plumber = this.searchData.find(p => p.id == plumberId);
                if (plumber) {
                    this.showContactModal(plumber);
                }
            });
        });

        // Website buttons
        document.querySelectorAll('.btn-website').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const website = e.target.closest('.btn-website').dataset.website;
                const url = website.startsWith('http') ? website : `https://${website}`;
                window.open(url, '_blank');
                this.trackEvent('website_visit', { website });
            });
        });
    }
    
    showContactModal(plumber) {
        // Implementation would be similar to main.js but with plumber-specific data
        console.log('Show contact modal for:', plumber.name);
    }
    
    clearFilters() {
        this.filters = {
            service: '',
            location: '',
            rating: 0,
            priceRange: [0, 1000],
            availability: '',
            experience: 0,
            verified: false
        };
        
        // Reset form elements
        const form = document.getElementById('filters-form');
        if (form) form.reset();
        
        this.currentPage = 1;
        this.performSearch();
    }
    
    updatePriceDisplay(value) {
        const display = document.getElementById('price-display');
        if (display) {
            display.textContent = `Up to $${value}/hour`;
        }
    }
    
    updateURL() {
        const params = new URLSearchParams();
        
        Object.keys(this.filters).forEach(key => {
            const value = this.filters[key];
            if (value && value !== '' && value !== 0 && value !== false) {
                if (Array.isArray(value)) {
                    params.set(key, value.join(','));
                } else {
                    params.set(key, value.toString());
                }
            }
        });
        
        if (this.sortBy !== 'rating') {
            params.set('sort', this.sortBy);
        }
        
        if (this.currentPage > 1) {
            params.set('page', this.currentPage.toString());
        }
        
        const newURL = `${window.location.pathname}?${params.toString()}`;
        window.history.replaceState({}, '', newURL);
    }
    
    scrollToResults() {
        const resultsContainer = document.getElementById('search-results');
        if (resultsContainer) {
            resultsContainer.scrollIntoView({ behavior: 'smooth', block: 'start' });
        }
    }
    
    trackSearch(resultCount) {
        if (typeof trackEvent === 'function') {
            trackEvent('search_performed', {
                filters: this.filters,
                sort: this.sortBy,
                result_count: resultCount
            });
        }
    }
    
    trackEvent(eventName, properties) {
        if (typeof trackEvent === 'function') {
            trackEvent(eventName, properties);
        }
    }
}

// Initialize search functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('search-results')) {
        window.plumberSearch = new PlumberSearch();
    }
});
