// Image fallback system for PlumberPro
// This script handles missing images and creates placeholder images

document.addEventListener('DOMContentLoaded', function() {
    setupImageFallbacks();
});

function setupImageFallbacks() {
    // Handle all images on the page
    const images = document.querySelectorAll('img');
    
    images.forEach(img => {
        // Add error handler for missing images
        img.addEventListener('error', function() {
            handleImageError(this);
        });
        
        // Check if image is already broken
        if (img.complete && img.naturalHeight === 0) {
            handleImageError(img);
        }
    });
}

function handleImageError(img) {
    const alt = img.alt || 'Plumber';
    const width = img.offsetWidth || 200;
    const height = img.offsetHeight || 150;
    
    // Create a placeholder image using canvas or SVG
    const placeholder = createPlaceholderImage(alt, width, height);
    img.src = placeholder;
    img.classList.add('placeholder-image');
}

function createPlaceholderImage(text, width, height) {
    // Create SVG placeholder
    const svg = `
        <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
            <rect width="100%" height="100%" fill="#f3f4f6"/>
            <rect x="10" y="10" width="${width-20}" height="${height-20}" fill="#e5e7eb" stroke="#d1d5db" stroke-width="2"/>
            <text x="50%" y="40%" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" fill="#6b7280">
                <tspan x="50%" dy="0">🔧</tspan>
                <tspan x="50%" dy="20">${text}</tspan>
                <tspan x="50%" dy="20">Professional</tspan>
            </text>
        </svg>
    `;
    
    return 'data:image/svg+xml;base64,' + btoa(svg);
}

// Update image sources to use placeholder service
function updateImageSources() {
    const imageMap = {
        'ares-plumbing.jpg': 'https://via.placeholder.com/200x150/2563eb/ffffff?text=ARES+Plumbing',
        'metro-flow.jpg': 'https://via.placeholder.com/200x150/2563eb/ffffff?text=Metro-Flow',
        'benjamin-franklin.jpg': 'https://via.placeholder.com/200x150/2563eb/ffffff?text=Benjamin+Franklin',
        'cathedral-plumbing.jpg': 'https://via.placeholder.com/200x150/2563eb/ffffff?text=Cathedral+Plumbing',
        'baker-brothers.jpg': 'https://via.placeholder.com/200x150/2563eb/ffffff?text=Baker+Brothers',
        'tl-plumbing.jpg': 'https://via.placeholder.com/200x150/2563eb/ffffff?text=T%26L+Plumbing',
        'buchheit-plumbing.jpg': 'https://via.placeholder.com/200x150/2563eb/ffffff?text=Buchheit+Plumbing',
        'rodgers-plumbing.jpg': 'https://via.placeholder.com/200x150/2563eb/ffffff?text=Rodgers+Plumbing',
        'hanks-plumbing.jpg': 'https://via.placeholder.com/200x150/2563eb/ffffff?text=Hanks+Plumbing',
        'allpro-plumbing.jpg': 'https://via.placeholder.com/200x150/2563eb/ffffff?text=All+Pro+Plumbing',
        'hero-plumber.jpg': 'https://via.placeholder.com/600x400/2563eb/ffffff?text=Professional+Plumber',
        'pro-dashboard.jpg': 'https://via.placeholder.com/600x400/2563eb/ffffff?text=Professional+Dashboard'
    };
    
    // Update all images that match our naming convention
    document.querySelectorAll('img').forEach(img => {
        const src = img.src;
        const filename = src.split('/').pop();
        
        if (imageMap[filename]) {
            img.src = imageMap[filename];
        }
    });
}

// Call this function after DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Small delay to ensure all elements are rendered
    setTimeout(updateImageSources, 100);
});
