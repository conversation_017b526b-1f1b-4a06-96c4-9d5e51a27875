<?php
// Set error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Define base path
define('BASE_PATH', __DIR__);

// Include the Database class
require_once BASE_PATH . '/src/utils/Database.php';

try {
    // Get database instance
    echo "Getting database instance...\n";
    $db = Database::getInstance();
    echo "Database instance created successfully.\n";
    
    // Test a simple query
    echo "Testing a simple query...\n";
    $result = $db->query("SHOW TABLES");
    
    if ($result) {
        echo "Query executed successfully.\n";
        echo "Tables in database:\n";
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            echo "- " . reset($row) . "\n";
        }
    } else {
        echo "Query failed.\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
