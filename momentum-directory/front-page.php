<?php
/**
 * The template for displaying the front page
 *
 * @package Momentum_Directory
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

global $directoryGoogleCat, $directoryCountry, $customTaxonomyKey, $customPostKey;

get_header();
?>

<div id="primary" class="content-area">
    <main id="main" class="site-main">
        
        <!-- Hero Section -->
        <section class="hero-section" style="background-image: url('<?php echo esc_url(get_template_directory_uri() . '/assets/images/hero-section.png'); ?>');">
            <div class="container">
                <div class="hero-content">
                    <h1><?php echo esc_html(get_bloginfo('name')); ?></h1>
                    <p><?php echo esc_html(get_bloginfo('description')); ?></p>
                    
                    <div class="search-form">
                        <form role="search" method="get" action="<?php echo esc_url(home_url('/')); ?>">
                            <input type="hidden" name="post_type" value="<?php echo esc_attr($customPostKey); ?>">
                            <div class="search-input-group">
                                <div class="search-input">
                                    <i class="fas fa-search"></i>
                                    <input type="text" name="s" placeholder="<?php esc_attr_e('Search for businesses...', 'momentum-directory'); ?>">
                                </div>
                                <div class="search-input">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <input type="text" name="location" placeholder="<?php esc_attr_e('Location', 'momentum-directory'); ?>">
                                </div>
                                <button type="submit" class="btn btn-primary"><?php esc_html_e('Search', 'momentum-directory'); ?></button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Search by City Section -->
        <section class="search-by-city-section section-padding">
            <div class="container">
                <div class="section-header">
                    <h3><?php echo esc_html__('Search', 'momentum-directory') . ' ' . esc_html($directoryGoogleCat) . ' ' . esc_html__('by City', 'momentum-directory'); ?></h3>
                    <p><?php esc_html_e('Find the best businesses in your area', 'momentum-directory'); ?></p>
                </div>
                
                <div class="search-dropdowns">
                    <div class="row">
                        <div class="col col-md-5">
                            <div class="form-group">
                                <label for="first-level-taxonomy"><?php esc_html_e('Select State', 'momentum-directory'); ?></label>
                                <select id="first-level-taxonomy" class="form-control">
                                    <option value=""><?php esc_html_e('Select State', 'momentum-directory'); ?></option>
                                    <?php
                                    $parent_terms = get_terms(array(
                                        'taxonomy' => $customTaxonomyKey,
                                        'hide_empty' => false,
                                        'parent' => 0,
                                    ));
                                    
                                    if (!empty($parent_terms) && !is_wp_error($parent_terms)) {
                                        foreach ($parent_terms as $term) {
                                            echo '<option value="' . esc_attr($term->term_id) . '">' . esc_html($term->name) . '</option>';
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col col-md-5">
                            <div class="form-group">
                                <label for="child-taxonomy"><?php esc_html_e('Select City', 'momentum-directory'); ?></label>
                                <select id="child-taxonomy" class="form-control" disabled>
                                    <option value=""><?php esc_html_e('Select City', 'momentum-directory'); ?></option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="col col-md-2">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <button id="search-button" class="btn btn-primary btn-block"><?php esc_html_e('Go', 'momentum-directory'); ?></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Browse by State Section -->
        <section class="browse-by-state-section section-padding bg-light">
            <div class="container">
                <div class="section-header">
                    <h3><?php echo esc_html__('Browse', 'momentum-directory') . ' ' . esc_html($directoryGoogleCat) . ' ' . esc_html__('by State', 'momentum-directory'); ?></h3>
                    <p><?php esc_html_e('Explore businesses across different states', 'momentum-directory'); ?></p>
                </div>
                
                <div class="row">
                    <?php
                    $parent_terms = get_terms(array(
                        'taxonomy' => $customTaxonomyKey,
                        'hide_empty' => false,
                        'parent' => 0,
                    ));
                    
                    if (!empty($parent_terms) && !is_wp_error($parent_terms)) {
                        foreach ($parent_terms as $term) {
                            $term_link = get_term_link($term);
                            $term_count = momentum_get_term_post_count($term->term_id, $customTaxonomyKey, $customPostKey);
                            ?>
                            <div class="col col-md-4 col-sm-6">
                                <div class="card location-card">
                                    <?php
                                    $term_image = get_term_meta($term->term_id, 'taxonomy_image', true);
                                    if ($term_image) {
                                        echo '<img src="' . esc_url($term_image) . '" alt="' . esc_attr($term->name) . '" class="card-img">';
                                    } else {
                                        echo '<div class="card-img-placeholder"><i class="fas fa-map-marked-alt"></i></div>';
                                    }
                                    ?>
                                    <div class="card-content">
                                        <h4><a href="<?php echo esc_url($term_link); ?>"><?php echo esc_html($term->name); ?></a></h4>
                                        <?php if (!empty($term->description)) : ?>
                                            <p><?php echo esc_html(wp_trim_words($term->description, 15)); ?></p>
                                        <?php endif; ?>
                                        <span class="listing-count"><?php echo esc_html($term_count) . ' ' . esc_html(_n('Listing', 'Listings', $term_count, 'momentum-directory')); ?></span>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                    } else {
                        echo '<div class="col col-12"><p class="no-results">' . esc_html__('No states found. Please add some locations to get started.', 'momentum-directory') . '</p></div>';
                    }
                    ?>
                </div>
            </div>
        </section>
        
        <!-- Popular Businesses Section -->
        <section class="popular-businesses-section section-padding">
            <div class="container">
                <div class="section-header">
                    <h3><?php echo esc_html__('Popular', 'momentum-directory') . ' ' . esc_html($directoryGoogleCat) . ' ' . esc_html__('around', 'momentum-directory') . ' ' . esc_html($directoryCountry); ?></h3>
                    <p><?php esc_html_e('Discover top-rated businesses in your country', 'momentum-directory'); ?></p>
                </div>
                
                <div class="row">
                    <?php
                    $popular_listings = new WP_Query(array(
                        'post_type' => $customPostKey,
                        'posts_per_page' => 25,
                        'orderby' => 'rand',
                    ));
                    
                    if ($popular_listings->have_posts()) {
                        while ($popular_listings->have_posts()) {
                            $popular_listings->the_post();
                            
                            // Get ACF fields
                            $address = get_field('address');
                            $rating_json = get_field('rating');
                            $rating = null;
                            
                            if ($rating_json) {
                                $rating_data = json_decode($rating_json, true);
                                if (isset($rating_data['value'])) {
                                    $rating = $rating_data['value'];
                                }
                            }
                            ?>
                            <div class="col col-md-4 col-sm-6">
                                <div class="card business-card">
                                    <a href="<?php the_permalink(); ?>" class="card-img-link">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <?php the_post_thumbnail('medium', array('class' => 'card-img')); ?>
                                        <?php else : ?>
                                            <img src="https://placehold.co/600x400?text=<?php echo esc_attr(get_bloginfo('name')); ?>" alt="<?php the_title_attribute(); ?>" class="card-img">
                                        <?php endif; ?>
                                    </a>
                                    <div class="card-content">
                                        <h4 class="card-title"><a href="<?php the_permalink(); ?>"><?php the_title(); ?></a></h4>
                                        
                                        <?php if ($rating) : ?>
                                            <div class="rating">
                                                <?php
                                                for ($i = 1; $i <= 5; $i++) {
                                                    if ($i <= $rating) {
                                                        echo '<i class="fas fa-star"></i>';
                                                    } elseif ($i - 0.5 <= $rating) {
                                                        echo '<i class="fas fa-star-half-alt"></i>';
                                                    } else {
                                                        echo '<i class="far fa-star"></i>';
                                                    }
                                                }
                                                ?>
                                                <span class="rating-value"><?php echo esc_html(number_format($rating, 1)); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ($address) : ?>
                                            <div class="address">
                                                <i class="fas fa-map-marker-alt"></i>
                                                <span><?php echo esc_html($address); ?></span>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <a href="<?php the_permalink(); ?>" class="btn btn-outline btn-sm"><?php esc_html_e('View Details', 'momentum-directory'); ?></a>
                                    </div>
                                </div>
                            </div>
                            <?php
                        }
                        wp_reset_postdata();
                    } else {
                        echo '<div class="col col-12"><p class="no-results">' . esc_html__('No listings found. Please add some businesses to get started.', 'momentum-directory') . '</p></div>';
                    }
                    ?>
                </div>
            </div>
        </section>
        
        <!-- Call to Action Section -->
        <section class="cta-section">
            <div class="container">
                <div class="cta-content">
                    <h2><?php esc_html_e('Add Your Business to Our Directory', 'momentum-directory'); ?></h2>
                    <p><?php esc_html_e('Join thousands of businesses and reach more customers today!', 'momentum-directory'); ?></p>
                    <a href="#" class="btn btn-primary btn-lg"><?php esc_html_e('Add Your Listing', 'momentum-directory'); ?></a>
                </div>
            </div>
        </section>
        
    </main><!-- #main -->
</div><!-- #primary -->

<script>
jQuery(document).ready(function($) {
    $('#first-level-taxonomy').on('change', function() {
        var parent_id = $(this).val();
        var $child_dropdown = $('#child-taxonomy');
        $child_dropdown.empty().append('<option value=""><?php echo esc_js(__('Select City', 'momentum-directory')); ?></option>');

        if (parent_id) {
            $.ajax({
                url: momentum_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'get_child_taxonomies',
                    nonce: momentum_ajax.nonce,
                    parent_id: parent_id,
                    taxonomy: '<?php echo esc_js($customTaxonomyKey); ?>'
                },
                success: function(response) {
                    if (response && response.length > 0) {
                        $.each(response, function(index, term) {
                            $child_dropdown.append('<option value="' + term.link + '">' + term.name + '</option>');
                        });
                        $child_dropdown.prop('disabled', false);
                    } else {
                        $child_dropdown.prop('disabled', true);
                    }
                },
                error: function(xhr, status, error) {
                    $child_dropdown.prop('disabled', true);
                    console.error('AJAX error:', status, error);
                }
            });
        } else {
            $child_dropdown.prop('disabled', true);
        }
    });

    $('#child-taxonomy').on('change', function() {
        var term_link = $(this).val();
        if (term_link) {
            $('#search-button').on('click', function() {
                window.location.href = term_link;
            });
        }
    });
    
    // Direct navigation when clicking the Go button
    $('#search-button').on('click', function() {
        var term_link = $('#child-taxonomy').val();
        if (term_link) {
            window.location.href = term_link;
        }
    });
});
</script>

<?php
get_footer();
