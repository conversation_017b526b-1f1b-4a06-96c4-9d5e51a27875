<?php
// Include necessary files
require_once __DIR__ . '/src/utils/Database.php';

// Get database instance
$db = Database::getInstance();

// Query cognitive distortions
$sql = "SELECT * FROM cognitive_distortions ORDER BY name ASC";
$distortions = $db->fetchAll($sql);

// Display results
echo "<h1>Cognitive Distortions Test</h1>";

if ($distortions) {
    echo "<p>Found " . count($distortions) . " cognitive distortions:</p>";
    echo "<ul>";
    foreach ($distortions as $distortion) {
        echo "<li><strong>" . htmlspecialchars($distortion['name']) . "</strong>: " . htmlspecialchars($distortion['description']) . "</li>";
    }
    echo "</ul>";
} else {
    echo "<p>No cognitive distortions found or error accessing the database.</p>";
}
